import { writable } from 'svelte/store';
import type { FixtureType, ShowFixtureInstance } from '../types/bindings/FixtureType';
import { get } from 'svelte/store';
import { networking } from './networking';
import { TOAST } from './toast';

function createFixtureTypesStore() {
    const { subscribe, set, update } = writable<FixtureType[]>([]);

    return {
        subscribe,
        set,
        updateLocal: update,
        refresh: async () => await refreshFixtureTypes(),
        create: async (fixtureType: FixtureType) => await createFixtureType(fixtureType),
        update: async (fixtureType: FixtureType) => await updateFixtureType(fixtureType),
        delete: async (id: number) => await deleteFixtureType(id),
        createFromFixture: async (fixtureId: number, newName: string) => await createFixtureTypeFromFixture(fixtureId, newName)
    };
}

function createShowFixtureInstancesStore() {
    const { subscribe, set, update } = writable<ShowFixtureInstance[]>([]);

    return {
        subscribe,
        set,
        updateLocal: update,
        refresh: async () => await refreshShowFixtureInstances(),
        create: async (instance: ShowFixtureInstance) => await createShowFixtureInstance(instance)
    };
}

export const fixtureTypes = createFixtureTypesStore();
export const showFixtureInstances = createShowFixtureInstancesStore();

async function refreshFixtureTypes(): Promise<void> {
    try {
        const response = await fetch(`http://${get(networking)}:${networking.port}/fixture-types`);
        if (response.ok) {
            const types: FixtureType[] = await response.json();
            fixtureTypes.set(types);
        } else {
            TOAST.error('Failed to load fixture types');
        }
    } catch (error) {
        TOAST.error('Error loading fixture types: ' + error);
    }
}

async function createFixtureType(fixtureType: FixtureType): Promise<void> {
    try {
        const response = await fetch(`http://${get(networking)}:${networking.port}/fixture-type`, {
            method: 'POST',
            body: JSON.stringify(fixtureType),
            headers: new Headers({ 'content-type': 'application/json' }),
        });
        
        if (response.ok) {
            await refreshFixtureTypes();
            TOAST.success('Fixture type created successfully');
        } else {
            TOAST.error('Failed to create fixture type');
        }
    } catch (error) {
        TOAST.error('Error creating fixture type: ' + error);
    }
}

async function updateFixtureType(fixtureType: FixtureType): Promise<void> {
    try {
        const response = await fetch(`http://${get(networking)}:${networking.port}/fixture-type`, {
            method: 'PATCH',
            body: JSON.stringify(fixtureType),
            headers: new Headers({ 'content-type': 'application/json' }),
        });
        
        if (response.ok) {
            await refreshFixtureTypes();
            TOAST.success('Fixture type updated successfully');
        } else {
            TOAST.error('Failed to update fixture type');
        }
    } catch (error) {
        TOAST.error('Error updating fixture type: ' + error);
    }
}

async function deleteFixtureType(id: number): Promise<void> {
    try {
        const response = await fetch(`http://${get(networking)}:${networking.port}/fixture-type/${id}`, {
            method: 'DELETE'
        });
        
        if (response.ok) {
            await refreshFixtureTypes();
            TOAST.success('Fixture type deleted successfully');
        } else {
            TOAST.error('Failed to delete fixture type');
        }
    } catch (error) {
        TOAST.error('Error deleting fixture type: ' + error);
    }
}

async function createFixtureTypeFromFixture(fixtureId: number, newName: string): Promise<void> {
    try {
        const response = await fetch(`http://${get(networking)}:${networking.port}/fixture-type/from-fixture/${fixtureId}`, {
            method: 'POST',
            body: JSON.stringify(newName),
            headers: new Headers({ 'content-type': 'application/json' }),
        });
        
        if (response.ok) {
            await refreshFixtureTypes();
            TOAST.success('Fixture type created from fixture successfully');
        } else {
            TOAST.error('Failed to create fixture type from fixture');
        }
    } catch (error) {
        TOAST.error('Error creating fixture type from fixture: ' + error);
    }
}

async function refreshShowFixtureInstances(): Promise<void> {
    try {
        const response = await fetch(`http://${get(networking)}:${networking.port}/show-fixture-instances`);
        if (response.ok) {
            const instances: ShowFixtureInstance[] = await response.json();
            showFixtureInstances.set(instances);
        } else {
            TOAST.error('Failed to load show fixture instances');
        }
    } catch (error) {
        TOAST.error('Error loading show fixture instances: ' + error);
    }
}

async function createShowFixtureInstance(instance: ShowFixtureInstance): Promise<void> {
    try {
        const response = await fetch(`http://${get(networking)}:${networking.port}/show-fixture-instance`, {
            method: 'POST',
            body: JSON.stringify(instance),
            headers: new Headers({ 'content-type': 'application/json' }),
        });
        
        if (response.ok) {
            await refreshShowFixtureInstances();
            TOAST.success('Fixtures created successfully');
        } else {
            TOAST.error('Failed to create fixture instances');
        }
    } catch (error) {
        TOAST.error('Error creating fixture instances: ' + error);
    }
}