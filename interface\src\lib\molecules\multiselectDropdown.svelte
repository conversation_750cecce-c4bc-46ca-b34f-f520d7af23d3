<script lang="ts" generics="T extends { id: number | null, name: string }">
    import Textinput from "$lib/atoms/textinput.svelte";
    import Icon from "$lib/atoms/icon.svelte";
    import Button from "$lib/atoms/button.svelte";
    import ClickOutside from "svelte-click-outside";
    import type { Snippet } from "svelte";
    import { teleportToBody } from "$lib/useActions/teleport.svelte";

    let isOpen = $state(false);
    let buttonElement: HTMLDivElement | undefined = $state();

    let {
        items,
        checkedIds = $bindable(),
        disabled = false,
        onchange,
        button,
    }: {
        items: T[];
        checkedIds: number[];
        disabled?: boolean;
        onchange?: (checkedIds: number[]) => any;
        button?: Snippet;
    } = $props();

    let filteredItems = $derived(
        items.filter((item) =>
            item.name.toLowerCase().includes(textSearchValue.toLowerCase()),
        ),
    );

    let hoveringButtonWrapper = $state(false);

    let maxItemLength = $derived.by(() => {
        if (items.length > 0) {
            return items
                .map((item) => item.name)
                .sort((a, b) => a.length - b.length)
                .slice(-1)[0].length;
        } else {
            return 1;
        }
    });

    let formerCheckedIdsLength = 0;
    $effect(() => {
        if (formerCheckedIdsLength !== checkedIds.length && onchange) {
            formerCheckedIdsLength = checkedIds.length;
            onchange(checkedIds);
        }
    });

    let textSearchValue = $state("");
</script>

<!-- TODO: fix ClickOutside; maybe diy? -->
<!-- <ClickOutside onClickOutside={() => (isOpen = false)}> -->
<div class="flex items-center justify-center">
    <div
        class="-mt-1"
        bind:this={buttonElement}
        onmouseenter={() => {
            hoveringButtonWrapper = true;
        }}
        onmouseleave={() => {
            hoveringButtonWrapper = false;
        }}
        role="cell"
        tabindex={1}
    >
        <Button
            id="multiselect-dropdown-toggle"
            small
            onclick={() => {
                if (!disabled) {
                    hoveringButtonWrapper = false;
                    isOpen = !isOpen;
                }
            }}
        >
            {#if button}
                {@render button()}
            {:else}
                <div class="flex" class:text-disabled={disabled}>
                    <div class="ml-auto mt-1">
                        <div
                            class="transition"
                            class:rotate-90={!isOpen}
                            class:-rotate-90={isOpen}
                        >
                            <Icon icon="weui:arrow-filled"></Icon>
                        </div>
                    </div>
                </div>
            {/if}
        </Button>
    </div>
    {#if isOpen}
        <div
            id="dropdown-menu"
            class="absolute z-10 mt-2 space-y-1 rounded-md bg-input p-2 shadow-lg ring-1 ring-black ring-opacity-5 text-primary group"
            use:teleportToBody
            style={`
                   left: ${buttonElement.getBoundingClientRect().x}px;
                   top: ${buttonElement.getBoundingClientRect().y + 10}px;
                   max-width: ${maxItemLength * 25}px;
                `}
        >
            <Textinput bind:value={textSearchValue} placeholder="search"
            ></Textinput>
            <div class="max-h-32 overflow-y-scroll p-1">
                {#each filteredItems as item}
                    <button
                        class="w-full rounded-md p-1 transition-colors hover:text-complementary"
                        class:hover:bg-accent={checkedIds.find(
                            (checkedId) => checkedId === item.id,
                        ) === undefined}
                        class:bg-accent={checkedIds.find(
                            (checkedId) => checkedId === item.id,
                        ) !== undefined}
                        class:bg-opacity-50={checkedIds.find(
                            (checkedId) => checkedId === item.id,
                        ) !== undefined}
                        class:hover:bg-opacity-25={checkedIds.find(
                            (checkedId) => checkedId === item.id,
                        ) !== undefined}
                        onclick={() => {
                            let checkedId = checkedIds.find(
                                (checkedId) => checkedId === item.id,
                            );
                            if (checkedId !== undefined) {
                                checkedIds = checkedIds.filter(
                                    (checkedId) => checkedId !== item.id,
                                );
                            } else {
                                checkedIds = [...checkedIds, item.id ?? 0];
                            }
                        }}
                    >
                        {item.name}
                    </button>
                {/each}
            </div>
        </div>
    {:else if hoveringButtonWrapper}
        <div
            class="absolute rounded-md bg-input p-2 shadow-lg text-disabled group"
            use:teleportToBody
            style={buttonElement !== undefined
                ? `
                            left: ${buttonElement.getBoundingClientRect().x}px;
                    top: ${buttonElement.getBoundingClientRect().y + 35}px;
                         max-width: ${maxItemLength * 25}px;
                         `
                      .replaceAll(" ", "")
                      .replaceAll("\n", "")
                : ""}
        >
            {#each checkedIds as id}
                <p>
                    {items.find((item) => item.id === id)?.name ?? ""}
                </p>
            {/each}
        </div>
    {/if}
</div>
<!-- </ClickOutside> -->
