import { get, writable } from 'svelte/store';
import type { DmxFixtureLibraryDescriptor } from '../types/bindings/DmxFixtureLibraryDescriptor';
import { networking } from './networking';

function createLibraryFixturesStore() {
    const { subscribe, set } = writable<DmxFixtureLibraryDescriptor[]>([]);

    fetchFixtures().then(data => set(data));

    return {
        subscribe,
    };
}

export const libraryFixtures = createLibraryFixturesStore();

async function fetchFixtures(): Promise<DmxFixtureLibraryDescriptor[]> {
    return new Promise(async (resolve, _) => {
        const ip = get(networking);
        if (ip) {
            const fixtureResponse = await fetch(
                `http://${get(networking)}:${networking.port}/library/fixtures`,
            );
            resolve(fixtureResponse.json());
        } else {
            setTimeout(async () => resolve(await fetchFixtures()), 1000)
        }
    })
}
