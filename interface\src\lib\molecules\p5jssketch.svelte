<script lang="ts">
	import { onMount, createEventDispatcher, onDestroy } from 'svelte';
	import type p5 from 'p5';

	type Sketch = (sketch: p5) => void;

	let { sketch }: { sketch: Sketch } = $props();
	let target: HTMLElement | undefined = $state();

	let project: p5 | undefined = $state();

	const event = createEventDispatcher();
	const dispatch = {
		ref() {
			event('ref', target);
		},
		instance() {
			event('instance', project);
		},
	};

	function ref(node: HTMLElement) {
		target = node;
		return {
			destroy() {
				target = undefined;
			},
		};
	}

	function augmentClasses<
		NativeClasses extends [string, Record<string, any>][],
	>(instance: p5, classes: NativeClasses) {
		// @ts-ignore
		classes.forEach(([key, value]) => (instance[key] = value));
		return instance;
	}

	onMount(async () => {
		const library = await import('p5');
		const { default: p5 } = library;

		const entries = Object.entries(library);
		const nativeClasses = entries.filter(
			([key, value]) =>
				value instanceof Function &&
				key[0] !== '_' &&
				key !== 'default',
		);

		project = new p5((instance: p5) => {
			instance = augmentClasses(instance, nativeClasses);

			// Set up a global object to capture this instance.
			// @ts-ignore
			window._p5Instance = instance;
			if (sketch) {
				return sketch(instance);
			} else {
				console.error('could not load p5 sketch');
			}
		}, target);

		// Initial event dispatching
		dispatch.ref();
		dispatch.instance();
	});

	onDestroy(() => {
		project?.remove();
	});
</script>

<!-- style={parentDivStyle} -->
<div use:ref class="m-0" ></div>
