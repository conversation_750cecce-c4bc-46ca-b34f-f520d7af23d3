use crate::dmx_renderer::fixture::DmxFixtureLibraryDescriptor;

use super::{
    fixtures::{
        saturate_color_channels_cmy_for_fixture,
        saturate_color_channels_hsv_for_fixture,
        saturate_color_channels_rgb_for_fixture,
        saturate_color_channels_xyz_for_fixture,
        saturate_color_wheel_for_fixture, saturate_dmx_channels_for_fixture,
        shallow_fixtures_for_active_show,
    },
    DbHandler,
};

impl DbHandler {
    #[must_use]
    pub fn library_fixtures(&mut self) -> Vec<DmxFixtureLibraryDescriptor> {
        // TODO: Merge this with GDTF-share

        let mut fixtures =
            shallow_fixtures_for_active_show(&mut self.db_connection());

        fixtures.sort_by_key(|fx| fx.fixturetype.clone());
        fixtures.dedup_by(|a, b| a.fixturetype == b.fixturetype);
        for fixture in &mut fixtures {
            if let Some(id) = fixture.id {
                saturate_dmx_channels_for_fixture(
                    &mut self.db_connection(),
                    id,
                    fixture,
                );

                saturate_color_channels_rgb_for_fixture(
                    &mut self.db_connection(),
                    id,
                    fixture,
                );

                saturate_color_channels_cmy_for_fixture(
                    &mut self.db_connection(),
                    id,
                    fixture,
                );

                saturate_color_channels_hsv_for_fixture(
                    &mut self.db_connection(),
                    id,
                    fixture,
                );

                saturate_color_channels_xyz_for_fixture(
                    &mut self.db_connection(),
                    id,
                    fixture,
                );

                saturate_color_wheel_for_fixture(
                    &mut self.db_connection(),
                    id,
                    fixture,
                );
            }
        }
        fixtures
            .iter()
            .map(|fixture| DmxFixtureLibraryDescriptor {
                fixturetype: fixture.fixturetype.clone(),
                movement_channels: fixture.movement_channels.clone(),
                footprint_size: fixture.footprint_size,
                color: fixture.color.clone(),
                unimplemented_channels: fixture.unimplemented_channels.clone(),
            })
            .collect()
    }
}
