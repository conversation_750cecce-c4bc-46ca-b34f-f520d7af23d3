<script lang="ts" module>
    export const navigationAllowed = writable(true);
    declare const __APP_VERSION__: string;
</script>

<script lang="ts">
    import { networking } from "$lib/stores/networking";
    import { writable } from "svelte/store";
    import { goto } from "$app/navigation";
    import { shows } from "$lib/stores/shows";
    import Icon from "$lib/atoms/icon.svelte";
    import { page } from "$app/stores";
    import { allSnippetsOf, snippetDir } from "$lib/stores/snippets";
    import { blueprints } from "$lib/stores/blueprints";
    import { theme } from "$lib/stores/theme";
    import RequiresUseractionHint from "$lib/atoms/RequiresUseractionHint.svelte";
    import { isLightColor, getThemeTextColors } from "$lib/utils";

    const routes = [
        { route: "dashboard", name: "Dashboard" },
        { route: "createFixture", name: "Create Fixture" },
        { route: "stageCoordinates", name: "Stage Coordinates" },
        // TODO: reenable timecode editor
        // { route: "timecodeEditor", name: "Timecode Editor" },
        { route: "blueprintBuilder", name: "Blueprint Builder" },
        { route: "codespace", name: "Codespace" },
        { route: "onlineInterface", name: "Online Console" },
        { route: "shows", name: "Shows" },
        { route: "networking", name: "Networking" },
        { route: "dmxWatcher", name: "DMX Watcher" },
    ];

    function handleNavigation(route: string) {
        if ($navigationAllowed) {
            goto(route);
        } else if (
            confirm(
                "Warning: You have unchanged changes for the current snippet. Still proceed?",
            )
        ) {
            goto(route);
        }
    }

    function stringToColor(str: string) {
        function hashString(str: string) {
            let hash = 0;
            for (let i = 0; i < str.length; i++) {
                hash = str.charCodeAt(i) + ((hash << 5) - hash);
                hash = hash & hash;
            }
            return hash;
        }

        function intToRGB(i: number) {
            const c = (i & 0x00ffffff).toString(16).toUpperCase();
            return "00000".substring(0, 6 - c.length) + c;
        }

        if (str) {
            const hash = hashString(str);
            const color = intToRGB(hash);
            return `#${color}`;
        } else {
            return "#FF0000";
        }
    }

    let activeShowName = $derived($shows.find((show) => show[2])?.[1]);

    let filteredRoutes = $derived.by(() => {
        if (!$networking) {
            return routes.filter((route) => route.route === "networking");
        } else if (activeShowName) {
            return routes;
        } else {
            return routes.filter(
                (route) =>
                    route.route === "shows" || route.route === "networking",
            );
        }
    });

    let showNameBannerText = $derived(
        activeShowName ? activeShowName : "No show selected",
    );

    let showNameBatchBackgroundColor = $derived(
        stringToColor($shows.find((show) => show[2])?.[1] ?? ""),
    );

    let showNameTextColor = $derived(() => {
        const themeColors = getThemeTextColors($theme as string);
        return isLightColor(showNameBatchBackgroundColor)
            ? themeColors.primary
            : themeColors.complementary;
    });
</script>

<div class="flex bg-surface">
    <div class="flex flex-row w-full">
        <div class="flex mr-auto">
            {#each filteredRoutes as route}
                <RequiresUseractionHint
                    reason={route.route === "blueprintBuilder" &&
                    !$page.url.pathname.includes("blueprintBuilder")
                        ? $blueprints
                              .filter(
                                  (blueprint) =>
                                      blueprint.requires_user_action_reason,
                              )
                              .map(
                                  (blueprint) =>
                                      `${blueprint.name}: ${blueprint.requires_user_action_reason}`,
                              )
                              .join(", ")
                        : route.route === "codespace" &&
                            !$page.url.pathname.includes("codespace")
                          ? allSnippetsOf($snippetDir)
                                .filter(
                                    (blueprint) =>
                                        blueprint.requires_user_action_reason,
                                )
                                .map(
                                    (snippet) =>
                                        `${snippet.name}: ${snippet.requires_user_action_reason}`,
                                )
                                .join(", ")
                          : null}
                >
                    <button
                        class="h-navbar ml-4 flex flex-col justify-center px-2 transition hover:bg-accent hover:text-black
                    {$page.url.pathname.includes(route.route)
                            ? 'bg-accent text-complementary'
                            : ''}"
                        onclick={() => handleNavigation(route.route)}
                    >
                        {route.name}
                    </button>
                </RequiresUseractionHint>
            {/each}
        </div>
        {#if $networking}
            <div
                class="mt-1.5 px-2 flex h-[80%] max-w-[25%] hover:max-w-none w-fit flex-col justify-center rounded-full text-center transition-all duration-200"
                style="background-color: {showNameBatchBackgroundColor};"
            >
                <div class="flex justify-center min-w-20 w-20 hover:w-fit">
                    <p
                        class="font-bold truncate hover:whitespace-normal hover:break-words"
                        style="color: {showNameTextColor()};"
                    >
                        {showNameBannerText}
                    </p>
                </div>
            </div>
        {/if}
        <div class="text-primary flex flex-col justify-center mx-2">
            <div class="rounded-full bg-accent h-[80%] px-2">
                <p class="h-full flex flex-col justify-center">
                    {__APP_VERSION__}
                </p>
            </div>
        </div>
        <div class="flex justify-end">
            <button
                class="flex h-16 flex-col justify-center bg-object px-4 text-5xl"
                onclick={() => theme.cycle()}
            >
                <Icon icon="mdi:theme-light-dark"></Icon>
            </button>
        </div>
    </div>
</div>
