#![no_std]
#![no_main]
#![allow(incomplete_features)]
#![feature(generic_const_exprs)]

use arduino_hal::{adc::AdcSettings, hal::usart::Event};
use avr_device::interrupt;
use millis::{millis, millis_init};
use my_rust_utils::clamp_cast::ClampCast;
use ws2812_avr::{GRB, WS2812};

use rwm_communication::{
    sendable_bytes_queue::SendableBytesQueueHead, ACKNOWLEDGED, HEAP, HEAP_MEM, HEAP_SIZE,
    IDENTIFIED,
};
use rwm_package_definitions::{RawInput, RwModuleUsbPackage, HEAD_1, HEAD_2, HEAD_3};

const PAUSE_BETWEEN_SENDS_MS: u32 = 25;

const MAX_FADER_QUEUE: u8 = 3;

const LED_COUNT: usize = 116;

// 1 = FULL, 2 = HALF, 4 = QUATER, ...
#[cfg(not(debug_assertions))]
const COLOR_DIMM_SCALE: u8 = 32;
#[cfg(debug_assertions)]
const COLOR_DIMM_SCALE: u8 = 64;

#[arduino_hal::entry]
fn main() -> ! {
    unsafe { HEAP.init(HEAP_MEM.as_ptr() as usize, HEAP_SIZE) }

    #[allow(clippy::unwrap_used)]
    let dp = arduino_hal::Peripherals::take().unwrap();
    let pins = arduino_hal::pins!(dp);

    let mut physical_leds = WS2812::new(pins.a12.into_output());
    let mut led_buf: [GRB; LED_COUNT] = [GRB::default(); LED_COUNT];

    led_buf[0] = GRB { r: 51, g: 255, b: 255 };
    led_buf[1] = GRB { r: 51, g: 255, b: 255 };
    led_buf[2] = GRB { r: 51, g: 255, b: 255 };
    led_buf[3] = GRB { r: 51, g: 51, b: 255 };
    led_buf[4] = GRB { r: 51, g: 51, b: 255 };
    led_buf[5] = GRB { r: 51, g: 51, b: 255 };
    led_buf[6] = GRB { r: 51, g: 255, b: 255 };
    led_buf[7] = GRB { r: 51, g: 255, b: 255 };
    led_buf[8] = GRB { r: 51, g: 255, b: 255 };
    led_buf[9] = GRB { r: 51, g: 51, b: 255 };
    led_buf[10] = GRB { r: 51, g: 51, b: 255 };
    led_buf[11] = GRB { r: 51, g: 51, b: 255 };
    led_buf[12] = GRB { r: 153, g: 51, b: 255 };
    led_buf[13] = GRB { r: 153, g: 51, b: 255 };
    led_buf[14] = GRB { r: 153, g: 51, b: 255 };
    led_buf[15] = GRB { r: 255, g: 255, b: 255 };
    led_buf[16] = GRB { r: 255, g: 255, b: 255 };
    led_buf[17] = GRB { r: 255, g: 255, b: 255 };
    led_buf[18] = GRB { r: 153, g: 51, b: 255 };
    led_buf[19] = GRB { r: 153, g: 51, b: 255 };
    led_buf[20] = GRB { r: 153, g: 51, b: 255 };
    led_buf[21] = GRB { r: 255, g: 255, b: 255 };
    led_buf[22] = GRB { r: 255, g: 255, b: 255 };
    led_buf[23] = GRB { r: 255, g: 255, b: 255 };
    led_buf[24] = GRB { r: 51, g: 51, b: 255 };
    led_buf[25] = GRB { r: 51, g: 51, b: 255 };
    led_buf[26] = GRB { r: 255, g: 255, b: 255 };
    led_buf[27] = GRB { r: 51, g: 51, b: 255 };
    led_buf[28] = GRB { r: 255, g: 255, b: 255 };
    led_buf[29] = GRB { r: 51, g: 51, b: 255 };
    led_buf[30] = GRB { r: 43, g: 192, b: 255 };
    led_buf[31] = GRB { r: 43, g: 192, b: 255 };
    led_buf[32] = GRB { r: 43, g: 192, b: 255 };
    led_buf[33] = GRB { r: 255, g: 255, b: 255 };
    led_buf[34] = GRB { r: 43, g: 192, b: 255 };
    led_buf[35] = GRB { r: 43, g: 192, b: 255 };
    led_buf[36] = GRB { r: 51, g: 255, b: 255 };
    led_buf[37] = GRB { r: 51, g: 255, b: 255 };
    led_buf[38] = GRB { r: 255, g: 255, b: 255 };
    led_buf[39] = GRB { r: 51, g: 255, b: 255 };
    led_buf[40] = GRB { r: 255, g: 255, b: 255 };
    led_buf[41] = GRB { r: 51, g: 255, b: 255 };
    led_buf[42] = GRB { r: 255, g: 255, b: 255 };
    led_buf[43] = GRB { r: 255, g: 255, b: 255 };
    led_buf[44] = GRB { r: 51, g: 51, b: 255 };
    led_buf[45] = GRB { r: 255, g: 255, b: 255 };
    led_buf[46] = GRB { r: 255, g: 255, b: 255 };
    led_buf[47] = GRB { r: 51, g: 51, b: 255 };
    led_buf[48] = GRB { r: 255, g: 255, b: 255 };
    led_buf[49] = GRB { r: 255, g: 255, b: 255 };
    led_buf[50] = GRB { r: 255, g: 255, b: 0 };
    led_buf[51] = GRB { r: 207, g: 255, b: 13 };
    led_buf[52] = GRB { r: 255, g: 208, b: 20 };
    led_buf[53] = GRB { r: 51, g: 255, b: 51 };
    led_buf[54] = GRB { r: 255, g: 153, b: 51 };
    led_buf[55] = GRB { r: 51, g: 255, b: 255 };
    led_buf[56] = GRB { r: 255, g: 105, b: 31 };
    led_buf[57] = GRB { r: 43, g: 192, b: 255 };
    led_buf[58] = GRB { r: 255, g: 51, b: 51 };
    led_buf[59] = GRB { r: 51, g: 51, b: 255 };
    led_buf[60] = GRB { r: 255, g: 51, b: 255 };
    led_buf[61] = GRB { r: 153, g: 51, b: 255 };
    led_buf[62] = GRB { r: 0, g: 0, b: 0 };
    led_buf[63] = GRB { r: 0, g: 0, b: 0 };
    led_buf[64] = GRB { r: 0, g: 0, b: 0 };
    led_buf[65] = GRB { r: 0, g: 0, b: 0 };
    led_buf[66] = GRB { r: 51, g: 255, b: 255 };
    led_buf[67] = GRB { r: 43, g: 192, b: 255 };
    led_buf[68] = GRB { r: 51, g: 51, b: 255 };
    led_buf[69] = GRB { r: 0, g: 0, b: 0 };
    led_buf[70] = GRB { r: 0, g: 0, b: 0 };
    led_buf[71] = GRB { r: 0, g: 0, b: 0 };
    led_buf[72] = GRB { r: 0, g: 0, b: 0 };
    led_buf[73] = GRB { r: 51, g: 255, b: 51 };
    led_buf[74] = GRB { r: 0, g: 0, b: 0 };
    led_buf[75] = GRB { r: 0, g: 0, b: 0 };
    led_buf[76] = GRB { r: 0, g: 0, b: 0 };
    led_buf[77] = GRB { r: 0, g: 0, b: 0 };
    led_buf[78] = GRB { r: 51, g: 51, b: 255 };
    led_buf[79] = GRB { r: 0, g: 0, b: 0 };
    led_buf[80] = GRB { r: 0, g: 0, b: 0 };
    led_buf[81] = GRB { r: 255, g: 51, b: 51 };
    led_buf[82] = GRB { r: 255, g: 105, b: 31 };
    led_buf[83] = GRB { r: 0, g: 0, b: 0 };
    led_buf[84] = GRB { r: 0, g: 0, b: 0 };
    led_buf[85] = GRB { r: 0, g: 0, b: 0 };
    led_buf[86] = GRB { r: 0, g: 0, b: 0 };
    led_buf[87] = GRB { r: 0, g: 0, b: 0 };
    led_buf[88] = GRB { r: 0, g: 0, b: 0 };
    led_buf[89] = GRB { r: 0, g: 0, b: 0 };
    led_buf[90] = GRB { r: 0, g: 0, b: 0 };
    led_buf[91] = GRB { r: 255, g: 51, b: 51 };
    led_buf[92] = GRB { r: 255, g: 105, b: 31 };
    led_buf[93] = GRB { r: 255, g: 51, b: 255 };
    led_buf[94] = GRB { r: 153, g: 51, b: 255 };
    led_buf[95] = GRB { r: 51, g: 51, b: 255 };
    led_buf[96] = GRB { r: 43, g: 192, b: 255 };
    led_buf[97] = GRB { r: 255, g: 51, b: 51 };
    led_buf[98] = GRB { r: 255, g: 105, b: 31 };
    led_buf[99] = GRB { r: 255, g: 51, b: 255 };
    led_buf[100] = GRB { r: 153, g: 51, b: 255 };
    led_buf[101] = GRB { r: 51, g: 51, b: 255 };
    led_buf[102] = GRB { r: 43, g: 192, b: 255 };
    led_buf[103] = GRB { r: 255, g: 51, b: 51 };
    led_buf[104] = GRB { r: 255, g: 105, b: 31 };
    led_buf[105] = GRB { r: 255, g: 255, b: 255 };
    led_buf[106] = GRB { r: 153, g: 51, b: 255 };
    led_buf[107] = GRB { r: 51, g: 51, b: 255 };
    led_buf[108] = GRB { r: 43, g: 192, b: 255 };
    led_buf[109] = GRB { r: 255, g: 51, b: 51 };
    led_buf[110] = GRB { r: 255, g: 105, b: 31 };
    led_buf[111] = GRB { r: 255, g: 51, b: 255 };
    led_buf[112] = GRB { r: 153, g: 51, b: 255 };
    led_buf[113] = GRB { r: 51, g: 51, b: 255 };
    led_buf[114] = GRB { r: 43, g: 192, b: 255 };
    led_buf[115] = GRB { r: 51, g: 51, b: 255 };

    for led in &mut led_buf {
        led.r = led.r / COLOR_DIMM_SCALE;
        led.g = led.g / COLOR_DIMM_SCALE;
        led.b = led.b / COLOR_DIMM_SCALE;
    }

    millis_init(dp.TC0);

    let mut ack_timestamp = millis();

    let mut serial = arduino_hal::default_serial!(dp, pins, 250_000);
    let mut watchdog = arduino_hal::hal::wdt::Wdt::new(dp.WDT, &dp.CPU.mcusr);
    #[allow(clippy::unwrap_used)]
    watchdog
        .start(arduino_hal::hal::wdt::Timeout::Ms1000)
        .unwrap();

    // FADER
    let mut adc = arduino_hal::Adc::new(dp.ADC, AdcSettings::default());
    let fader_1_in = pins.a6.into_analog_input(&mut adc);
    let mut old_position_1: u16 = fader_1_in.analog_read(&mut adc);
    let fader_2_in = pins.a7.into_analog_input(&mut adc);
    let mut old_position_2: u16 = fader_2_in.analog_read(&mut adc);
    let fader_3_in = pins.a8.into_analog_input(&mut adc);
    let mut old_position_3: u16 = fader_3_in.analog_read(&mut adc);
    let fader_4_in = pins.a9.into_analog_input(&mut adc);
    let mut old_position_4: u16 = fader_4_in.analog_read(&mut adc);
    let fader_5_in = pins.a10.into_analog_input(&mut adc);
    let mut old_position_5: u16 = fader_5_in.analog_read(&mut adc);
    let fader_6_in = pins.a11.into_analog_input(&mut adc);
    let mut old_position_6: u16 = fader_6_in.analog_read(&mut adc);

    let mut sendable_bytes_queue = SendableBytesQueueHead::default();

    unsafe { avr_device::interrupt::enable() }

    physical_leds.write(&led_buf);
    loop {
        serial.listen(Event::RxComplete);
        watchdog.feed();
        if !interrupt::free(|cs| IDENTIFIED.borrow(cs).get()) {
            continue;
        }
        #[cfg(not(debug_assertions))]
        {
            let new_position = fader_1_in.analog_read(&mut adc);
            if old_position_1.abs_diff(new_position) > 5 {
                old_position_1 = new_position;
                if sendable_bytes_queue.package_count(123) < MAX_FADER_QUEUE {
                    sendable_bytes_queue.append_package(RawInput {
                        id: 123,
                        value: (new_position / 4).clamp_cast(),
                    });
                }
            }
            let new_position = fader_2_in.analog_read(&mut adc);
            if old_position_2.abs_diff(new_position) > 5 {
                old_position_2 = new_position;
                if sendable_bytes_queue.package_count(124) < MAX_FADER_QUEUE {
                    sendable_bytes_queue.append_package(RawInput {
                        id: 124,
                        value: (new_position / 4).clamp_cast(),
                    });
                }
            }
            let new_position = fader_3_in.analog_read(&mut adc);
            if old_position_3.abs_diff(new_position) > 5 {
                old_position_3 = new_position;
                if sendable_bytes_queue.package_count(125) < MAX_FADER_QUEUE {
                    sendable_bytes_queue.append_package(RawInput {
                        id: 125,
                        value: (new_position / 4).clamp_cast(),
                    });
                }
            }
            let new_position = fader_4_in.analog_read(&mut adc);
            if old_position_4.abs_diff(new_position) > 5 {
                old_position_4 = new_position;
                if sendable_bytes_queue.package_count(126) < MAX_FADER_QUEUE {
                    sendable_bytes_queue.append_package(RawInput {
                        id: 126,
                        value: (new_position / 4).clamp_cast(),
                    });
                }
            }
            let new_position = fader_5_in.analog_read(&mut adc);
            if old_position_5.abs_diff(new_position) > 5 {
                old_position_5 = new_position;
                if sendable_bytes_queue.package_count(127) < MAX_FADER_QUEUE {
                    sendable_bytes_queue.append_package(RawInput {
                        id: 127,
                        value: (new_position / 4).clamp_cast(),
                    });
                }
            }
            let new_position = fader_6_in.analog_read(&mut adc);
            if old_position_6.abs_diff(new_position) > 5 {
                old_position_6 = new_position;
                if sendable_bytes_queue.package_count(128) < MAX_FADER_QUEUE {
                    sendable_bytes_queue.append_package(RawInput {
                        id: 128,
                        value: (new_position / 4).clamp_cast(),
                    });
                }
            }
        }

        if millis().wrapping_sub(ack_timestamp) > PAUSE_BETWEEN_SENDS_MS {
            if interrupt::free(|cs| ACKNOWLEDGED.borrow(cs).get()) {
                if sendable_bytes_queue.waiting_for_ack() {
                    sendable_bytes_queue.discard_first();
                    sendable_bytes_queue.set_waiting_for_ack(false);
                }
                if sendable_bytes_queue.is_empty() {
                    continue;
                }
            }
            if let Some(package) = sendable_bytes_queue.first() {
                let package_as_array: [u8; 3] = package.into();
                let package = [
                    HEAD_1,
                    HEAD_2,
                    HEAD_3,
                    RwModuleUsbPackage::Input(RawInput::default()).into(),
                    package_as_array[0],
                    package_as_array[1],
                    package_as_array[2],
                ];
                for byte in package {
                    serial.write_byte(byte);
                }
                sendable_bytes_queue.set_waiting_for_ack(true);
            }
            ack_timestamp = millis();
        }
    }
}
