<script lang="ts">
    import Tooltip from "./tooltip.svelte";
    import type { Snippet } from "svelte";

    let {
        reason,
        children,
    }: { reason: string | null | undefined; children: Snippet } = $props();

    let tooltipOriginElement: HTMLElement | undefined = $state();
    let tooltipOrigin: { x: number; y: number } | undefined = $derived.by(
        () => {
            if (tooltipOriginElement) {
                let originRect = tooltipOriginElement.getBoundingClientRect();
                return {
                    x: originRect.x + originRect.width + window.scrollX,
                    y: originRect.y + originRect.height + window.scrollY,
                };
            }
        },
    );
</script>

<Tooltip overwriteOrigin={tooltipOrigin} hidden={!reason}>
    <div class="flex">
        {@render children()}
        {#if reason?.length}
            <div
                class="h-2 w-2 rounded-full bg-red-600"
                bind:this={tooltipOriginElement}
            ></div>
            <div
                class="-ml-2 h-2 w-2 animate-ping rounded-full bg-red-600"
            ></div>
        {/if}
    </div>
    {#snippet tooltip()}
        {#if reason?.length}
            <p>{reason}</p>
        {/if}
    {/snippet}
</Tooltip>
