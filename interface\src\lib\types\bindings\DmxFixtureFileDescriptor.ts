// This file was generated by [ts-rs](https://github.com/Aleph-Alpha/ts-rs). Do not edit this file manually.
import type { DmxFixtureColor } from "./DmxFixtureColor";
import type { MovementChannels } from "./MovementChannels";
import type { UnimplementedChannel } from "./UnimplementedChannel";

export interface DmxFixtureFileDescriptor { id: number | null, dmx_address: number | null, dmx_universe: number, name: string, fixturetype: string, movement_channels: MovementChannels | null, stage_coordinates: [number, number], footprint_size: number, unimplemented_channels: Array<UnimplementedChannel>, color: DmxFixtureColor | null, }