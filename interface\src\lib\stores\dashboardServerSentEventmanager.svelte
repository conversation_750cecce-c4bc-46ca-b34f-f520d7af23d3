<script lang="ts">
    import { onMount } from "svelte";
    import { networking } from "$lib/stores/networking";
    import { dashboard } from "$lib/stores/dashboard";
    import { get } from "svelte/store";

    onMount(async () => {
        await_ip();
    });

    async function await_ip() {
        return new Promise(async (resolve, _) => {
            const ip = get(networking);
            if (ip) {
                attach_sse(ip);
            } else {
                setTimeout(async () => resolve(await await_ip()), 1000);
            }
        });
    }

    async function attach_sse(ip: string) {
        const eventSource = new EventSource(
            `http://${ip}:${networking.port}/dashboard/sse`,
        );

        eventSource.addEventListener("message", (event) => {
            try {
                let parsed_data = JSON.parse(event.data);
                $dashboard = parsed_data;
            } catch {}
        });

        eventSource.addEventListener("error", (_) => {});

        eventSource.addEventListener("open", () => {});
    }
</script>
