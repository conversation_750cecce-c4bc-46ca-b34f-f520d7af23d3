[workspace]
resolver = "2"
members = [ "button","millis", "rwm_communication", "rwm_package_definitions", "tomahawk_1", "tomahawk_2"]

[profile.dev]
panic = "abort"
lto = true
opt-level = "s"

[profile.release]
panic = "abort"
codegen-units = 1
debug = false
lto = true
opt-level = 3
strip = "debuginfo"

[workspace.lints.clippy]
pedantic = "warn"
arithmetic_side_effects = "warn"
clone_on_ref_ptr = "warn"
expect_used = "warn"
float_cmp_const = "warn"
indexing_slicing = "warn"
panic = "warn"
string_add = "warn"
string_to_string = "warn"
todo = "warn"
unimplemented = "warn"
unreachable = "warn"
unwrap_used = "warn"
wildcard_enum_match_arm = "warn"
module_name_repetitions = { level = "allow", priority = 1 }
similar_names = { level = "allow", priority = 1 }
assigning_clones = { level = "allow", priority = 1 }
too_many_lines = { level = "allow", priority = 1 }
