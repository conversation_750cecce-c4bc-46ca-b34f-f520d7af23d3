#![no_std]
#![feature(abi_avr_interrupt)]
#![feature(cell_update)]

use arduino_hal::pac::TC0;
use avr_device::interrupt::Mutex;
use core::cell;

const PRESCALER: u32 = 1024;
const TIMER_COUNTS: u8 = 125;

const MILLIS_INCREMENT: u32 = PRESCALER * TIMER_COUNTS as u32 / 16000;

static MILLIS_COUNTER: Mutex<cell::Cell<u32>> = Mutex::new(cell::Cell::new(0));

#[allow(clippy::needless_pass_by_value)]
pub fn millis_init(tc0: TC0) {
    tc0.tccr0a.write(|w| w.wgm0().ctc());
    tc0.ocr0a.write(|w| w.bits(TIMER_COUNTS));
    tc0.tccr0b.write(|w| w.cs0().prescale_1024());
    tc0.timsk0.write(|w| w.ocie0a().set_bit());

    avr_device::interrupt::free(|cs| {
        MILLIS_COUNTER.borrow(cs).set(0);
    });
}

#[avr_device::interrupt(atmega2560)]
fn TIMER0_COMPA() {
    avr_device::interrupt::free(|cs| {
        let counter_cell = MILLIS_COUNTER.borrow(cs);
        let counter = counter_cell.get();
        counter_cell.set(counter.wrapping_add(MILLIS_INCREMENT));
    });
}

#[must_use]
pub fn millis() -> u32 {
    avr_device::interrupt::free(|cs| MILLIS_COUNTER.borrow(cs).get())
}
