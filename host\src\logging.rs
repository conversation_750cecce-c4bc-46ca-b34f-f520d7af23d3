use colored::Colorize;
use mysql::prelude::Queryable;
use mysql::PooledConn;
use serde::Serialize;
use std::fs;
use std::io::Write;
use ts_rs::TS;

/// describes the priority level at which the messages should be logged
pub enum LogLevel {
    /// level-2
    Info,
    /// level-1
    Warning,
    /// db-error
    DbError,
}

const LOG_FILE_NAME_EXTENSION: &str = ".log";
const LOG_FILE_PATH: &str = "logs/";

#[derive(Default, Serialize, TS)]
#[ts(export)]
pub struct Dashboard {
    pub fixture_count: usize,
    pub universes: Vec<usize>,
    pub snippets: Vec<(String, String)>,
    pub dbg_mode: String,
    pub bpm: u16,
    pub bpm_modifier: u8,
}

/// This loggs a given message with the given prioritylevel to the console and to a logfile if persist is true.
#[allow(clippy::needless_pass_by_value)]
pub fn log(message: String, log_level: LogLevel, persist: bool) {
    let current_time = get_time();
    if let Ok(mut file) = fs::OpenOptions::new()
        .write(true)
        .create(true)
        .append(true)
        .open(format!(
            "{}{}{}",
            LOG_FILE_PATH,
            get_date(),
            LOG_FILE_NAME_EXTENSION
        ))
    {
        match log_level {
            LogLevel::Info => {
                println!("{} {}", "INFO:".blue(), message);
                if persist {
                    writeln!(&mut file, "{current_time} - INFO : {message}")
                        .unwrap();
                }
            }
            LogLevel::Warning => {
                println!("{} {}", "WARN:".bright_red(), message.bright_white());
                if persist {
                    writeln!(&mut file, "{current_time} - WARN : {message}")
                        .unwrap();
                }
            }
            LogLevel::DbError => {
                println!(
                    "{} {} {}",
                    "WARN:".bright_red(),
                    "[DB]".bright_red(),
                    message.bright_white()
                );
                if persist {
                    writeln!(
                        &mut file,
                        "{current_time} - WARN : [DB] {message}"
                    )
                    .unwrap();
                }
            }
        }
    } else {
        log(
            "failed to open logfile".to_string(),
            LogLevel::Warning,
            false,
        );
    }
}

/// This function is only a quick helper for short debuggs
#[deprecated(note = "**Use this function only while debugging**")]
#[allow(clippy::needless_pass_by_value)]
#[cfg(debug_assertions)]
pub fn debug(message: String) {
    #[cfg(debug_assertions)]
    println!("{} {}", "DEBG:".purple(), message.bright_purple());
}

/// This function writes an indicator line to the log file to mark a new programm coldstart
pub fn mark_start(mode: &str) {
    let _ = std::fs::create_dir_all("logs");
    if let Ok(mut file) = fs::OpenOptions::new()
        .write(true)
        .create(true)
        .append(true)
        .open(format!(
            "{}{}{}",
            LOG_FILE_PATH,
            get_date(),
            LOG_FILE_NAME_EXTENSION
        ))
    {
        let _trash_bin =
            writeln!(&mut file, "\n{} - STARTUP in {mode} mode", get_time());

        let loglevel: LogLevel = match mode {
            "DEBUG" => LogLevel::Warning,
            "RELEASE" => LogLevel::Info,
            e => {
                log(
                    format!("Unknown loglevel found {e}"),
                    LogLevel::Warning,
                    false,
                );
                LogLevel::Warning
            }
        };
        log(
            format!(
                "Running RW in {mode} mode with version {}",
                env!("CARGO_PKG_VERSION")
            ),
            loglevel,
            false,
        );
    } else {
        log(
            "failed to open logfile".to_string(),
            LogLevel::Warning,
            false,
        );
    }
}
fn get_time() -> String {
    format!("{} UTC", date_time().1)
}
fn get_date() -> String {
    date_time().0
}

fn date_time() -> (String, String) {
    const SECONDS_IN_A_MINUTE: i64 = 60;
    const SECONDS_IN_AN_HOUR: i64 = 3600;
    const SECONDS_IN_A_DAY: i64 = 86400;

    let seconds_since_epoch: i64 = std::time::SystemTime::now()
        .duration_since(std::time::UNIX_EPOCH)
        .unwrap_or(core::time::Duration::ZERO)
        .as_secs()
        .try_into()
        .unwrap_or_default();

    let mut seconds = seconds_since_epoch;
    let mut year = 1970;

    while seconds
        >= (if is_leap_year(year) { 366 } else { 365_i64 })
            .saturating_mul(SECONDS_IN_A_DAY)
    {
        seconds = seconds.saturating_sub(
            (if is_leap_year(year) { 366 } else { 365_i64 })
                .saturating_mul(SECONDS_IN_A_DAY),
        );
        year = year.saturating_add(1);
    }

    let mut month = 0;
    let mut days = seconds / SECONDS_IN_A_DAY;
    #[allow(clippy::modulo_arithmetic)]
    {
        seconds %= SECONDS_IN_A_DAY;
    }

    let month_days = [
        31,
        if is_leap_year(year) { 29 } else { 28 },
        31,
        30,
        31,
        30,
        31,
        31,
        30,
        31,
        30,
        31,
    ];

    while Some(&days) >= month_days.get(month) {
        days = days.saturating_sub(*month_days.get(month).unwrap_or(&0));
        month = month.saturating_add(1);
    }

    let day = days.saturating_add(1);

    let hours = seconds / SECONDS_IN_AN_HOUR;
    #[allow(clippy::modulo_arithmetic)]
    {
        seconds %= SECONDS_IN_AN_HOUR;
    }
    let minutes = seconds / SECONDS_IN_A_MINUTE;
    #[allow(clippy::modulo_arithmetic)]
    let seconds = { seconds % SECONDS_IN_A_MINUTE };

    (
        format!("{day:02}.{:02}.{year}", month.saturating_add(1)),
        format!("{hours:02}:{minutes:02}:{seconds:02}"),
    )
}

const fn is_leap_year(year: i64) -> bool {
    (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0)
}

pub fn log_show_metadata(db_connection: &mut PooledConn) {
    if let Some(show_name) = db_connection
        .query_map(
            "
                SELECT name
                FROM shows
                WHERE shows.active
            ",
            |name: String| name,
        )
        .unwrap_or_else(|err| {
            log(
                format!("{err:?}\n        (selecting active showname)"),
                LogLevel::DbError,
                true,
            );
            vec![]
        })
        .first()
    {
        if let Some(fixtures_count) = db_connection
            .query_map(
                "
                SELECT COUNT(*)
                FROM fixtures
                JOIN shows ON fixtures.show_id = shows.id
                WHERE shows.active
            ",
                |count: usize| count,
            )
            .unwrap_or_else(|err| {
                log(
                    format!("{err:?}\n        (selecting fixture count)"),
                    LogLevel::DbError,
                    true,
                );
                vec![]
            })
            .first()
        {
            let mut existing_universes = db_connection.query_map(
            "
                SELECT dmx_universe
                FROM fixtures
                JOIN shows ON fixtures.show_id = shows.id
                WHERE shows.active
            ",
            |universe: usize| universe,
        )
        .unwrap_or_else(|err| {
            log(
                format!(
                    "{err:?}\n        (selecting dmx_universes where show is active)"
                ),
                LogLevel::DbError,
                true,
            );
            vec![]
        });
            existing_universes.sort_unstable();
            existing_universes.dedup();
            log(
                format!(
                    "Show {} selected with {} {}{} {}",
                    show_name.green(),
                    format!("{fixtures_count}").green(),
                    if *fixtures_count == 1 {
                        "fixture"
                    } else {
                        "fixtures"
                    },
                    if existing_universes.len() == 1 {
                        " on universe"
                    } else if existing_universes.is_empty() {
                        ""
                    } else {
                        " on universes"
                    },
                    if existing_universes.is_empty() {
                        String::new().green()
                    } else {
                        format!("{existing_universes:?}").green()
                    },
                ),
                LogLevel::Info,
                true,
            );
        }
    } else {
        log("No active show found".to_owned(), LogLevel::Warning, true);
    }
}
