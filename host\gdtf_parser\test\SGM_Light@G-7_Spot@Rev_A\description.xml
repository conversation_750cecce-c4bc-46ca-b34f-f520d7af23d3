<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<GDTF DataVersion="1.1">

  <FixtureType CanHaveChildren="Yes" Description="The G-7 Spot is the quintessence of IP-rated moving heads. A fast, compact, and lightweight mid-sized moving head spot with high-output and low power consumption. Thanks to its white LED engine and CMY color mixing, the G-7 Spot is the perfect moving head for those who need maximum light output inside an easy-to-move luminaire. The G-7 Spot gives you a solid construction, a high-quality beam, and an optimal projection in a very flexible assembly. A fixture born to rock night after night." FixtureTypeID="14030EC0-9085-4756-8B19-8B08369E06B9" LongName="G-7 Spot" Manufacturer="SGM Light" Name="G-7 Spot" RefFT="" ShortName="G-7 Spot" Thumbnail="G-7_RAL_black_small">
    <AttributeDefinitions>
      <ActivationGroups>
        <ActivationGroup Name="ColorRGB"/>
        <ActivationGroup Name="PanTilt"/>
        <ActivationGroup Name="Gobo1"/>
        <ActivationGroup Name="ColorIndirect"/>
        <ActivationGroup Name="Gobo2"/>
        <ActivationGroup Name="Prism"/>
      </ActivationGroups>
      <FeatureGroups>
        <FeatureGroup Name="Beam" Pretty="Beam">
          <Feature Name="Beam"/>
        </FeatureGroup>
        <FeatureGroup Name="Dimmer" Pretty="Dimmer">
          <Feature Name="Dimmer"/>
        </FeatureGroup>
        <FeatureGroup Name="Color" Pretty="Color">
          <Feature Name="Color"/>
          <Feature Name="Indirect"/>
        </FeatureGroup>
        <FeatureGroup Name="Position" Pretty="Position">
          <Feature Name="PanTilt"/>
        </FeatureGroup>
        <FeatureGroup Name="Gobo" Pretty="Gobo">
          <Feature Name="Gobo"/>
        </FeatureGroup>
        <FeatureGroup Name="Focus" Pretty="Focus">
          <Feature Name="Focus"/>
        </FeatureGroup>
        <FeatureGroup Name="Control" Pretty="Control">
          <Feature Name="Control"/>
        </FeatureGroup>
      </FeatureGroups>
      <Attributes>
        <Attribute Feature="Beam.Beam" Name="Shutter1" PhysicalUnit="None" Pretty="Sh1"/>
        <Attribute Feature="Dimmer.Dimmer" Name="Dimmer" PhysicalUnit="None" Pretty="Dim"/>
        <Attribute ActivationGroup="ColorRGB" Feature="Color.Color" Name="Color1" PhysicalUnit="None" Pretty="C1"/>
        <Attribute ActivationGroup="PanTilt" Feature="Position.PanTilt" Name="Pan" PhysicalUnit="Angle" Pretty="P"/>
        <Attribute ActivationGroup="PanTilt" Feature="Position.PanTilt" Name="Tilt" PhysicalUnit="Angle" Pretty="T"/>
        <Attribute ActivationGroup="Gobo1" Feature="Gobo.Gobo" Name="Gobo1" PhysicalUnit="None" Pretty="G1"/>
        <Attribute ActivationGroup="Gobo1" Feature="Gobo.Gobo" MainAttribute="Gobo1" Name="Gobo1WheelIndex" PhysicalUnit="Angle" Pretty="Wheel Index"/>
        <Attribute Feature="Focus.Focus" Name="Zoom" PhysicalUnit="Angle" Pretty="Zoom"/>
        <Attribute Feature="Control.Control" Name="Function" PhysicalUnit="None" Pretty="Function"/>
        <Attribute Color="0.312700,0.329000,100.000000" Feature="Control.Control" Name="Reserved" PhysicalUnit="None" Pretty="Reserved"/>
        <Attribute Feature="Beam.Beam" MainAttribute="Shutter1" Name="Shutter1StrobeEffect" PhysicalUnit="Frequency" Pretty="Effect1"/>
        <Attribute Feature="Beam.Beam" MainAttribute="Shutter1" Name="Shutter1Strobe" PhysicalUnit="Frequency" Pretty="Strobe1"/>
        <Attribute Feature="Beam.Beam" MainAttribute="Shutter1" Name="Shutter1StrobePulseOpen" PhysicalUnit="Frequency" Pretty="Pulse Open1"/>
        <Attribute Feature="Beam.Beam" MainAttribute="Shutter1" Name="Shutter1StrobeRandom" PhysicalUnit="Frequency" Pretty="Random1"/>
        <Attribute Feature="Beam.Beam" MainAttribute="Shutter1" Name="Shutter1StrobePulseClose" PhysicalUnit="Frequency" Pretty="Pulse Close1"/>
        <Attribute Feature="Beam.Beam" MainAttribute="Shutter1" Name="Shutter1StrobePulse" PhysicalUnit="Frequency" Pretty="Pulse1"/>
        <Attribute Feature="Control.Control" Name="NoFeature" PhysicalUnit="None" Pretty="NoFeature"/>
        <Attribute ActivationGroup="ColorRGB" Feature="Color.Color" MainAttribute="Color1" Name="Color1WheelSpin" PhysicalUnit="AngularSpeed" Pretty="Wheel Spin"/>
        <Attribute ActivationGroup="ColorRGB" Feature="Color.Color" MainAttribute="Color1" Name="Color1WheelIndex" PhysicalUnit="Angle" Pretty="Wheel Index"/>
        <Attribute ActivationGroup="Gobo1" Feature="Gobo.Gobo" MainAttribute="Gobo1" Name="Gobo1SelectShake" PhysicalUnit="Frequency" Pretty="Select Shake"/>
        <Attribute ActivationGroup="Gobo1" Feature="Gobo.Gobo" MainAttribute="Gobo1" Name="Gobo1WheelSpin" PhysicalUnit="AngularSpeed" Pretty="Wheel Spin"/>
        <Attribute ActivationGroup="ColorIndirect" Feature="Color.Indirect" Name="ColorRGB_Cyan" PhysicalUnit="None" Pretty="C"/>
        <Attribute ActivationGroup="ColorIndirect" Feature="Color.Indirect" Name="ColorRGB_Magenta" PhysicalUnit="None" Pretty="M"/>
        <Attribute ActivationGroup="ColorIndirect" Feature="Color.Indirect" Name="ColorRGB_Yellow" PhysicalUnit="None" Pretty="Y"/>
        <Attribute Feature="Color.Color" Name="CTO" PhysicalUnit="None" Pretty="CTO"/>
        <Attribute ActivationGroup="Gobo2" Feature="Gobo.Gobo" Name="Gobo2" PhysicalUnit="None" Pretty="G2"/>
        <Attribute Feature="Focus.Focus" Name="Focus1" PhysicalUnit="None" Pretty="Focus1"/>
        <Attribute Feature="Beam.Beam" Name="Iris" PhysicalUnit="None" Pretty="Iris"/>
        <Attribute Feature="Beam.Beam" MainAttribute="Iris" Name="IrisPulseOpen" PhysicalUnit="Frequency" Pretty="Pulse Open"/>
        <Attribute Feature="Beam.Beam" MainAttribute="Iris" Name="IrisPulseClose" PhysicalUnit="Frequency" Pretty="Pulse Close"/>
        <Attribute Feature="Beam.Beam" MainAttribute="Iris" Name="IrisRandomPulseOpen" PhysicalUnit="Frequency" Pretty="Random Pulse Open"/>
        <Attribute ActivationGroup="Prism" Feature="Beam.Beam" Name="Prism1" PhysicalUnit="None" Pretty="Prism1"/>
        <Attribute Feature="Beam.Beam" Name="Frost1" PhysicalUnit="None" Pretty="Frost1"/>
      </Attributes>
    </AttributeDefinitions>
    <Wheels>
      <Wheel Name="Color Wheel 1">
        <Slot Color="0.312700,0.329000,100.000000" Name="Open"/>
        <Slot Color="0.171200,0.117500,1.278000" Name="Color 1"/>
        <Slot Color="0.523600,0.419700,21.870000" Name="Color 2"/>
        <Slot Color="0.322200,0.523500,48.310000" Name="Color 3"/>
        <Slot Color="0.202600,0.178200,6.860000" Name="Color 4"/>
        <Slot Color="0.328900,0.352400,96.480000" Name="Color 5"/>
        <Slot Color="0.640100,0.330000,21.260000" Name="Red"/>
        <Slot Color="0.300000,0.600000,71.520000" Name="Green"/>
        <Slot Color="0.150000,0.060010,7.220000" Name="Blue"/>
        <Slot Color="0.312700,0.329000,100.000000" Name="Effect"/>
      </Wheel>
      <Wheel Name="Rotating Gobo Wheel">
        <Slot Color="0.312700,0.329000,100.000000" Name="Open"/>
        <Slot Color="0.312700,0.329000,100.000000" Name="Gobo 1"/>
        <Slot Color="0.312700,0.329000,100.000000" Name="Gobo 2"/>
        <Slot Color="0.312700,0.329000,100.000000" Name="Gobo 3"/>
        <Slot Color="0.312700,0.329000,100.000000" Name="Gobo 4"/>
        <Slot Color="0.312700,0.329000,100.000000" Name="Gobo 5"/>
        <Slot Color="0.312700,0.329000,100.000000" Name="Gobo 6"/>
      </Wheel>
      <Wheel Name="Static Gobo Wheel">
        <Slot Color="0.312700,0.329000,100.000000" Name="Open"/>
        <Slot Color="0.312700,0.329000,100.000000" Name="Gobo 1"/>
        <Slot Color="0.312700,0.329000,100.000000" Name="Gobo 2"/>
        <Slot Color="0.312700,0.329000,100.000000" Name="Gobo 3"/>
        <Slot Color="0.312700,0.329000,100.000000" Name="Gobo 4"/>
        <Slot Color="0.312700,0.329000,100.000000" Name="Gobo 5"/>
        <Slot Color="0.312700,0.329000,100.000000" Name="Gobo 6"/>
        <Slot Color="0.312700,0.329000,100.000000" Name="Gobo 7"/>
        <Slot Color="0.312700,0.329000,100.000000" Name="Gobo 8"/>
        <Slot Color="0.312700,0.329000,100.000000" Name="Gobo 9"/>
      </Wheel>
      <Wheel Name="Prism">
        <Slot Color="0.312700,0.329000,100.000000" Name="Prism"/>
      </Wheel>
    </Wheels>
    <PhysicalDescriptions>
      <ColorSpace Mode="sRGB"/>
      <Filters/>
      <Emitters>
        <Emitter Color="0.312700,0.329000,100.000000" DiodePart="" DominantWaveLength="0.000000" Name="LED Engine">
          <Measurement InterpolationTo="Linear" LuminousIntensity="10.000000" Physical="100.000000"/>
        </Emitter>
      </Emitters>
      <DMXProfiles/>
      <CRIs/>
      <Connectors>
        <Connector DMXBreak="0" Gender="-1" Length="0.000000" Name="DMX In" Type="XLR5"/>
        <Connector DMXBreak="0" Gender="1" Length="0.000000" Name="DMX Out" Type="XLR5"/>
        <Connector DMXBreak="0" Gender="-1" Length="0.000000" Name="Power In" Type="PowerconTRUE1"/>
        <Connector DMXBreak="0" Gender="1" Length="0.000000" Name="Power Out" Type="PowerconTRUE1"/>
      </Connectors>
      <Properties>
        <OperatingTemperature High="40.000000" Low="0.000000"/>
        <Weight Value="27.000000"/>
        <LegHeight Value="0.000000"/>
      </Properties>
    </PhysicalDescriptions>
    <Models>
      <Model File="" Height="0.120000" Length="0.310000" Name="Base" PrimitiveType="Base" Width="0.310000"/>
      <Model File="" Height="0.320000" Length="0.380000" Name="Yoke" PrimitiveType="Yoke" Width="0.120000"/>
      <Model File="" Height="0.450000" Length="0.240000" Name="Head" PrimitiveType="Head" Width="0.220000"/>
      <Model File="" Height="0.020000" Length="0.080000" Name="Beam" PrimitiveType="Cylinder" Width="0.080000"/>
    </Models>
    <Geometries>
      <Geometry Model="Base" Name="Base" Position="{1.000000,0.000000,0.000000,0.000000}{0.000000,1.000000,0.000000,0.000000}{0.000000,0.000000,1.000000,0.000000}{0,0,0,1}">
        <Axis Model="Yoke" Name="Yoke" Position="{1.000000,0.000000,0.000000,0.000000}{0.000000,1.000000,0.000000,0.000000}{0.000000,0.000000,1.000000,-0.220000}{0,0,0,1}">
          <Axis Model="Head" Name="Head" Position="{1.000000,0.000000,0.000000,0.000000}{0.000000,1.000000,0.000000,0.000000}{0.000000,0.000000,1.000000,-0.140000}{0,0,0,1}">
            <Beam BeamAngle="25.000000" BeamRadius="0.040000" BeamType="Wash" ColorRenderingIndex="100" ColorTemperature="6000.000000" FieldAngle="25.000000" LampType="LED" LuminousFlux="1000.000000" Model="Beam" Name="Beam" Position="{1.000000,0.000000,0.000000,0.000000}{0.000000,1.000000,0.000000,0.000000}{0.000000,0.000000,1.000000,-0.210000}{0,0,0,1}" PowerConsumption="1000.000000" RectangleRatio="1.777700" ThrowRatio="1.000000">
              <Geometry Model="Beam" Name="Beam Pix 1" Position="{1.000000,0.000000,0.000000,0.000000}{0.000000,1.000000,0.000000,0.000000}{0.000000,0.000000,1.000000,0.000000}{0,0,0,1}"/>
              <Geometry Model="Beam" Name="Beam Pix 2" Position="{1.000000,0.000000,0.000000,0.000000}{0.000000,1.000000,0.000000,0.000000}{0.000000,0.000000,1.000000,0.000000}{0,0,0,1}"/>
              <Geometry Model="Beam" Name="Beam Pix 3" Position="{1.000000,0.000000,0.000000,0.000000}{0.000000,1.000000,0.000000,0.000000}{0.000000,0.000000,1.000000,0.000000}{0,0,0,1}"/>
            </Beam>
          </Axis>
        </Axis>
      </Geometry>
    </Geometries>
    <DMXModes>
      <DMXMode Geometry="Base" Name="Standard">
        <DMXChannels>
          <DMXChannel DMXBreak="1" Geometry="Beam" Highlight="8/1" InitialFunction="Beam_Shutter1.Shutter1.Open" Offset="1">
            <LogicalChannel Attribute="Shutter1" DMXChangeTimeLimit="0.000000" Master="None" MibFade="0.000000" Snap="No">
              <ChannelFunction Attribute="Shutter1" DMXFrom="0/1" Default="8/1" Name="Open" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="0/1" Name="Closed" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="5/1" Name="Open" WheelSlotIndex="0"/>
              </ChannelFunction>
              <ChannelFunction Attribute="Shutter1Strobe" DMXFrom="10/1" Default="10/1" Name="Strobe" OriginalAttribute="" PhysicalFrom="10.000000" PhysicalTo="0.100000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="10/1" Name="Slow" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="11/1" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="54/1" Name="Fast" WheelSlotIndex="0"/>
              </ChannelFunction>
              <ChannelFunction Attribute="Shutter1StrobeRandom" DMXFrom="55/1" Default="55/1" Name="Random" OriginalAttribute="" PhysicalFrom="0.100000" PhysicalTo="10.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="55/1" Name="Slow" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="56/1" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="79/1" Name="Fast" WheelSlotIndex="0"/>
              </ChannelFunction>
              <ChannelFunction Attribute="Shutter1StrobePulseOpen" DMXFrom="80/1" Default="80/1" Name="Pulse Up" OriginalAttribute="" PhysicalFrom="0.100000" PhysicalTo="10.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="80/1" Name="Slow" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="81/1" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="84/1" Name="Fast" WheelSlotIndex="0"/>
              </ChannelFunction>
              <ChannelFunction Attribute="Shutter1StrobePulseClose" DMXFrom="85/1" Default="85/1" Name="Pulse Down" OriginalAttribute="" PhysicalFrom="0.100000" PhysicalTo="10.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="85/1" Name="Slow" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="86/1" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="89/1" Name="Fast" WheelSlotIndex="0"/>
              </ChannelFunction>
              <ChannelFunction Attribute="Shutter1StrobePulse" DMXFrom="90/1" Default="90/1" Name="Pulse Up Down" OriginalAttribute="" PhysicalFrom="0.100000" PhysicalTo="10.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="90/1" Name="Slow" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="91/1" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="94/1" Name="Fast" WheelSlotIndex="0"/>
              </ChannelFunction>
              <ChannelFunction Attribute="Shutter1StrobeEffect" DMXFrom="95/1" Default="95/1" Name="Double Strobe" OriginalAttribute="" PhysicalFrom="0.100000" PhysicalTo="10.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="95/1" Name="Slow" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="96/1" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="139/1" Name="Fast" WheelSlotIndex="0"/>
              </ChannelFunction>
              <ChannelFunction Attribute="Shutter1StrobeEffect" DMXFrom="140/1" Default="140/1" Name="Triple Strobe" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="140/1" Name="Slow" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="141/1" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="184/1" Name="Fast" WheelSlotIndex="0"/>
              </ChannelFunction>
              <ChannelFunction Attribute="NoFeature" DMXFrom="185/1" Default="185/1" Name="Reserved" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000"/>
              <ChannelFunction Attribute="Shutter1StrobeEffect" DMXFrom="225/1" Default="225/1" Name="Fade Wave Up" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="225/1" Name="Slow" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="226/1" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="229/1" Name="Fast" WheelSlotIndex="0"/>
              </ChannelFunction>
              <ChannelFunction Attribute="Shutter1StrobeEffect" DMXFrom="230/1" Default="230/1" Name="Random Pixel" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="230/1" Name="Fast" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="231/1" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="234/1" Name="Slow" WheelSlotIndex="0"/>
              </ChannelFunction>
              <ChannelFunction Attribute="Shutter1StrobeEffect" DMXFrom="235/1" Default="235/1" Name="Wave Up Down" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="235/1" Name="Fast" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="236/1" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="239/1" Name="Slow" WheelSlotIndex="0"/>
              </ChannelFunction>
              <ChannelFunction Attribute="Shutter1StrobeEffect" DMXFrom="240/1" Default="240/1" Name="Wave Up" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="240/1" Name="Fast" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="241/1" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="244/1" Name="Slow" WheelSlotIndex="0"/>
              </ChannelFunction>
              <ChannelFunction Attribute="Shutter1StrobeEffect" DMXFrom="245/1" Default="245/1" Name="Wave Down" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="245/1" Name="Slow" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="246/1" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="249/1" Name="Fast" WheelSlotIndex="0"/>
              </ChannelFunction>
              <ChannelFunction Attribute="Shutter1" DMXFrom="250/1" Default="250/1" Name="Open (2)" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="250/1" Name="Open" WheelSlotIndex="0"/>
              </ChannelFunction>
            </LogicalChannel>
          </DMXChannel>
          <DMXChannel DMXBreak="1" Geometry="Beam" Highlight="100/1" InitialFunction="Beam_Dimmer.Dimmer.Dimmer" Offset="2">
            <LogicalChannel Attribute="Dimmer" DMXChangeTimeLimit="0.000000" Master="Grand" MibFade="0.000000" Snap="No">
              <ChannelFunction Attribute="Dimmer" DMXFrom="0/1" Default="0/1" Name="Dimmer" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="0/1" Name="min" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="1/1" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="100/1" Name="max" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="101/1" Name="" WheelSlotIndex="0"/>
              </ChannelFunction>
            </LogicalChannel>
          </DMXChannel>
          <DMXChannel DMXBreak="1" Geometry="Beam" Highlight="None" InitialFunction="Beam_ColorRGB_Cyan.ColorRGB_Cyan.C" Offset="3">
            <LogicalChannel Attribute="ColorRGB_Cyan" DMXChangeTimeLimit="0.000000" Master="None" MibFade="0.000000" Snap="No">
              <ChannelFunction Attribute="ColorRGB_Cyan" DMXFrom="0/1" Default="0/1" Name="C" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="0/1" Name="min" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="1/1" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="255/1" Name="max" WheelSlotIndex="0"/>
              </ChannelFunction>
            </LogicalChannel>
          </DMXChannel>
          <DMXChannel DMXBreak="1" Geometry="Beam" Highlight="None" InitialFunction="Beam_ColorRGB_Magenta.ColorRGB_Magenta.M" Offset="4">
            <LogicalChannel Attribute="ColorRGB_Magenta" DMXChangeTimeLimit="0.000000" Master="None" MibFade="0.000000" Snap="No">
              <ChannelFunction Attribute="ColorRGB_Magenta" DMXFrom="0/1" Default="0/1" Name="M" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="0/1" Name="min" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="1/1" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="255/1" Name="max" WheelSlotIndex="0"/>
              </ChannelFunction>
            </LogicalChannel>
          </DMXChannel>
          <DMXChannel DMXBreak="1" Geometry="Beam" Highlight="None" InitialFunction="Beam_ColorRGB_Yellow.ColorRGB_Yellow.Y" Offset="5">
            <LogicalChannel Attribute="ColorRGB_Yellow" DMXChangeTimeLimit="0.000000" Master="None" MibFade="0.000000" Snap="No">
              <ChannelFunction Attribute="ColorRGB_Yellow" DMXFrom="0/1" Default="0/1" Name="Y" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="0/1" Name="min" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="1/1" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="255/1" Name="max" WheelSlotIndex="0"/>
              </ChannelFunction>
            </LogicalChannel>
          </DMXChannel>
          <DMXChannel DMXBreak="1" Geometry="Beam" Highlight="None" InitialFunction="Beam_CTO.CTO.CTO" Offset="6">
            <LogicalChannel Attribute="CTO" DMXChangeTimeLimit="0.000000" Master="None" MibFade="0.000000" Snap="No">
              <ChannelFunction Attribute="CTO" DMXFrom="0/1" Default="0/1" Name="CTO" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="0/1" Name="min" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="1/1" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="244/1" Name="max" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="245/1" Name="CTB" WheelSlotIndex="0"/>
              </ChannelFunction>
            </LogicalChannel>
          </DMXChannel>
          <DMXChannel DMXBreak="1" Geometry="Beam" Highlight="None" InitialFunction="Beam_Color1.Color1.Fixed Colors" Offset="7">
            <LogicalChannel Attribute="Color1" DMXChangeTimeLimit="0.000000" Master="None" MibFade="0.000000" Snap="No">
              <ChannelFunction Attribute="Color1" DMXFrom="0/1" Default="0/1" Name="Fixed Colors" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000" Wheel="Color Wheel 1">
                <ChannelSet DMXFrom="0/1" Name="Open" WheelSlotIndex="1"/>
                <ChannelSet DMXFrom="10/1" Name="Color 1" WheelSlotIndex="2"/>
                <ChannelSet DMXFrom="20/1" Name="Color 2" WheelSlotIndex="3"/>
                <ChannelSet DMXFrom="30/1" Name="Color 3" WheelSlotIndex="4"/>
                <ChannelSet DMXFrom="40/1" Name="Color 4" WheelSlotIndex="5"/>
                <ChannelSet DMXFrom="50/1" Name="Color 5" WheelSlotIndex="6"/>
                <ChannelSet DMXFrom="60/1" Name="Color 6" WheelSlotIndex="7"/>
                <ChannelSet DMXFrom="70/1" Name="Color 7" WheelSlotIndex="8"/>
                <ChannelSet DMXFrom="80/1" Name="Color 8" WheelSlotIndex="9"/>
                <ChannelSet DMXFrom="90/1" Name="Color 9" WheelSlotIndex="10"/>
              </ChannelFunction>
              <ChannelFunction Attribute="Color1WheelIndex" DMXFrom="100/1" Default="100/1" Name="Split Colors" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000" Wheel="Color Wheel 1">
                <ChannelSet DMXFrom="100/1" Name="Open" WheelSlotIndex="1"/>
                <ChannelSet DMXFrom="101/1" Name="Open -&gt; Color 1" WheelSlotIndex="2"/>
                <ChannelSet DMXFrom="110/1" Name="Color 1 -&gt; Color 2" WheelSlotIndex="3"/>
                <ChannelSet DMXFrom="120/1" Name="Color 2 -&gt; Color 3" WheelSlotIndex="4"/>
                <ChannelSet DMXFrom="130/1" Name="Color 3 -&gt; Color 4" WheelSlotIndex="5"/>
                <ChannelSet DMXFrom="140/1" Name="Color 4 -&gt; Color 5" WheelSlotIndex="6"/>
                <ChannelSet DMXFrom="150/1" Name="Color 5 -&gt; Color 6" WheelSlotIndex="7"/>
                <ChannelSet DMXFrom="160/1" Name="Color 6 -&gt; Color 7" WheelSlotIndex="8"/>
                <ChannelSet DMXFrom="170/1" Name="Color 7 -&gt; Color 8" WheelSlotIndex="9"/>
                <ChannelSet DMXFrom="180/1" Name="Color 8 -&gt; Color 9" WheelSlotIndex="10"/>
                <ChannelSet DMXFrom="190/1" Name="Color 9 -&gt; Open" WheelSlotIndex="1"/>
              </ChannelFunction>
              <ChannelFunction Attribute="Color1WheelSpin" DMXFrom="201/1" Default="201/1" Name="Color Scroll" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000" Wheel="Color Wheel 1">
                <ChannelSet DMXFrom="201/1" Name="Scroll &lt;&lt;&lt;" WheelSlotIndex="1"/>
                <ChannelSet DMXFrom="228/1" Name="Stop" WheelSlotIndex="1"/>
                <ChannelSet DMXFrom="229/1" Name="Scroll &gt;&gt;&gt;" WheelSlotIndex="1"/>
              </ChannelFunction>
            </LogicalChannel>
          </DMXChannel>
          <DMXChannel DMXBreak="1" Geometry="Yoke" Highlight="32768/2" InitialFunction="Yoke_Pan.Pan.Pan" Offset="8,9">
            <LogicalChannel Attribute="Pan" DMXChangeTimeLimit="0.000000" Master="None" MibFade="0.000000" Snap="No">
              <ChannelFunction Attribute="Pan" DMXFrom="0/2" Default="32768/2" Name="Pan" OriginalAttribute="" PhysicalFrom="-270.000000" PhysicalTo="270.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="0/2" Name="-270" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="1/2" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="32768/2" Name="center" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="32769/2" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="65535/2" Name="270" WheelSlotIndex="0"/>
              </ChannelFunction>
            </LogicalChannel>
          </DMXChannel>
          <DMXChannel DMXBreak="1" Geometry="Yoke" Highlight="32768/2" InitialFunction="Yoke_Tilt.Tilt.Tilt" Offset="10,11">
            <LogicalChannel Attribute="Tilt" DMXChangeTimeLimit="0.000000" Master="None" MibFade="0.000000" Snap="No">
              <ChannelFunction Attribute="Tilt" DMXFrom="0/2" Default="32768/2" Name="Tilt" OriginalAttribute="" PhysicalFrom="-110.000000" PhysicalTo="110.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="0/2" Name="-120" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="1/2" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="32768/2" Name="center" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="32769/2" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="65535/2" Name="120" WheelSlotIndex="0"/>
              </ChannelFunction>
            </LogicalChannel>
          </DMXChannel>
          <DMXChannel DMXBreak="1" Geometry="Beam" Highlight="None" InitialFunction="Beam_Gobo1.Gobo1.Gobo Select" Offset="12">
            <LogicalChannel Attribute="Gobo1" DMXChangeTimeLimit="0.000000" Master="None" MibFade="0.000000" Snap="No">
              <ChannelFunction Attribute="Gobo1WheelIndex" DMXFrom="0/1" Default="0/1" Name="Gobo Select" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000" Wheel="Rotating Gobo Wheel">
                <ChannelSet DMXFrom="0/1" Name="Open" WheelSlotIndex="1"/>
                <ChannelSet DMXFrom="10/1" Name="Gobo 1" WheelSlotIndex="2"/>
                <ChannelSet DMXFrom="20/1" Name="Gobo 2" WheelSlotIndex="3"/>
                <ChannelSet DMXFrom="30/1" Name="Gobo 3" WheelSlotIndex="4"/>
                <ChannelSet DMXFrom="40/1" Name="Gobo 4" WheelSlotIndex="5"/>
                <ChannelSet DMXFrom="50/1" Name="Gobo 5" WheelSlotIndex="6"/>
                <ChannelSet DMXFrom="60/1" Name="Gobo 6" WheelSlotIndex="7"/>
              </ChannelFunction>
              <ChannelFunction Attribute="Gobo1SelectShake" DMXFrom="70/1" Default="100/1" Name="Gobo Shake" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000" Wheel="Rotating Gobo Wheel">
                <ChannelSet DMXFrom="70/1" Name="Open Shake S -&gt; F" WheelSlotIndex="1"/>
                <ChannelSet DMXFrom="80/1" Name="Gobo 1 Shake S -&gt; F" WheelSlotIndex="2"/>
                <ChannelSet DMXFrom="90/1" Name="Gobo 2 Shake S -&gt; F" WheelSlotIndex="3"/>
                <ChannelSet DMXFrom="100/1" Name="Gobo 3 Shake S -&gt; F" WheelSlotIndex="4"/>
                <ChannelSet DMXFrom="110/1" Name="Gobo 4 Shake S -&gt; F" WheelSlotIndex="5"/>
                <ChannelSet DMXFrom="120/1" Name="Gobo 5 Shake S -&gt; F" WheelSlotIndex="6"/>
                <ChannelSet DMXFrom="130/1" Name="Gobo 6 Shake S -&gt; F" WheelSlotIndex="7"/>
              </ChannelFunction>
              <ChannelFunction Attribute="Gobo1WheelSpin" DMXFrom="140/1" Default="140/1" Name="Gobo Scroll" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000" Wheel="Rotating Gobo Wheel">
                <ChannelSet DMXFrom="140/1" Name="&lt;&lt;&lt;" WheelSlotIndex="1"/>
                <ChannelSet DMXFrom="198/1" Name="&gt;&gt;&gt;" WheelSlotIndex="1"/>
              </ChannelFunction>
            </LogicalChannel>
          </DMXChannel>
          <DMXChannel DMXBreak="1" Geometry="Beam" Highlight="None" InitialFunction="Beam_Gobo1WheelIndex.Gobo1WheelIndex.Gobo Rotation" Offset="13,14">
            <LogicalChannel Attribute="Gobo1WheelIndex" DMXChangeTimeLimit="0.000000" Master="None" MibFade="0.000000" Snap="No">
              <ChannelFunction Attribute="Gobo1WheelIndex" DMXFrom="0/2" Default="0/2" Name="Gobo Rotation" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000" Wheel="Rotating Gobo Wheel">
                <ChannelSet DMXFrom="0/2" Name="No Index" WheelSlotIndex="1"/>
                <ChannelSet DMXFrom="512/2" Name="Index 0 -&gt; 620" WheelSlotIndex="1"/>
                <ChannelSet DMXFrom="49152/2" Name="&gt;&gt;&gt;" WheelSlotIndex="1"/>
                <ChannelSet DMXFrom="57088/2" Name="Stop" WheelSlotIndex="1"/>
                <ChannelSet DMXFrom="57600/2" Name="&lt;&lt;&lt;" WheelSlotIndex="1"/>
              </ChannelFunction>
            </LogicalChannel>
          </DMXChannel>
          <DMXChannel DMXBreak="1" Geometry="Beam" Highlight="None" InitialFunction="Beam_Gobo2.Gobo2.Gobo Select" Offset="15">
            <LogicalChannel Attribute="Gobo2" DMXChangeTimeLimit="0.000000" Master="None" MibFade="0.000000" Snap="No">
              <ChannelFunction Attribute="Gobo1WheelIndex" DMXFrom="0/1" Default="0/1" Name="Gobo Select" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000" Wheel="Static Gobo Wheel">
                <ChannelSet DMXFrom="0/1" Name="Open" WheelSlotIndex="1"/>
                <ChannelSet DMXFrom="10/1" Name="Gobo 1" WheelSlotIndex="2"/>
                <ChannelSet DMXFrom="20/1" Name="Gobo 2" WheelSlotIndex="3"/>
                <ChannelSet DMXFrom="30/1" Name="Gobo 3" WheelSlotIndex="4"/>
                <ChannelSet DMXFrom="40/1" Name="Gobo 4" WheelSlotIndex="5"/>
                <ChannelSet DMXFrom="50/1" Name="Gobo 5" WheelSlotIndex="6"/>
                <ChannelSet DMXFrom="60/1" Name="Gobo 6" WheelSlotIndex="7"/>
                <ChannelSet DMXFrom="70/1" Name="Gobo 7" WheelSlotIndex="8"/>
                <ChannelSet DMXFrom="80/1" Name="Gobo 8" WheelSlotIndex="9"/>
                <ChannelSet DMXFrom="90/1" Name="Gobo 9" WheelSlotIndex="10"/>
              </ChannelFunction>
              <ChannelFunction Attribute="Gobo1SelectShake" DMXFrom="100/1" Default="100/1" Name="Gobo Shake" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000" Wheel="Static Gobo Wheel">
                <ChannelSet DMXFrom="100/1" Name="Open Shake S -&gt; F" WheelSlotIndex="1"/>
                <ChannelSet DMXFrom="110/1" Name="Gobo 1 Shake S -&gt; F" WheelSlotIndex="2"/>
                <ChannelSet DMXFrom="120/1" Name="Gobo 2 Shake S -&gt; F" WheelSlotIndex="3"/>
                <ChannelSet DMXFrom="130/1" Name="Gobo 3 Shake S -&gt; F" WheelSlotIndex="4"/>
                <ChannelSet DMXFrom="140/1" Name="Gobo 4 Shake S -&gt; F" WheelSlotIndex="5"/>
                <ChannelSet DMXFrom="150/1" Name="Gobo 5 Shake S -&gt; F" WheelSlotIndex="6"/>
                <ChannelSet DMXFrom="160/1" Name="Gobo 6 Shake S -&gt; F" WheelSlotIndex="7"/>
                <ChannelSet DMXFrom="170/1" Name="Gobo 7 Shake S -&gt; F" WheelSlotIndex="8"/>
                <ChannelSet DMXFrom="180/1" Name="Gobo 8 Shake S -&gt; F" WheelSlotIndex="9"/>
                <ChannelSet DMXFrom="190/1" Name="Gobo 9 Shake S -&gt; F" WheelSlotIndex="10"/>
              </ChannelFunction>
              <ChannelFunction Attribute="Gobo1WheelSpin" DMXFrom="200/1" Default="200/1" Name="Gobo Scroll" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000" Wheel="Static Gobo Wheel">
                <ChannelSet DMXFrom="200/1" Name="&lt;&lt;&lt;" WheelSlotIndex="1"/>
                <ChannelSet DMXFrom="228/1" Name="&gt;&gt;&gt;" WheelSlotIndex="1"/>
              </ChannelFunction>
            </LogicalChannel>
          </DMXChannel>
          <DMXChannel DMXBreak="1" Geometry="Beam" Highlight="None" InitialFunction="Beam_Iris.Iris.Iris" Offset="16">
            <LogicalChannel Attribute="Iris" DMXChangeTimeLimit="0.000000" Master="None" MibFade="0.000000" Snap="No">
              <ChannelFunction Attribute="Iris" DMXFrom="0/1" Default="0/1" Name="Iris" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="0/1" Name="Open" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="1/1" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="200/1" Name="Close" WheelSlotIndex="0"/>
              </ChannelFunction>
              <ChannelFunction Attribute="IrisPulseOpen" DMXFrom="201/1" Default="201/1" Name="Pulse Open" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="201/1" Name="min" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="202/1" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="210/1" Name="max" WheelSlotIndex="0"/>
              </ChannelFunction>
              <ChannelFunction Attribute="IrisPulseClose" DMXFrom="211/1" Default="211/1" Name="Pulse Close" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="211/1" Name="min" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="212/1" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="220/1" Name="max" WheelSlotIndex="0"/>
              </ChannelFunction>
              <ChannelFunction Attribute="IrisRandomPulseOpen" DMXFrom="221/1" Default="221/1" Name="Random" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="221/1" Name="min" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="222/1" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="254/1" Name="max" WheelSlotIndex="0"/>
              </ChannelFunction>
              <ChannelFunction Attribute="Iris" DMXFrom="255/1" Default="255/1" Name="Open" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="255/1" Name="Open" WheelSlotIndex="0"/>
              </ChannelFunction>
            </LogicalChannel>
          </DMXChannel>
          <DMXChannel DMXBreak="1" Geometry="Beam" Highlight="None" InitialFunction="Beam_Prism1.Prism1.Prism" Offset="17">
            <LogicalChannel Attribute="Prism1" DMXChangeTimeLimit="0.000000" Master="None" MibFade="0.000000" Snap="No">
              <ChannelFunction Attribute="Prism1" DMXFrom="0/1" Default="0/1" Name="Prism" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000" Wheel="Prism">
                <ChannelSet DMXFrom="0/1" Name="Open" WheelSlotIndex="1"/>
                <ChannelSet DMXFrom="5/1" Name="Index" WheelSlotIndex="1"/>
                <ChannelSet DMXFrom="128/1" Name="Spin CCW" WheelSlotIndex="1"/>
                <ChannelSet DMXFrom="192/1" Name="Stop" WheelSlotIndex="1"/>
                <ChannelSet DMXFrom="193/1" Name="Spin CW" WheelSlotIndex="1"/>
              </ChannelFunction>
            </LogicalChannel>
          </DMXChannel>
          <DMXChannel DMXBreak="1" Geometry="Beam" Highlight="None" InitialFunction="Beam_Frost1.Frost1.Frost" Offset="18">
            <LogicalChannel Attribute="Frost1" DMXChangeTimeLimit="0.000000" Master="None" MibFade="0.000000" Snap="No">
              <ChannelFunction Attribute="Frost1" DMXFrom="0/1" Default="0/1" Name="Frost" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="0/1" Name="Open" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="6/1" Name="min" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="7/1" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="255/1" Name="max" WheelSlotIndex="0"/>
              </ChannelFunction>
            </LogicalChannel>
          </DMXChannel>
          <DMXChannel DMXBreak="1" Geometry="Beam" Highlight="None" InitialFunction="Beam_Zoom.Zoom.Zoom" Offset="19">
            <LogicalChannel Attribute="Zoom" DMXChangeTimeLimit="0.000000" Master="None" MibFade="0.000000" Snap="No">
              <ChannelFunction Attribute="Zoom" DMXFrom="0/1" Default="0/1" Name="Zoom" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="0/1" Name="min" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="1/1" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="255/1" Name="max" WheelSlotIndex="0"/>
              </ChannelFunction>
            </LogicalChannel>
          </DMXChannel>
          <DMXChannel DMXBreak="1" Geometry="Beam" Highlight="None" InitialFunction="Beam_Focus1.Focus1.Focus" Offset="20">
            <LogicalChannel Attribute="Focus1" DMXChangeTimeLimit="0.000000" Master="None" MibFade="0.000000" Snap="No">
              <ChannelFunction Attribute="Focus1" DMXFrom="0/1" Default="0/1" Name="Focus" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="0/1" Name="min" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="1/1" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="255/1" Name="max" WheelSlotIndex="0"/>
              </ChannelFunction>
            </LogicalChannel>
          </DMXChannel>
          <DMXChannel DMXBreak="1" Geometry="Head" Highlight="None" InitialFunction="Head_Reserved.Reserved.Macro" Offset="21">
            <LogicalChannel Attribute="Reserved" DMXChangeTimeLimit="0.000000" Master="None" MibFade="0.000000" Snap="No">
              <ChannelFunction Attribute="Reserved" DMXFrom="0/1" Default="0/1" Name="Macro" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000"/>
            </LogicalChannel>
          </DMXChannel>
          <DMXChannel DMXBreak="1" Geometry="Head" Highlight="None" InitialFunction="Head_Function.Function.Function" Offset="22">
            <LogicalChannel Attribute="Function" DMXChangeTimeLimit="0.000000" Master="None" MibFade="0.000000" Snap="No">
              <ChannelFunction Attribute="Function" DMXFrom="0/1" Default="0/1" Name="Function" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="0/1" Name="No Function" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="10/1" Name="Full Reset" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="20/1" Name="P/T Reset" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="30/1" Name="Zoom/Focus" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="40/1" Name="Gobo Wheel Reset" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="50/1" Name="Iris Reset" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="60/1" Name="CMY-CTO Reset" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="70/1" Name="Color Wheel Reset" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="80/1" Name="Frost" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="90/1" Name="Prism" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="100/1" Name="Fan Standard" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="110/1" Name="Fan Silent" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="120/1" Name="Fan Max" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="130/1" Name="Fan Full" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="140/1" Name="Reserved" WheelSlotIndex="0"/>
              </ChannelFunction>
            </LogicalChannel>
          </DMXChannel>
        </DMXChannels>
        <Relations/>
        <FTMacros/>
      </DMXMode>
      <DMXMode Geometry="Base" Name="Extended">
        <DMXChannels>
          <DMXChannel DMXBreak="1" Geometry="Beam" Highlight="8/1" Offset="1">
            <LogicalChannel Attribute="Shutter1" DMXChangeTimeLimit="0.000000" Master="None" MibFade="0.000000" Snap="No">
              <ChannelFunction Attribute="Shutter1" DMXFrom="0/1" Default="8/1" Name="Open" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="0/1" Name="Closed" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="5/1" Name="Open" WheelSlotIndex="0"/>
              </ChannelFunction>
              <ChannelFunction Attribute="Shutter1Strobe" DMXFrom="10/1" Default="10/1" Name="Strobe" OriginalAttribute="" PhysicalFrom="10.000000" PhysicalTo="0.100000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="10/1" Name="Slow" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="11/1" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="54/1" Name="Fast" WheelSlotIndex="0"/>
              </ChannelFunction>
              <ChannelFunction Attribute="Shutter1StrobeRandom" DMXFrom="55/1" Default="55/1" Name="Random" OriginalAttribute="" PhysicalFrom="0.100000" PhysicalTo="10.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="55/1" Name="Slow" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="56/1" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="79/1" Name="Fast" WheelSlotIndex="0"/>
              </ChannelFunction>
              <ChannelFunction Attribute="Shutter1StrobePulseOpen" DMXFrom="80/1" Default="80/1" Name="Pulse Up" OriginalAttribute="" PhysicalFrom="0.100000" PhysicalTo="10.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="80/1" Name="Slow" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="81/1" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="84/1" Name="Fast" WheelSlotIndex="0"/>
              </ChannelFunction>
              <ChannelFunction Attribute="Shutter1StrobePulseClose" DMXFrom="85/1" Default="85/1" Name="Pulse Down" OriginalAttribute="" PhysicalFrom="0.100000" PhysicalTo="10.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="85/1" Name="Slow" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="86/1" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="89/1" Name="Fast" WheelSlotIndex="0"/>
              </ChannelFunction>
              <ChannelFunction Attribute="Shutter1StrobePulse" DMXFrom="90/1" Default="90/1" Name="Pulse Up Down" OriginalAttribute="" PhysicalFrom="0.100000" PhysicalTo="10.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="90/1" Name="Slow" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="91/1" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="94/1" Name="Fast" WheelSlotIndex="0"/>
              </ChannelFunction>
              <ChannelFunction Attribute="Shutter1StrobeEffect" DMXFrom="95/1" Default="95/1" Name="Double Strobe" OriginalAttribute="" PhysicalFrom="0.100000" PhysicalTo="10.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="95/1" Name="Slow" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="96/1" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="139/1" Name="Fast" WheelSlotIndex="0"/>
              </ChannelFunction>
              <ChannelFunction Attribute="Shutter1StrobeEffect" DMXFrom="140/1" Default="140/1" Name="Triple Strobe" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="140/1" Name="Slow" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="141/1" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="184/1" Name="Fast" WheelSlotIndex="0"/>
              </ChannelFunction>
              <ChannelFunction Attribute="NoFeature" DMXFrom="185/1" Default="185/1" Name="Reserved" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000"/>
              <ChannelFunction Attribute="Shutter1StrobeEffect" DMXFrom="225/1" Default="225/1" Name="Fade Wave Up" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="225/1" Name="Slow" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="226/1" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="229/1" Name="Fast" WheelSlotIndex="0"/>
              </ChannelFunction>
              <ChannelFunction Attribute="Shutter1StrobeEffect" DMXFrom="230/1" Default="230/1" Name="Random Pixel" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="230/1" Name="Fast" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="231/1" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="234/1" Name="Slow" WheelSlotIndex="0"/>
              </ChannelFunction>
              <ChannelFunction Attribute="Shutter1StrobeEffect" DMXFrom="235/1" Default="235/1" Name="Wave Up Down" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="235/1" Name="Fast" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="236/1" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="239/1" Name="Slow" WheelSlotIndex="0"/>
              </ChannelFunction>
              <ChannelFunction Attribute="Shutter1StrobeEffect" DMXFrom="240/1" Default="240/1" Name="Wave Up" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="240/1" Name="Fast" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="241/1" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="244/1" Name="Slow" WheelSlotIndex="0"/>
              </ChannelFunction>
              <ChannelFunction Attribute="Shutter1StrobeEffect" DMXFrom="245/1" Default="245/1" Name="Wave Down" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="245/1" Name="Slow" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="246/1" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="249/1" Name="Fast" WheelSlotIndex="0"/>
              </ChannelFunction>
              <ChannelFunction Attribute="Shutter1" DMXFrom="250/1" Default="250/1" Name="Open (2)" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="250/1" Name="Open" WheelSlotIndex="0"/>
              </ChannelFunction>
            </LogicalChannel>
          </DMXChannel>
          <DMXChannel DMXBreak="1" Geometry="Beam" Highlight="25700/2" InitialFunction="Beam_Dimmer.Dimmer.Dimmer" Offset="2,3">
            <LogicalChannel Attribute="Dimmer" DMXChangeTimeLimit="0.000000" Master="Grand" MibFade="0.000000" Snap="No">
              <ChannelFunction Attribute="Dimmer" DMXFrom="0/2" Default="0/2" Name="Dimmer" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="0/2" Name="min" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="1/2" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="25700/2" Name="max" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="25701/2" Name="" WheelSlotIndex="0"/>
              </ChannelFunction>
            </LogicalChannel>
          </DMXChannel>
          <DMXChannel DMXBreak="1" Geometry="Beam" Highlight="None" InitialFunction="Beam_ColorRGB_Cyan.ColorRGB_Cyan.C" Offset="4,5">
            <LogicalChannel Attribute="ColorRGB_Cyan" DMXChangeTimeLimit="0.000000" Master="None" MibFade="0.000000" Snap="No">
              <ChannelFunction Attribute="ColorRGB_Cyan" DMXFrom="0/2" Default="0/2" Name="C" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="0/2" Name="min" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="1/2" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="65535/2" Name="max" WheelSlotIndex="0"/>
              </ChannelFunction>
            </LogicalChannel>
          </DMXChannel>
          <DMXChannel DMXBreak="1" Geometry="Beam" Highlight="None" InitialFunction="Beam_ColorRGB_Magenta.ColorRGB_Magenta.M" Offset="6,7">
            <LogicalChannel Attribute="ColorRGB_Magenta" DMXChangeTimeLimit="0.000000" Master="None" MibFade="0.000000" Snap="No">
              <ChannelFunction Attribute="ColorRGB_Magenta" DMXFrom="0/2" Default="0/2" Name="M" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="0/2" Name="min" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="1/2" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="65535/2" Name="max" WheelSlotIndex="0"/>
              </ChannelFunction>
            </LogicalChannel>
          </DMXChannel>
          <DMXChannel DMXBreak="1" Geometry="Beam" Highlight="None" InitialFunction="Beam_ColorRGB_Yellow.ColorRGB_Yellow.Y" Offset="8,9">
            <LogicalChannel Attribute="ColorRGB_Yellow" DMXChangeTimeLimit="0.000000" Master="None" MibFade="0.000000" Snap="No">
              <ChannelFunction Attribute="ColorRGB_Yellow" DMXFrom="0/2" Default="0/2" Name="Y" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="0/2" Name="min" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="1/2" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="65535/2" Name="max" WheelSlotIndex="0"/>
              </ChannelFunction>
            </LogicalChannel>
          </DMXChannel>
          <DMXChannel DMXBreak="1" Geometry="Beam" Highlight="None" InitialFunction="Beam_CTO.CTO.CTO" Offset="10,11">
            <LogicalChannel Attribute="CTO" DMXChangeTimeLimit="0.000000" Master="None" MibFade="0.000000" Snap="No">
              <ChannelFunction Attribute="CTO" DMXFrom="0/2" Default="0/2" Name="CTO" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="0/2" Name="min" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="1/2" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="62708/2" Name="max" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="62709/2" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="62965/2" Name="CTB" WheelSlotIndex="0"/>
              </ChannelFunction>
            </LogicalChannel>
          </DMXChannel>
          <DMXChannel DMXBreak="1" Geometry="Beam" Highlight="None" InitialFunction="Beam_Color1.Color1.Fixed Colors" Offset="12">
            <LogicalChannel Attribute="Color1" DMXChangeTimeLimit="0.000000" Master="None" MibFade="0.000000" Snap="No">
              <ChannelFunction Attribute="Color1" DMXFrom="0/1" Default="0/1" Name="Fixed Colors" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000" Wheel="Color Wheel 1">
                <ChannelSet DMXFrom="0/1" Name="Open" WheelSlotIndex="1"/>
                <ChannelSet DMXFrom="10/1" Name="Color 1" WheelSlotIndex="2"/>
                <ChannelSet DMXFrom="20/1" Name="Color 2" WheelSlotIndex="3"/>
                <ChannelSet DMXFrom="30/1" Name="Color 3" WheelSlotIndex="4"/>
                <ChannelSet DMXFrom="40/1" Name="Color 4" WheelSlotIndex="5"/>
                <ChannelSet DMXFrom="50/1" Name="Color 5" WheelSlotIndex="6"/>
                <ChannelSet DMXFrom="60/1" Name="Color 6" WheelSlotIndex="7"/>
                <ChannelSet DMXFrom="70/1" Name="Color 7" WheelSlotIndex="8"/>
                <ChannelSet DMXFrom="80/1" Name="Color 8" WheelSlotIndex="9"/>
                <ChannelSet DMXFrom="90/1" Name="Color 9" WheelSlotIndex="10"/>
              </ChannelFunction>
              <ChannelFunction Attribute="Color1WheelIndex" DMXFrom="100/1" Default="100/1" Name="Split Colors" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000" Wheel="Color Wheel 1">
                <ChannelSet DMXFrom="100/1" Name="Open" WheelSlotIndex="1"/>
                <ChannelSet DMXFrom="101/1" Name="Open -&gt; Color 1" WheelSlotIndex="2"/>
                <ChannelSet DMXFrom="110/1" Name="Color 1 -&gt; Color 2" WheelSlotIndex="3"/>
                <ChannelSet DMXFrom="120/1" Name="Color 2 -&gt; Color 3" WheelSlotIndex="4"/>
                <ChannelSet DMXFrom="130/1" Name="Color 3 -&gt; Color 4" WheelSlotIndex="5"/>
                <ChannelSet DMXFrom="140/1" Name="Color 4 -&gt; Color 5" WheelSlotIndex="6"/>
                <ChannelSet DMXFrom="150/1" Name="Color 5 -&gt; Color 6" WheelSlotIndex="7"/>
                <ChannelSet DMXFrom="160/1" Name="Color 6 -&gt; Color 7" WheelSlotIndex="8"/>
                <ChannelSet DMXFrom="170/1" Name="Color 7 -&gt; Color 8" WheelSlotIndex="9"/>
                <ChannelSet DMXFrom="180/1" Name="Color 8 -&gt; Color 9" WheelSlotIndex="10"/>
                <ChannelSet DMXFrom="190/1" Name="Color 9 -&gt; Open" WheelSlotIndex="1"/>
              </ChannelFunction>
              <ChannelFunction Attribute="Color1WheelSpin" DMXFrom="201/1" Default="201/1" Name="Color Scroll" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000" Wheel="Color Wheel 1">
                <ChannelSet DMXFrom="201/1" Name="Scroll &lt;&lt;&lt;" WheelSlotIndex="1"/>
                <ChannelSet DMXFrom="228/1" Name="Stop" WheelSlotIndex="1"/>
                <ChannelSet DMXFrom="229/1" Name="Scroll &gt;&gt;&gt;" WheelSlotIndex="1"/>
              </ChannelFunction>
            </LogicalChannel>
          </DMXChannel>
          <DMXChannel DMXBreak="1" Geometry="Yoke" Highlight="32768/2" InitialFunction="Yoke_Pan.Pan.Pan" Offset="13,14">
            <LogicalChannel Attribute="Pan" DMXChangeTimeLimit="0.000000" Master="None" MibFade="0.000000" Snap="No">
              <ChannelFunction Attribute="Pan" DMXFrom="0/2" Default="32768/2" Name="Pan" OriginalAttribute="" PhysicalFrom="-270.000000" PhysicalTo="270.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="0/2" Name="-270" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="1/2" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="32768/2" Name="center" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="32769/2" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="65535/2" Name="270" WheelSlotIndex="0"/>
              </ChannelFunction>
            </LogicalChannel>
          </DMXChannel>
          <DMXChannel DMXBreak="1" Geometry="Yoke" Highlight="32768/2" InitialFunction="Yoke_Tilt.Tilt.Tilt" Offset="15,16">
            <LogicalChannel Attribute="Tilt" DMXChangeTimeLimit="0.000000" Master="None" MibFade="0.000000" Snap="No">
              <ChannelFunction Attribute="Tilt" DMXFrom="0/2" Default="32768/2" Name="Tilt" OriginalAttribute="" PhysicalFrom="-110.000000" PhysicalTo="110.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="0/2" Name="-120" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="1/2" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="32768/2" Name="center" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="32769/2" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="65535/2" Name="120" WheelSlotIndex="0"/>
              </ChannelFunction>
            </LogicalChannel>
          </DMXChannel>
          <DMXChannel DMXBreak="1" Geometry="Beam" Highlight="None" InitialFunction="Beam_Gobo1.Gobo1.Gobo Select" Offset="17">
            <LogicalChannel Attribute="Gobo1" DMXChangeTimeLimit="0.000000" Master="None" MibFade="0.000000" Snap="No">
              <ChannelFunction Attribute="Gobo1WheelIndex" DMXFrom="0/1" Default="0/1" Name="Gobo Select" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000" Wheel="Rotating Gobo Wheel">
                <ChannelSet DMXFrom="0/1" Name="Open" WheelSlotIndex="1"/>
                <ChannelSet DMXFrom="10/1" Name="Gobo 1" WheelSlotIndex="2"/>
                <ChannelSet DMXFrom="20/1" Name="Gobo 2" WheelSlotIndex="3"/>
                <ChannelSet DMXFrom="30/1" Name="Gobo 3" WheelSlotIndex="4"/>
                <ChannelSet DMXFrom="40/1" Name="Gobo 4" WheelSlotIndex="5"/>
                <ChannelSet DMXFrom="50/1" Name="Gobo 5" WheelSlotIndex="6"/>
                <ChannelSet DMXFrom="60/1" Name="Gobo 6" WheelSlotIndex="7"/>
              </ChannelFunction>
              <ChannelFunction Attribute="Gobo1SelectShake" DMXFrom="70/1" Default="100/1" Name="Gobo Shake" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000" Wheel="Rotating Gobo Wheel">
                <ChannelSet DMXFrom="70/1" Name="Open Shake S -&gt; F" WheelSlotIndex="1"/>
                <ChannelSet DMXFrom="80/1" Name="Gobo 1 Shake S -&gt; F" WheelSlotIndex="2"/>
                <ChannelSet DMXFrom="90/1" Name="Gobo 2 Shake S -&gt; F" WheelSlotIndex="3"/>
                <ChannelSet DMXFrom="100/1" Name="Gobo 3 Shake S -&gt; F" WheelSlotIndex="4"/>
                <ChannelSet DMXFrom="110/1" Name="Gobo 4 Shake S -&gt; F" WheelSlotIndex="5"/>
                <ChannelSet DMXFrom="120/1" Name="Gobo 5 Shake S -&gt; F" WheelSlotIndex="6"/>
                <ChannelSet DMXFrom="130/1" Name="Gobo 6 Shake S -&gt; F" WheelSlotIndex="7"/>
              </ChannelFunction>
              <ChannelFunction Attribute="Gobo1WheelSpin" DMXFrom="140/1" Default="140/1" Name="Gobo Scroll" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000" Wheel="Rotating Gobo Wheel">
                <ChannelSet DMXFrom="140/1" Name="&lt;&lt;&lt;" WheelSlotIndex="1"/>
                <ChannelSet DMXFrom="198/1" Name="&gt;&gt;&gt;" WheelSlotIndex="1"/>
              </ChannelFunction>
            </LogicalChannel>
          </DMXChannel>
          <DMXChannel DMXBreak="1" Geometry="Beam" Highlight="None" InitialFunction="Beam_Gobo1WheelIndex.Gobo1WheelIndex.Gobo Rotation" Offset="18,19">
            <LogicalChannel Attribute="Gobo1WheelIndex" DMXChangeTimeLimit="0.000000" Master="None" MibFade="0.000000" Snap="No">
              <ChannelFunction Attribute="Gobo1WheelIndex" DMXFrom="0/2" Default="0/2" Name="Gobo Rotation" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000" Wheel="Rotating Gobo Wheel">
                <ChannelSet DMXFrom="0/2" Name="No Index" WheelSlotIndex="1"/>
                <ChannelSet DMXFrom="512/2" Name="Index 0 -&gt; 620" WheelSlotIndex="1"/>
                <ChannelSet DMXFrom="49152/2" Name="&gt;&gt;&gt;" WheelSlotIndex="1"/>
                <ChannelSet DMXFrom="57088/2" Name="Stop" WheelSlotIndex="1"/>
                <ChannelSet DMXFrom="57600/2" Name="&lt;&lt;&lt;" WheelSlotIndex="1"/>
              </ChannelFunction>
            </LogicalChannel>
          </DMXChannel>
          <DMXChannel DMXBreak="1" Geometry="Beam" Highlight="None" InitialFunction="Beam_Gobo2.Gobo2.Gobo Select" Offset="20">
            <LogicalChannel Attribute="Gobo2" DMXChangeTimeLimit="0.000000" Master="None" MibFade="0.000000" Snap="No">
              <ChannelFunction Attribute="Gobo1WheelIndex" DMXFrom="0/1" Default="0/1" Name="Gobo Select" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000" Wheel="Static Gobo Wheel">
                <ChannelSet DMXFrom="0/1" Name="Open" WheelSlotIndex="1"/>
                <ChannelSet DMXFrom="10/1" Name="Gobo 1" WheelSlotIndex="2"/>
                <ChannelSet DMXFrom="20/1" Name="Gobo 2" WheelSlotIndex="3"/>
                <ChannelSet DMXFrom="30/1" Name="Gobo 3" WheelSlotIndex="4"/>
                <ChannelSet DMXFrom="40/1" Name="Gobo 4" WheelSlotIndex="5"/>
                <ChannelSet DMXFrom="50/1" Name="Gobo 5" WheelSlotIndex="6"/>
                <ChannelSet DMXFrom="60/1" Name="Gobo 6" WheelSlotIndex="7"/>
                <ChannelSet DMXFrom="70/1" Name="Gobo 7" WheelSlotIndex="8"/>
                <ChannelSet DMXFrom="80/1" Name="Gobo 8" WheelSlotIndex="9"/>
                <ChannelSet DMXFrom="90/1" Name="Gobo 9" WheelSlotIndex="10"/>
              </ChannelFunction>
              <ChannelFunction Attribute="Gobo1SelectShake" DMXFrom="100/1" Default="100/1" Name="Gobo Shake" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000" Wheel="Static Gobo Wheel">
                <ChannelSet DMXFrom="100/1" Name="Open Shake S -&gt; F" WheelSlotIndex="1"/>
                <ChannelSet DMXFrom="110/1" Name="Gobo 1 Shake S -&gt; F" WheelSlotIndex="2"/>
                <ChannelSet DMXFrom="120/1" Name="Gobo 2 Shake S -&gt; F" WheelSlotIndex="3"/>
                <ChannelSet DMXFrom="130/1" Name="Gobo 3 Shake S -&gt; F" WheelSlotIndex="4"/>
                <ChannelSet DMXFrom="140/1" Name="Gobo 4 Shake S -&gt; F" WheelSlotIndex="5"/>
                <ChannelSet DMXFrom="150/1" Name="Gobo 5 Shake S -&gt; F" WheelSlotIndex="6"/>
                <ChannelSet DMXFrom="160/1" Name="Gobo 6 Shake S -&gt; F" WheelSlotIndex="7"/>
                <ChannelSet DMXFrom="170/1" Name="Gobo 7 Shake S -&gt; F" WheelSlotIndex="8"/>
                <ChannelSet DMXFrom="180/1" Name="Gobo 8 Shake S -&gt; F" WheelSlotIndex="9"/>
                <ChannelSet DMXFrom="190/1" Name="Gobo 9 Shake S -&gt; F" WheelSlotIndex="10"/>
              </ChannelFunction>
              <ChannelFunction Attribute="Gobo1WheelSpin" DMXFrom="200/1" Default="200/1" Name="Gobo Scroll" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000" Wheel="Static Gobo Wheel">
                <ChannelSet DMXFrom="200/1" Name="&lt;&lt;&lt;" WheelSlotIndex="1"/>
                <ChannelSet DMXFrom="228/1" Name="&gt;&gt;&gt;" WheelSlotIndex="1"/>
              </ChannelFunction>
            </LogicalChannel>
          </DMXChannel>
          <DMXChannel DMXBreak="1" Geometry="Beam" Highlight="None" InitialFunction="Beam_Iris.Iris.Iris" Offset="21">
            <LogicalChannel Attribute="Iris" DMXChangeTimeLimit="0.000000" Master="None" MibFade="0.000000" Snap="No">
              <ChannelFunction Attribute="Iris" DMXFrom="0/1" Default="0/1" Name="Iris" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="0/1" Name="Open" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="1/1" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="200/1" Name="Close" WheelSlotIndex="0"/>
              </ChannelFunction>
              <ChannelFunction Attribute="IrisPulseOpen" DMXFrom="201/1" Default="201/1" Name="Pulse Open" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="201/1" Name="min" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="202/1" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="210/1" Name="max" WheelSlotIndex="0"/>
              </ChannelFunction>
              <ChannelFunction Attribute="IrisPulseClose" DMXFrom="211/1" Default="211/1" Name="Pulse Close" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="211/1" Name="min" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="212/1" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="220/1" Name="max" WheelSlotIndex="0"/>
              </ChannelFunction>
              <ChannelFunction Attribute="IrisRandomPulseOpen" DMXFrom="221/1" Default="221/1" Name="Random" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="221/1" Name="min" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="222/1" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="254/1" Name="max" WheelSlotIndex="0"/>
              </ChannelFunction>
              <ChannelFunction Attribute="Iris" DMXFrom="255/1" Default="255/1" Name="Open" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="255/1" Name="Open" WheelSlotIndex="0"/>
              </ChannelFunction>
            </LogicalChannel>
          </DMXChannel>
          <DMXChannel DMXBreak="1" Geometry="Beam" Highlight="None" InitialFunction="Beam_Prism1.Prism1.Prism" Offset="22">
            <LogicalChannel Attribute="Prism1" DMXChangeTimeLimit="0.000000" Master="None" MibFade="0.000000" Snap="No">
              <ChannelFunction Attribute="Prism1" DMXFrom="0/1" Default="0/1" Name="Prism" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000" Wheel="Prism">
                <ChannelSet DMXFrom="0/1" Name="Open" WheelSlotIndex="1"/>
                <ChannelSet DMXFrom="5/1" Name="Index" WheelSlotIndex="1"/>
                <ChannelSet DMXFrom="128/1" Name="Spin CCW" WheelSlotIndex="1"/>
                <ChannelSet DMXFrom="192/1" Name="Stop" WheelSlotIndex="1"/>
                <ChannelSet DMXFrom="193/1" Name="Spin CW" WheelSlotIndex="1"/>
              </ChannelFunction>
            </LogicalChannel>
          </DMXChannel>
          <DMXChannel DMXBreak="1" Geometry="Beam" Highlight="None" InitialFunction="Beam_Frost1.Frost1.Frost" Offset="23">
            <LogicalChannel Attribute="Frost1" DMXChangeTimeLimit="0.000000" Master="None" MibFade="0.000000" Snap="No">
              <ChannelFunction Attribute="Frost1" DMXFrom="0/1" Default="0/1" Name="Frost" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="0/1" Name="Open" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="6/1" Name="min" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="7/1" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="255/1" Name="max" WheelSlotIndex="0"/>
              </ChannelFunction>
            </LogicalChannel>
          </DMXChannel>
          <DMXChannel DMXBreak="1" Geometry="Beam" Highlight="None" InitialFunction="Beam_Zoom.Zoom.Zoom" Offset="24,25">
            <LogicalChannel Attribute="Zoom" DMXChangeTimeLimit="0.000000" Master="None" MibFade="0.000000" Snap="No">
              <ChannelFunction Attribute="Zoom" DMXFrom="0/2" Default="0/2" Name="Zoom" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="0/2" Name="min" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="1/2" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="65535/2" Name="max" WheelSlotIndex="0"/>
              </ChannelFunction>
            </LogicalChannel>
          </DMXChannel>
          <DMXChannel DMXBreak="1" Geometry="Beam" Highlight="None" InitialFunction="Beam_Focus1.Focus1.Focus" Offset="26,27">
            <LogicalChannel Attribute="Focus1" DMXChangeTimeLimit="0.000000" Master="None" MibFade="0.000000" Snap="No">
              <ChannelFunction Attribute="Focus1" DMXFrom="0/2" Default="0/2" Name="Focus" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="0/2" Name="min" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="1/2" Name="" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="65535/2" Name="max" WheelSlotIndex="0"/>
              </ChannelFunction>
            </LogicalChannel>
          </DMXChannel>
          <DMXChannel DMXBreak="1" Geometry="Head" Highlight="None" InitialFunction="Head_Reserved.Reserved.Macro" Offset="28">
            <LogicalChannel Attribute="Reserved" DMXChangeTimeLimit="0.000000" Master="None" MibFade="0.000000" Snap="No">
              <ChannelFunction Attribute="Reserved" DMXFrom="0/1" Default="0/1" Name="Macro" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000"/>
            </LogicalChannel>
          </DMXChannel>
          <DMXChannel DMXBreak="1" Geometry="Head" Highlight="None" InitialFunction="Head_Function.Function.Function" Offset="29">
            <LogicalChannel Attribute="Function" DMXChangeTimeLimit="0.000000" Master="None" MibFade="0.000000" Snap="No">
              <ChannelFunction Attribute="Function" DMXFrom="0/1" Default="0/1" Name="Function" OriginalAttribute="" PhysicalFrom="0.000000" PhysicalTo="1.000000" RealAcceleration="0.000000" RealFade="0.000000">
                <ChannelSet DMXFrom="0/1" Name="No Function" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="10/1" Name="Full Reset" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="20/1" Name="P/T Reset" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="30/1" Name="Zoom/Focus" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="40/1" Name="Gobo Wheel Reset" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="50/1" Name="Iris Reset" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="60/1" Name="CMY-CTO Reset" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="70/1" Name="Color Wheel Reset" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="80/1" Name="Frost" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="90/1" Name="Prism" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="100/1" Name="Fan Standard" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="110/1" Name="Fan Silent" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="120/1" Name="Fan Max" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="130/1" Name="Fan Full" WheelSlotIndex="0"/>
                <ChannelSet DMXFrom="140/1" Name="Reserved" WheelSlotIndex="0"/>
              </ChannelFunction>
            </LogicalChannel>
          </DMXChannel>
        </DMXChannels>
        <Relations/>
        <FTMacros/>
      </DMXMode>
    </DMXModes>
    <Revisions>
      <Revision Date="2020-12-21T12:13:43" Text="Rev B" UserID="0"/>
      <Revision Date="2021-02-02T12:55:40" Text="Rev A" UserID="0"/>
      <Revision Date="2021-02-03T14:43:21" Text="Rev A" UserID="0"/>
    <Revision Text="Rev A, Uploaded to GDTF Share." Date="2021-02-03T15:48:55" UserID="1170"/></Revisions>
    <FTPresets/>
    <Protocols>
      <FTRDM DeviceModelID="0x98" ManufacturerID="0x0"/>
    </Protocols>
  </FixtureType>

</GDTF>
