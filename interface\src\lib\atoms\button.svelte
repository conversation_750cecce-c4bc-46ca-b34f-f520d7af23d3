<script lang="ts">
    import type { Snippet } from "svelte";

    let {
        id,
        onclick,
        grow = false,
        disabled = false,
        small = false,
        children,
    }: {
        id: string;
        onclick: () => any;
        disabled?: boolean;
        small?: boolean;
        grow?: boolean;
        children: Snippet;
    } = $props();
</script>

<button
    {id}
    class="transition:name w-full rounded-lg border border-input drop-shadow focus:outline-none focus:ring-2 focus:ring-accent
    {small ? 'h-fit px-1' : 'px-4 py-2'}
    {grow ? 'grow' : ''}
    {disabled
        ? 'bg-surface text-disabled'
        : 'bg-accent text-complementary hover:text-secondary dark:bg-input dark:text-primary dark:hover:bg-accent dark:hover:text-complementary'}"
    onclick={() => onclick()}
    {disabled}
>
    {@render children()}
</button>
