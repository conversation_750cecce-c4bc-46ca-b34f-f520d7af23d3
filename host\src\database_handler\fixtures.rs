use crate::dmx_renderer::{
    channel::{
        color_channels::{
            CmyChannels, ColorChannel, ColorWheel, DmxFixtureColor,
            HsvChannels, RgbChannels, RgbColor, XyYChannels,
        },
        movement_channels::{MovementChannels, PAN_DEFAULT, TILT_DEFAULT},
        unimplemented_channels::{Keypoint, UnimplementedChannel},
        DmxChannelValue,
    },
    fixture::DmxFixtureFileTypeDescriptor,
};
use crate::logging;
use mysql::{prelude::*, PooledConn};

use super::DbHandler;

pub fn shallow_fixtures_for_active_show(
    db_connection: &mut PooledConn,
) -> Vec<DmxFixtureFileTypeDescriptor> {
    db_connection
        .query_map(
            "
                    SELECT
                        fixtures.id,
                        dmx_address,
                        dmx_universe,
                        fixtures.name,
                        fixturetype,
                        stage_coordinate_x,
                        stage_coordinate_y,
                        footprint_size
                    FROM fixtures
                    JOIN shows ON shows.id = fixtures.show_id
                    WHERE shows.active
                ",
            |(
                id,
                dmx_address,
                dmx_universe,
                name,
                fixturetype,
                stage_coordinates_x,
                stage_coordinates_y,
                footprint_size,
            )| {
                DmxFixtureFileDescriptor {
                    id,
                    dmx_address,
                    dmx_universe,
                    name,
                    fixturetype,
                    movement_channels: None,
                    stage_coordinates: (
                        stage_coordinates_x,
                        stage_coordinates_y,
                    ),
                    footprint_size,
                    color: None,
                    unimplemented_channels: vec![],
                }
            },
        )
        .unwrap_or_else(|err| {
            logging::log(
                format!("{err:?}\n        (selecting selecting fixtures)"),
                logging::LogLevel::DbError,
                true,
            );
            vec![]
        })
}

pub fn saturate_dmx_channel_keypoints(
    db_connection: &mut PooledConn,
    fixture: &mut DmxFixtureFileDescriptor,
) {
    for channel in &mut fixture.unimplemented_channels {
        if let Some(channel_id) = channel.id {
            db_connection
                .query_map(
                    format!(
                        "
                            SELECT
                                kp.id,
                                kp.name,
                                kp.value
                            FROM keypoints kp
                            JOIN dmx_channels ch ON kp.dmx_channel_id = ch.id
                            WHERE ch.id = {channel_id}
                        "
                    ),
                    |(id, name, value): (usize, String, u8)| {
                        channel.keypoints.push(Keypoint {
                            id: Some(id),
                            name,
                            value,
                        });
                    },
                )
                .unwrap_or_else(|err| {
                    logging::log(
                        format!(
                        "{err:?}\n        (selecting keypoints for dmx_channel)"
                    ),
                        logging::LogLevel::DbError,
                        true,
                    );
                    vec![]
                });
        }
    }
}

pub fn saturate_movement_channels_for_fixture(
    db_connection: &mut PooledConn,
    fixture_id: usize,
    fixture: &mut DmxFixtureFileDescriptor,
) {
    db_connection.query_map(
                    format!(
                        "
                            SELECT
                                dmx_channels.dmx_offset,
                                dmx_channels.name
                            FROM dmx_channels
                            JOIN fixtures ON fixtures.id = fixture_id
                            WHERE fixtures.id = fixture_id AND fixtures.id = {fixture_id}
                              AND (dmx_channels.name = 'MovementChannelsPan' OR dmx_channels.name = 'MovementChannelsTilt')
                        "
                    ),
                    |(
                        channel_offset,
                        channel_name,
                    ): (usize, String)| {
                        match channel_name.as_str() {
                            "MovementChannelsPan" => {
                                if let Some(ref mut mv_channels) =
                                    &mut fixture.movement_channels
                                {
                                    mv_channels.pan = Some(DmxChannelValue {
                                        channel: channel_offset,
                                        value: 0,
                                    });
                                } else {
                                    fixture.movement_channels =
                                        Some(MovementChannels {
                                            pan: Some(DmxChannelValue {
                                                channel: channel_offset,
                                                value: 0,
                                            }),
                                            tilt: None,
                                            pan_spline: None,
                                            tilt_spline: None,
                                            pan_origin: PAN_DEFAULT,
                                            tilt_origin: TILT_DEFAULT,
                                            pan_oneshot: false,
                                            tilt_oneshot: false,
                                            pan_blueprint_id: None,
                                            tilt_blueprint_id: None,
                                        });
                                }
                            }
                            "MovementChannelsTilt" => {
                                if let Some(ref mut mv_channels) =
                                    &mut fixture.movement_channels
                                {
                                    mv_channels.tilt = Some(DmxChannelValue {
                                        channel: channel_offset,
                                        value: 0,
                                    });
                                } else {
                                    fixture.movement_channels =
                                        Some(MovementChannels {
                                            pan: None,
                                            tilt: Some(DmxChannelValue {
                                                channel: channel_offset,
                                                value: 0,
                                            }),
                                            pan_spline: None,
                                            tilt_spline: None,
                                            pan_origin: PAN_DEFAULT,
                                            tilt_origin: TILT_DEFAULT,
                                            pan_oneshot: false,
                                            tilt_oneshot: false,
                                            pan_blueprint_id: None,
                                            tilt_blueprint_id: None,
                                        });
                                }
                            }
                            e => {
                                logging::log(format!("Encountered some unknown channel ({e:?}) while querying for Pan/Tilt channels"), logging::LogLevel::Warning, true);
                            }
                        }
                    },
                )
                .unwrap_or_else(|err| {
                    logging::log(
                        format!(
                            "{err:?}\n        (selecting movement channels for fixture)"
                        ),
                        logging::LogLevel::DbError,
                        true,
                    );
                    vec![]
                });
}

#[allow(clippy::too_many_lines)]
pub fn saturate_dmx_channels_for_fixture(
    db_connection: &mut PooledConn,
    fixture_id: usize,
    fixture: &mut DmxFixtureFileDescriptor,
) {
    db_connection.query_map(
                    format!(
                        "
                            SELECT
                                dmx_channels.id,
                                dmx_channels.dmx_offset,
                                dmx_channels.name,
                                dimmable,
                                inverted,
                                default_value
                            FROM dmx_channels
                            JOIN fixtures ON fixtures.id = fixture_id
                            WHERE fixtures.id = fixture_id AND fixtures.id = {fixture_id}
                              AND dmx_channels.name != 'MovementChannelsPan'
                              AND dmx_channels.name != 'MovementChannelsTilt'
                        "
                    ),
                    |(
                        channel_id,
                        channel_offset,
                        channel_name,
                        dimmable,
                        inverted,
                        default_value,
                    ): (usize, usize, String, bool, bool, u8)| {
                        fixture.unimplemented_channels.push(
                            UnimplementedChannel {
                                id: Some(channel_id),
                                channel: channel_offset,
                                name: channel_name,
                                dimmable,
                                inverted,
                                default: default_value,
                                keypoints: vec![],
                                value: 0,
                                spline: None,
                                oneshot: false,
                                blueprint_id: None,
                            },
                        );
                    },
                )
                .unwrap_or_else(|err| {
                    logging::log(
                        format!(
                            "{err:?}\n        (selecting unimplemented channels for fixture)"
                        ),
                        logging::LogLevel::DbError,
                        true,
                    );
                    vec![]
                });
}

pub fn saturate_color_channels_rgb_for_fixture(
    db_connection: &mut PooledConn,
    fixture_id: usize,
    fixture: &mut DmxFixtureFileDescriptor,
) {
    db_connection
        .query_map(
            format!(
                "
                    SELECT
                        rcc.red_channel_offset,
                        rcc.green_channel_offset,
                        rcc.blue_channel_offset
                    FROM rgb_color_channels rcc
                    JOIN fixtures fx ON fx.id = rcc.fixture_id
                    WHERE fx.id = {fixture_id}
                "
            ),
            |(
                red_channel_offset,
                green_channel_offset,
                blue_channel_offset,
            ): (Option<u8>, Option<u8>, Option<u8>)| {
                fixture.color = Some(DmxFixtureColor {
                    color: RgbColor::new_black(),
                    channels: ColorChannel::RgbChannels(RgbChannels {
                        r: red_channel_offset,
                        g: green_channel_offset,
                        b: blue_channel_offset,
                    }),
                    hue_spline: None,
                    saturation_spline: None,
                    oneshot: true,
                    blueprint_id: None,
                });
            },
        )
        .unwrap_or_else(|err| {
            logging::log(
                format!("{err:?}\n        (selecting rgb_color_channels)"),
                logging::LogLevel::DbError,
                true,
            );
            vec![]
        });
}

pub fn saturate_color_channels_cmy_for_fixture(
    db_connection: &mut PooledConn,
    fixture_id: usize,
    fixture: &mut DmxFixtureFileDescriptor,
) {
    db_connection
        .query_map(
            format!(
                "
                    SELECT
                        ccc.cyan_channel_offset,
                        ccc.magenta_channel_offset,
                        ccc.yellow_channel_offset
                    FROM cmy_color_channels ccc
                    JOIN fixtures fx ON fx.id = ccc.fixture_id
                    WHERE fx.id = {fixture_id}
                "
            ),
            |(
                cyan_channel_offset,
                magenta_channel_offset,
                yellow_channel_offset,
            ): (Option<u8>, Option<u8>, Option<u8>)| {
                fixture.color = Some(DmxFixtureColor {
                    color: RgbColor::new_black(),
                    channels: ColorChannel::CmyChannels(CmyChannels {
                        c: cyan_channel_offset,
                        m: magenta_channel_offset,
                        y: yellow_channel_offset,
                    }),
                    hue_spline: None,
                    saturation_spline: None,
                    oneshot: true,
                    blueprint_id: None,
                });
            },
        )
        .unwrap_or_else(|err| {
            logging::log(
                format!("{err:?}\n        (selecting cmy_color_channels)"),
                logging::LogLevel::DbError,
                true,
            );
            vec![]
        });
}

pub fn saturate_color_channels_hsv_for_fixture(
    db_connection: &mut PooledConn,
    fixture_id: usize,
    fixture: &mut DmxFixtureFileDescriptor,
) {
    db_connection
        .query_map(
            format!(
                "
                    SELECT
                        hcc.hue_channel_offset,
                        hcc.saturation_channel_offset,
                        hcc.value_channel_offset
                    FROM hsv_color_channels hcc
                    JOIN fixtures fx ON fx.id = hcc.fixture_id
                    WHERE fx.id = {fixture_id}
                "
            ),
            |(
                hue_channel_offset,
                saturation_channel_offset,
                value_channel_offset,
            ): (Option<u8>, Option<u8>, Option<u8>)| {
                fixture.color = Some(DmxFixtureColor {
                    color: RgbColor::new_black(),
                    channels: ColorChannel::HsvChannels(HsvChannels {
                        h: hue_channel_offset,
                        s: saturation_channel_offset,
                        v: value_channel_offset,
                    }),
                    hue_spline: None,
                    saturation_spline: None,
                    oneshot: true,
                    blueprint_id: None,
                });
            },
        )
        .unwrap_or_else(|err| {
            logging::log(
                format!("{err:?}\n        (selecting hsv_color_channels)"),
                logging::LogLevel::DbError,
                true,
            );
            vec![]
        });
}

pub fn saturate_color_channels_xyz_for_fixture(
    db_connection: &mut PooledConn,
    fixture_id: usize,
    fixture: &mut DmxFixtureFileDescriptor,
) {
    db_connection
        .query_map(
            format!(
                "
                    SELECT
                        xcc.x_channel_offset,
                        xcc.y_channel_offset,
                        xcc.z_channel_offset
                    FROM xyz_color_channels xcc
                    JOIN fixtures fx ON fx.id = xcc.fixture_id
                    WHERE fx.id = {fixture_id}
                "
            ),
            |(x_channel_offset, y_channel_offset, z_channel_offset): (
                Option<u8>,
                Option<u8>,
                Option<u8>,
            )| {
                fixture.color = Some(DmxFixtureColor {
                    color: RgbColor::new_black(),
                    channels: ColorChannel::XyzChannels(XyYChannels {
                        x: x_channel_offset,
                        y: y_channel_offset,
                        yy: z_channel_offset,
                    }),
                    hue_spline: None,
                    saturation_spline: None,
                    oneshot: true,
                    blueprint_id: None,
                });
            },
        )
        .unwrap_or_else(|err| {
            logging::log(
                format!("{err:?}\n        (selecting xyz_color_channels)"),
                logging::LogLevel::DbError,
                true,
            );
            vec![]
        });
}

pub fn saturate_color_wheel_for_fixture(
    db_connection: &mut PooledConn,
    fixture_id: usize,
    fixture: &mut DmxFixtureFileDescriptor,
) {
    let mut color_wheel_slots = vec![];
    db_connection
        .query_map(
            format!(
                "
                    SELECT
                        cwc.channel_offset,
                        cws.channel_value,
                        cws.name
                    FROM color_wheel_slot cws
                    JOIN color_wheel_channel cwc ON cws.color_wheel_id = cwc.id
                    JOIN fixtures fx ON fx.id = cwc.fixture_id
                    WHERE fx.id = {fixture_id}
                "
            ),
            |(channel_offset, channel_value, channel_name): (
                usize,
                u8,
                String,
            )| {
                color_wheel_slots.push((
                    channel_offset,
                    channel_value,
                    channel_name,
                ));
            },
        )
        .unwrap_or_else(|err| {
            logging::log(
                format!("{err:?}\n        (selecting xyz_color_channels)"),
                logging::LogLevel::DbError,
                true,
            );
            vec![]
        });
    if color_wheel_slots.len() == 9 {
        fixture.color = Some(DmxFixtureColor {
            color: RgbColor::new_black(),
            oneshot: true,
            hue_spline: None,
            saturation_spline: None,
            blueprint_id: None,
            channels: ColorChannel::ColorWheel(ColorWheel {
                channel: color_wheel_slots
                    .first()
                    .unwrap_or(&(0, 0, String::new()))
                    .0,
                red: color_wheel_slots
                    .iter()
                    .find(|slot| slot.2 == "red")
                    .unwrap_or(&(0, 0, String::new()))
                    .1,
                green: color_wheel_slots
                    .iter()
                    .find(|slot| slot.2 == "green")
                    .unwrap_or(&(0, 0, String::new()))
                    .1,
                blue: color_wheel_slots
                    .iter()
                    .find(|slot| slot.2 == "blue")
                    .unwrap_or(&(0, 0, String::new()))
                    .1,
                light_blue: color_wheel_slots
                    .iter()
                    .find(|slot| slot.2 == "light_blue")
                    .unwrap_or(&(0, 0, String::new()))
                    .1,
                purple: color_wheel_slots
                    .iter()
                    .find(|slot| slot.2 == "purple")
                    .unwrap_or(&(0, 0, String::new()))
                    .1,
                yellow: color_wheel_slots
                    .iter()
                    .find(|slot| slot.2 == "yellow")
                    .unwrap_or(&(0, 0, String::new()))
                    .1,
                pink: color_wheel_slots
                    .iter()
                    .find(|slot| slot.2 == "pink")
                    .unwrap_or(&(0, 0, String::new()))
                    .1,
                orange: color_wheel_slots
                    .iter()
                    .find(|slot| slot.2 == "orange")
                    .unwrap_or(&(0, 0, String::new()))
                    .1,
                white: color_wheel_slots
                    .iter()
                    .find(|slot| slot.2 == "white")
                    .unwrap_or(&(0, 0, String::new()))
                    .1,
            }),
        });
    }
}
impl DbHandler {
    #[must_use]
    pub fn fixture_types_for_active_show(
        db_connection: &mut PooledConn,
    ) -> Vec<DmxFixtureFileTypeDescriptor> {
        let mut fixtures = shallow_fixtures_for_active_show(db_connection);

        for fixture in &mut fixtures {
            if let Some(id) = fixture.id {
                saturate_movement_channels_for_fixture(
                    db_connection,
                    id,
                    fixture,
                );

                saturate_dmx_channels_for_fixture(db_connection, id, fixture);

                saturate_dmx_channel_keypoints(db_connection, fixture);

                saturate_color_channels_rgb_for_fixture(
                    db_connection,
                    id,
                    fixture,
                );

                saturate_color_channels_cmy_for_fixture(
                    db_connection,
                    id,
                    fixture,
                );

                saturate_color_channels_hsv_for_fixture(
                    db_connection,
                    id,
                    fixture,
                );

                saturate_color_channels_xyz_for_fixture(
                    db_connection,
                    id,
                    fixture,
                );

                saturate_color_wheel_for_fixture(db_connection, id, fixture);
            }
        }
        fixtures
    }
    pub fn update_fixture(&mut self, fixture: &DmxFixtureFileDescriptor) {
        if let Some(id) = fixture.id {
            self.db_connection()
                .query_drop(format!(
                    "
                        UPDATE fixtures SET
                            dmx_address = {},
                            dmx_universe = {},
                            name = '{}',
                            fixturetype = '{}',
                            stage_coordinate_x = {},
                            stage_coordinate_y = {},
                            footprint_size = {}
                        WHERE id = {id}
                    ",
                    fixture.dmx_address.unwrap_or_default(),
                    fixture.dmx_universe,
                    fixture.name,
                    fixture.fixturetype,
                    fixture.stage_coordinates.0,
                    fixture.stage_coordinates.1,
                    fixture.footprint_size,
                ))
                .unwrap_or_else(|err| {
                    logging::log(
                        format!("{err:?}\n        (updating fixture)"),
                        logging::LogLevel::DbError,
                        true,
                    );
                });
            self.db_connection()
                .query_drop(format!(
                    "
                        DELETE FROM dmx_channels WHERE fixture_id = {id};
                        DELETE FROM rgb_color_channels WHERE fixture_id = {id};
                        DELETE FROM cmy_color_channels WHERE fixture_id = {id};
                        DELETE FROM hsv_color_channels WHERE fixture_id = {id};
                        DELETE FROM xyz_color_channels WHERE fixture_id = {id};
                        DELETE FROM color_wheel_channel WHERE fixture_id = {id};
                        DELETE FROM color_wheel_slot WHERE fixture_id = {id};
                    "
                ))
                .unwrap_or_else(|err| {
                    logging::log(
                        format!("{err:?}\n        (deleting dmx_channels)"),
                        logging::LogLevel::DbError,
                        true,
                    );
                });

            persist_dmx_channels(&mut self.db_connection(), fixture, id);

            if let Some(color) = &fixture.color {
                persist_color_channels(
                    &mut self.db_connection(),
                    &color.channels,
                    id,
                );
            }
            self.active_show.fixtures =
                Self::fixtures_for_active_show(&mut self.db_connection());
            self.publish_changes();
        } else {
            logging::log(
                "Fixture with an id = None encountered".to_owned(),
                logging::LogLevel::Warning,
                true,
            );
        }
    }
    pub fn create_fixture(&mut self, fixture: &DmxFixtureFileDescriptor) {
        self.db_connection()
            .query_drop(format!(
                "
                INSERT INTO fixtures (
                    dmx_address,
                    dmx_universe,
                    name,
                    fixturetype,
                    stage_coordinate_x,
                    stage_coordinate_y,
                    footprint_size,
                    show_id
                )
                VALUES (
                    {},
                    {},
                    '{}',
                    '{}',
                    {},
                    {},
                    {},
                    (SELECT id FROM shows WHERE active)
                    )
                ",
                fixture.dmx_address.unwrap_or_default(),
                fixture.dmx_universe,
                fixture.name,
                fixture.fixturetype,
                fixture.stage_coordinates.0,
                fixture.stage_coordinates.1,
                fixture.footprint_size
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (creating fixture)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        let ids = self
            .db_connection()
            .query_map("SELECT MAX(id) FROM fixtures", |id: usize| id)
            .unwrap_or_else(|err| {
                logging::log(
                    format!(
                        "{err:?}\n        (obtaining just-created fixture id)"
                    ),
                    logging::LogLevel::DbError,
                    true,
                );
                vec![]
            });
        if let Some(id) = ids.first() {
            persist_dmx_channels(&mut self.db_connection(), fixture, *id);

            if let Some(color) = &fixture.color {
                persist_color_channels(
                    &mut self.db_connection(),
                    &color.channels,
                    *id,
                );
            }
        }
        self.active_show.fixtures =
            Self::fixtures_for_active_show(&mut self.db_connection());
        self.publish_changes();
    }
    pub fn delete_fixture(&mut self, id: usize) {
        self.db_connection()
            .query_drop(format!(
                "
                DELETE FROM fixtures WHERE id = {id}
            ",
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Deleting fixture)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        self.active_show.fixtures =
            Self::fixtures_for_active_show(&mut self.db_connection());
        logging::log_show_metadata(&mut self.db_connection());
        self.publish_changes();
    }
}

#[allow(clippy::too_many_lines)]
pub fn persist_dmx_channels(
    db_connection: &mut PooledConn,
    fixture: &DmxFixtureFileDescriptor,
    fixture_id: usize,
) {
    if let Some(mv_channels) = &fixture.movement_channels {
        db_connection
            .query_drop(format!(
                "
                    INSERT INTO dmx_channels
                    (
                        dmx_offset,
                        name,
                        dimmable,
                        default_value,
                        fixture_id
                    )
                    VALUES (
                        {},
                        'MovementChannelsPan',
                        {},
                        {},
                        {}
                        )
                ",
                mv_channels.pan.map_or(0, |pan| pan.channel),
                false,
                0,
                fixture_id
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!(
                        "{err:?}\n        (Inserting new MovementChannelsPan)"
                    ),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        db_connection
            .query_drop(format!(
                "
                        INSERT INTO dmx_channels
                        (
                            dmx_offset,
                            name,
                            dimmable,
                            default_value,
                            fixture_id
                        )
                        VALUES (
                            {},
                            'MovementChannelsTilt',
                            {},
                            {},
                            {}
                            )
                        ",
                mv_channels.tilt.map_or(0, |tilt| tilt.channel),
                false,
                0,
                fixture_id
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!(
                        "{err:?}\n        (Inserting new MovementChannelsTilt)"
                    ),
                    logging::LogLevel::DbError,
                    true,
                );
            });
    }
    for channel in &fixture.unimplemented_channels {
        db_connection
            .query_drop(format!(
                "
                        INSERT INTO dmx_channels
                        (
                            dmx_offset,
                            name,
                            dimmable,
                            inverted,
                            default_value,
                            fixture_id
                        )
                        VALUES (
                            {},
                            '{}',
                            {},
                            {},
                            {},
                            {}
                            )
                        ",
                channel.channel,
                channel.name,
                channel.dimmable,
                channel.inverted,
                channel.default,
                fixture_id
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!(
                        "{err:?}\n        (Inserting new UnimplementedChannel)"
                    ),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        let channel_id = db_connection
            .query_map(
                "
                    SELECT MAX(id) FROM dmx_channels;
                ",
                |id: usize| id,
            )
            .unwrap_or_else(|err| {
                logging::log(
                    format!(
                        "{err:?}\n        (Selecting just created dmx_channel)"
                    ),
                    logging::LogLevel::DbError,
                    true,
                );
                vec![]
            })
            .first()
            .copied();
        for keypoint in &channel.keypoints {
            if let Some(channel_id) = channel_id {
                db_connection
                    .query_drop(format!(
                        "
                        INSERT INTO keypoints
                        (
                            name,
                            value,
                            dmx_channel_id
                        )
                        VALUES (
                            '{}',
                            {},
                            {channel_id}
                            )
                        ",
                        keypoint.name, keypoint.value
                    ))
                    .unwrap_or_else(|err| {
                        logging::log(
                            format!(
                                "{err:?}\n        (Inserting new Keypoint)"
                            ),
                            logging::LogLevel::DbError,
                            true,
                        );
                    });
            }
        }
    }
}

#[allow(clippy::too_many_lines)]
fn persist_color_channels(
    db_connection: &mut PooledConn,
    color_channels: &ColorChannel,
    fixture_id: usize,
) {
    // TODO: The color wheel has static slots. They should be dynamic as in keypoints
    match color_channels {
        ColorChannel::RgbChannels(rgb_channels) => {
            db_connection
            .query_drop(format!(
                "
                    INSERT INTO rgb_color_channels
                    (
                        red_channel_offset,
                        green_channel_offset,
                        blue_channel_offset,
                        fixture_id
                    )
                    VALUES (
                        {},
                        {},
                        {},
                        {}
                    )
                ",
                rgb_channels.r.map_or("NULL".to_string(), |r| r.to_string()),
                rgb_channels.g.map_or("NULL".to_string(), |g| g.to_string()),
                rgb_channels.b.map_or("NULL".to_string(), |b| b.to_string()),
                fixture_id
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Inserting new rgb_color_channels)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        }
        ColorChannel::CmyChannels(cmy_channels) => {
            db_connection
            .query_drop(format!(
                "
                    INSERT INTO cmy_color_channels
                    (
                        cyan_channel_offset,
                        magenta_channel_offset,
                        yellow_channel_offset,
                        fixture_id
                    )
                    VALUES (
                        {},
                        {},
                        {},
                        {}
                    )
                ",
                cmy_channels.c.map_or("NULL".to_string(), |c| c.to_string()),
                cmy_channels.m.map_or("NULL".to_string(), |m| m.to_string()),
                cmy_channels.y.map_or("NULL".to_string(), |y| y.to_string()),
                fixture_id
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Inserting new cmy_color_channels)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        }
        ColorChannel::XyzChannels(xyz_channels) => {
            db_connection
            .query_drop(format!(
                "
                    INSERT INTO xyz_color_channels
                    (
                        x_channel_offset,
                        y_channel_offset,
                        z_channel_offset,
                        fixture_id
                    )
                    VALUES (
                        {},
                        {},
                        {},
                        {}
                    )
                ",
                xyz_channels.x.map_or("NULL".to_string(), |x| x.to_string()),
                xyz_channels.y.map_or("NULL".to_string(), |y| y.to_string()),
                xyz_channels.yy.map_or("NULL".to_string(), |yy| yy.to_string()),
                fixture_id
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Inserting new xyz_color_channels)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        }
        ColorChannel::HsvChannels(hsv_channels) => {
            db_connection
            .query_drop(format!(
                "
                    INSERT INTO hsv_color_channels
                    (
                        hue_channel_offset,
                        saturation_channel_offset,
                        value_channel_offset,
                        fixture_id
                    )
                    VALUES (
                        {},
                        {},
                        {},
                        {}
                    )
                ",
                hsv_channels.h.map_or("NULL".to_string(), |h| h.to_string()),
                hsv_channels.s.map_or("NULL".to_string(), |s| s.to_string()),
                hsv_channels.v.map_or("NULL".to_string(), |v| v.to_string()),
                fixture_id
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Inserting new hsv_color_channels)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        }
        ColorChannel::ColorWheel(color_wheel) => {
            db_connection
            .query_drop(format!(
                "
                    INSERT INTO color_wheel_channel
                    (
                        channel_offset,
                        fixture_id
                    )
                    VALUES (
                        {},
                        {}
                    )
                ",
                color_wheel.channel,
                fixture_id
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Inserting new color_wheel_channel)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
            db_connection
            .query_drop(format!(
                "
                    INSERT INTO color_wheel_slot
                    (
                        channel_value,
                        name,
                        color_wheel_id
                    )
                    VALUES (
                        {},
                        'red',
                        (SELECT MAX(id) FROM color_wheel_channel)
                    )
                ",
                color_wheel.red,
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Inserting new color_wheel_slot: red)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
            db_connection
            .query_drop(format!(
                "
                    INSERT INTO color_wheel_slot
                    (
                        channel_value,
                        name,
                        color_wheel_id
                    )
                    VALUES (
                        {},
                        'green',
                        (SELECT MAX(id) FROM color_wheel_channel)
                    )
                ",
                color_wheel.green,
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Inserting new color_wheel_slot: green)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
            db_connection
            .query_drop(format!(
                "
                    INSERT INTO color_wheel_slot
                    (
                        channel_value,
                        name,
                        color_wheel_id
                    )
                    VALUES (
                        {},
                        'blue',
                        (SELECT MAX(id) FROM color_wheel_channel)
                    )
                ",
                color_wheel.blue,
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Inserting new color_wheel_slot: blue)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
            db_connection
            .query_drop(format!(
                "
                    INSERT INTO color_wheel_slot
                    (
                        channel_value,
                        name,
                        color_wheel_id
                    )
                    VALUES (
                        {},
                        'light_blue',
                        (SELECT MAX(id) FROM color_wheel_channel)
                    )
                ",
                color_wheel.light_blue,
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Inserting new color_wheel_slot: light_blue)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
            db_connection
            .query_drop(format!(
                "
                    INSERT INTO color_wheel_slot
                    (
                        channel_value,
                        name,
                        color_wheel_id
                    )
                    VALUES (
                        {},
                        'purple',
                        (SELECT MAX(id) FROM color_wheel_channel)
                    )
                ",
                color_wheel.purple,
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Inserting new color_wheel_slot: purple)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
            db_connection
            .query_drop(format!(
                "
                    INSERT INTO color_wheel_slot
                    (
                        channel_value,
                        name,
                        color_wheel_id
                    )
                    VALUES (
                        {},
                        'yellow',
                        (SELECT MAX(id) FROM color_wheel_channel)
                    )
                ",
                color_wheel.yellow,
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Inserting new color_wheel_slot: yellow)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
            db_connection
            .query_drop(format!(
                "
                    INSERT INTO color_wheel_slot
                    (
                        channel_value,
                        name,
                        color_wheel_id
                    )
                    VALUES (
                        {},
                        'pink',
                        (SELECT MAX(id) FROM color_wheel_channel)
                    )
                ",
                color_wheel.pink,
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Inserting new color_wheel_slot: pink)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
            db_connection
            .query_drop(format!(
                "
                    INSERT INTO color_wheel_slot
                    (
                        channel_value,
                        name,
                        color_wheel_id
                    )
                    VALUES (
                        {},
                        'orange',
                        (SELECT MAX(id) FROM color_wheel_channel)
                    )
                ",
                color_wheel.orange,
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Inserting new color_wheel_slot: orange)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
            db_connection
            .query_drop(format!(
                "
                    INSERT INTO color_wheel_slot
                    (
                        channel_value,
                        name,
                        color_wheel_id
                    )
                    VALUES (
                        {},
                        'white',
                        (SELECT MAX(id) FROM color_wheel_channel)
                    )
                ",
                color_wheel.white,
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Inserting new color_wheel_slot: white)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        }
    }
}
