import { writable, get } from 'svelte/store';
import type { TimecodeFileDescriptor } from '$lib/types/bindings/TimecodeFileDescriptor';
import { shows } from './shows';
import { networking } from './networking';

function createTimecodeStore() {
    const { subscribe, set } = writable<TimecodeFileDescriptor[]>([]);

    shows.subscribe(() => fetchTimecodes().then(data => set(data)));

    return {
        subscribe,
        set,
        patchOne: (timecode: TimecodeFileDescriptor) =>
            patchTimecode(timecode).then(data => { set(data); return true }),
        createOne: () =>
            createTimecode().then(data => set(data)),
        deleteOne: (timecode: TimecodeFileDescriptor) =>
            deleteTimecode(timecode).then(data => set(data)),
    };
}

export const timecodes = createTimecodeStore();

export function appendTimecodeEditingReason(filterFn: (timecode: TimecodeFileDescriptor) => boolean, reason: string) {
    let snippets_from_store = get(timecodes);
    snippets_from_store = snippets_from_store.filter(snippet => filterFn(snippet));
    snippets_from_store.forEach(timecode => {
        if (timecode.requires_user_action_reason?.length) {
            timecode.requires_user_action_reason = timecode.requires_user_action_reason?.concat(", ", reason)
        } else {
            timecode.requires_user_action_reason = reason
        }
        timecodes.patchOne(timecode)
    })
}

async function fetchTimecodes(): Promise<TimecodeFileDescriptor[]> {
    return new Promise(async (resolve, _) => {
        const ip = get(networking);
        if (ip) {
            const response = await fetch(`http://${ip}:${networking.port}/timecodes`);
            resolve(response.json());
        } else {
            setTimeout(async () => resolve(await fetchTimecodes()), 1000)
        }
    })
}

async function createTimecode(
): Promise<TimecodeFileDescriptor[]> {
    await fetch(`http://${get(networking)}:${networking.port}/timecode`, { method: 'POST' });
    return fetchTimecodes();
}
async function patchTimecode(
    timecode: TimecodeFileDescriptor,
): Promise<TimecodeFileDescriptor[]> {
    await fetch(`http://${get(networking)}:${networking.port}/timecode`, {
        method: 'PATCH',
        body: JSON.stringify(timecode),
        headers: new Headers({ 'content-type': 'application/json' }),
    });
    return fetchTimecodes();
}
async function deleteTimecode(
    timecode: TimecodeFileDescriptor,
): Promise<TimecodeFileDescriptor[]> {
    await fetch(`http://${get(networking)}:${networking.port}/timecode/${timecode.id}`, {
        method: 'DELETE',
    });
    return fetchTimecodes();
}

