use crate::database_handler::snippet::Snippet;
use crate::database_handler::<PERSON>b<PERSON>and<PERSON>;
use crate::dmx_renderer::channel::color_channels::RgbColor;
use crate::dmx_renderer::channel::unimplemented_channels::UnimplementedChannelSetter;
use crate::dmx_renderer::fixture::PositionIndexOffsetMode;
use crate::dmx_renderer::DmxRenderer;
use crate::dmx_renderer::DEFAULT_BPM;
use crate::input_parser::collectors::spawn_beat_receive_thread;
use crate::logging;
use core::fmt;
use core::time::Duration;
use rand::{thread_rng, Rng};
use std::time::Instant;
use ts_rs::TS;

use super::dmx_communicator_functions::{PRESSED, RELEASED};

use alloc::sync::Arc;
use std::sync::Mutex;

use serde::Deserialize;
use serde::Serialize;

use super::dmx_communicator_functions::evaluate_instructions;

#[derive(Clone, Debug, PartialEq, Eq, Serialize, Deserialize, TS)]
#[ts(export)]
pub struct RawInput {
    pub id: u16,
    pub value: u8,
}

impl From<rwm_package_definitions::RawInput> for RawInput {
    fn from(value: rwm_package_definitions::RawInput) -> Self {
        Self {
            id: value.id,
            value: value.value,
        }
    }
}

#[derive(Clone, PartialEq, Eq, Debug, Serialize, Deserialize, TS)]
#[ts(export)]
pub struct FunctionGetter {
    pub fixture_id: usize,
    pub function: String,
}

#[derive(Clone, PartialEq, Eq, Debug, Serialize, Deserialize, TS)]
#[ts(export)]
pub struct MathOperator {
    pub left_value: InstructionValue,
    pub right_value: InstructionValue,
    pub operand: Operand,
}

#[derive(Clone, PartialEq, Eq, Debug, Serialize, Deserialize, TS)]
#[ts(export)]
pub struct NumberValue {
    pub value: u8,
    pub dmx_precision: bool,
}

#[derive(Clone, Copy, Debug, Serialize, Deserialize, PartialEq, Eq, TS)]
#[ts(export)]
pub enum Operand {
    Addition,
    Subtraction,
    Multiplication,
    Division,
    Modulus,
}
impl From<String> for Operand {
    fn from(value: String) -> Self {
        match value.as_str() {
            "Subtraction" => Self::Subtraction,
            "Multiplication" => Self::Multiplication,
            "Division" => Self::Division,
            "Modulus" => Self::Modulus,
            _ /* | "Addition" */ => Self::Addition,
        }
    }
}

#[derive(Clone, PartialEq, Eq, Debug, Serialize, Deserialize, TS)]
#[ts(export)]
pub enum InstructionValue {
    Number(NumberValue),
    Variable(String),
    Function(FunctionGetter),
    Keypoint(usize),
    Value,
    Pressed,
    Released,
    Default,
    Bpm,
    BpmModifier,
    MathOperator(Box<MathOperator>),
    Random((Box<InstructionValue>, Box<InstructionValue>)),
}

impl InstructionValue {
    #[must_use]
    pub fn to_num(
        &self,
        value: u8,
        dmx_renderer: &DmxRenderer,
        input_parser: &InputParser,
    ) -> Option<u8> {
        Some(match self {
            Self::Number(number_value) =>
            {
                #[allow(
                    clippy::cast_possible_truncation,
                    clippy::cast_sign_loss,
                    clippy::as_conversions
                )]
                if number_value.dmx_precision {
                    number_value.value
                } else {
                    let clamped = number_value.value.clamp(0, 100);
                    (((f64::from(clamped)) / 100.0) * 255.0)
                        .clamp(f64::from(u8::MIN), f64::from(u8::MAX))
                        as u8
                }
            }
            Self::Value => value,
            Self::Pressed => PRESSED,
            Self::Released => RELEASED,
            Self::Default => return None,
            Self::Random(range) => {
                let range: (u8, u8) = (
                    range
                        .0
                        .to_num(value, dmx_renderer, input_parser)
                        .unwrap_or(u8::MIN),
                    range
                        .1
                        .to_num(value, dmx_renderer, input_parser)
                        .unwrap_or(u8::MAX),
                );
                if range.0 < range.1 {
                    let mut rng = thread_rng();
                    rng.gen_range(range.0..range.1)
                } else {
                    range.0
                }
            }
            Self::Keypoint(keypoint_id) => {
                let keypoints = dmx_renderer.all_keypoints();
                for keypoint in &keypoints {
                    if keypoint.id == Some(*keypoint_id) {
                        return Some(keypoint.value);
                    }
                }
                u8::MIN
            }

            Self::Bpm => dmx_renderer.bpm_truncated(),
            Self::BpmModifier => dmx_renderer.bpm_modifier(),
            Self::Function(function_getter) => {
                dmx_renderer.get_function_value_of_fixture(function_getter)?
            }
            Self::Variable(var_name) => {
                input_parser.get_variable(var_name)?.value
            }
            Self::MathOperator(operator) => match operator.operand {
                Operand::Addition => operator
                    .left_value
                    .to_num(value, dmx_renderer, input_parser)?
                    .saturating_add(operator.right_value.to_num(
                        value,
                        dmx_renderer,
                        input_parser,
                    )?),
                Operand::Subtraction => operator
                    .left_value
                    .to_num(value, dmx_renderer, input_parser)?
                    .saturating_sub(operator.right_value.to_num(
                        value,
                        dmx_renderer,
                        input_parser,
                    )?),
                Operand::Multiplication => operator
                    .left_value
                    .to_num(value, dmx_renderer, input_parser)?
                    .saturating_mul(operator.right_value.to_num(
                        value,
                        dmx_renderer,
                        input_parser,
                    )?),
                Operand::Division => operator
                    .left_value
                    .to_num(value, dmx_renderer, input_parser)?
                    .checked_div(operator.right_value.to_num(
                        value,
                        dmx_renderer,
                        input_parser,
                    )?)?,
                Operand::Modulus => operator
                    .left_value
                    .to_num(value, dmx_renderer, input_parser)?
                    .checked_rem(operator.right_value.to_num(
                        value,
                        dmx_renderer,
                        input_parser,
                    )?)?,
            },
        })
    }
}

#[derive(Clone, Debug, Serialize, Deserialize, PartialEq, TS)]
#[ts(export)]
#[serde(tag = "instruction", content = "instructionMod")]
pub enum Instruction {
    // action
    ExecuteCallableSnippet(usize),
    ColorTo(ColorToInstruction),
    ColorToRandom(Option<InstructionValue>),
    ActivateAllFixtures,
    ActivateFixtureById(usize),
    ActivateFixtureGroup(String),
    BlueprintTo(BlueprintToInstruction),
    TimecodeTo(TimecodeToInstruction),
    SetBlueprintPositionIndexOffsetMode(PositionIndexOffsetMode),
    SetSpeedOfBlueprints(InstructionValue),
    ToggleQueueMode(QueueMode),
    UnimplementedChannelTo(UnimplementedChannelSetter),
    PanTo(InstructionValue),
    AddToPan(InstructionValue),
    TiltTo(InstructionValue),
    AddToTilt(InstructionValue),
    PositionTo(usize),
    BpmTo(InstructionValue),
    BpmModifierTo(InstructionValue),
    FixtureLoop(FixtureLoop),
    CreateVariable(String),
    SetVariable(VariableSetter),
    AddToGroup(AddToGroup),
    RemoveFromGroup(RemoveFromGroup),
    DelayBy(InstructionValue),
    DeselectAllFixtures,
    DimmerTo(InstructionValue),
    ClearBlueprint(usize),
    StartRecording,
    StopRecording,
    ClearRecording,

    // controllflow
    IfStatement(IfStatement),
    LoopStatement(LoopStatement),
    IfQueueAllowsContinue(Vec<Instruction>),

    // other
    Nop,
}

#[derive(Clone, Debug, Serialize, Deserialize, PartialEq, Eq, TS)]
#[ts(export)]
pub struct TimecodeToInstruction {
    pub id: usize,
    pub index: usize,
    pub play: bool,
}

#[derive(Clone, Debug, Serialize, Deserialize, PartialEq, Eq, TS)]
#[ts(export)]
pub struct BlueprintToInstruction {
    pub id: usize,
    pub oneshot: bool,
    pub delay: bool,
}

#[derive(Clone, Debug, Serialize, Deserialize, PartialEq, Eq, TS)]
#[ts(export)]
pub struct ColorToInstruction {
    pub color: RgbColor,
    pub fade_duration: Option<InstructionValue>,
}

#[derive(Clone, Debug, Serialize, Deserialize, PartialEq, TS)]
#[ts(export)]
pub struct RemoveFromGroup {
    pub name: String,
    pub fixtures: Vec<Instruction>,
}

#[derive(Clone, Debug, Serialize, Deserialize, PartialEq, TS)]
#[ts(export)]
pub struct AddToGroup {
    pub name: String,
    pub fixtures: Vec<Instruction>,
}

#[derive(Clone, Debug, Serialize, Deserialize, PartialEq, TS)]
#[ts(export)]
pub struct FixtureLoop {
    pub fixtures: Vec<Instruction>,
    pub instructions: Vec<Instruction>,
}

#[derive(Clone, Copy, Debug, Serialize, Deserialize, PartialEq, Eq, TS)]
#[ts(export)]
pub enum Comparator {
    Less,
    LessOrEqual,
    Greater,
    GreaterOrEqual,
    Equal,
    NotEqual,
}
impl From<String> for Comparator {
    fn from(value: String) -> Self {
        match value.as_str() {
            "Less" => Self::Less,
            "LessOrEqual" => Self::LessOrEqual,
            "Greater" => Self::Greater,
            "GreaterOrEqual" => Self::GreaterOrEqual,
            "NotEqual" => Self::NotEqual,
            _ /* | "Equal" */ => Self::Equal,
        }
    }
}
impl fmt::Display for Comparator {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            Self::Less => write!(f, "Less"),
            Self::LessOrEqual => write!(f, "LessOrEqual"),
            Self::Greater => write!(f, "Greater"),
            Self::GreaterOrEqual => write!(f, "GreaterOrEqual"),
            Self::Equal => write!(f, "Equal"),
            Self::NotEqual => write!(f, "NotEqual"),
        }
    }
}

#[derive(Clone, Debug, Serialize, Deserialize, PartialEq, Eq, TS)]
#[ts(export)]
pub struct Comparison {
    pub left_value: InstructionValue,
    pub right_value: InstructionValue,
    pub comparator: Comparator,
}
impl Comparison {
    pub const fn negate_comparator(&mut self) {
        self.comparator = match self.comparator {
            Comparator::Less => Comparator::GreaterOrEqual,
            Comparator::LessOrEqual => Comparator::Greater,
            Comparator::Greater => Comparator::LessOrEqual,
            Comparator::GreaterOrEqual => Comparator::Less,
            Comparator::Equal => Comparator::NotEqual,
            Comparator::NotEqual => Comparator::Equal,
        }
    }
}
#[derive(Clone, Debug, Serialize, Deserialize, PartialEq, TS)]
#[ts(export)]
pub struct IfStatement {
    pub comparison: Comparison,
    pub truthy_branch: Vec<Instruction>,
    pub falsy_branch: Vec<Instruction>,
}
#[derive(Clone, Debug, Serialize, Deserialize, PartialEq, TS)]
#[ts(export)]
pub struct LoopStatement {
    pub comparison: Comparison,
    pub content: Vec<Instruction>,
}

#[derive(Copy, Debug, PartialEq, Eq, Clone, Serialize, Deserialize, TS)]
#[ts(export)]
pub enum QueueMode {
    Flush,
    Queue,
}
impl core::ops::Not for QueueMode {
    type Output = Self;

    fn not(self) -> Self::Output {
        match self {
            Self::Flush => Self::Queue,
            Self::Queue => Self::Flush,
        }
    }
}
impl From<String> for QueueMode {
    fn from(value: String) -> Self {
        match value.as_str() {
            "Queue" => Self::Queue,
            _ /* | "Flush" */ => Self::Flush,
        }
    }
}
impl fmt::Display for QueueMode {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            Self::Flush => write!(f, "Flush"),
            Self::Queue => write!(f, "Queue"),
        }
    }
}

#[derive(Clone, Debug)]
pub struct QueuedInstruction {
    pub instruction: Instruction,
    pub value: u8,
}

#[derive(Clone, Debug)]
pub struct DelayableInstructionBatch {
    pub instructions: Vec<Instruction>,
    pub delay_by_sixteenth: u8,
    pub value: u8,
    pub selected_fixtures: Vec<ActivatedFixture>,
}

impl DelayableInstructionBatch {
    #[must_use]
    pub const fn new(
        value: u8,
        delay_by_sixteenth: u8,
        selected_fixtures: Vec<ActivatedFixture>,
    ) -> Self {
        Self {
            instructions: vec![],
            delay_by_sixteenth,
            value,
            selected_fixtures,
        }
    }
    #[must_use]
    pub fn from_snippet(snippet: &Snippet, value: u8) -> Self {
        Self {
            instructions: snippet.instructions.clone(),
            delay_by_sixteenth: 0,
            value,
            selected_fixtures: vec![],
        }
    }
    #[must_use]
    pub fn from_instruction(instruction: &Instruction, value: u8) -> Self {
        Self {
            instructions: vec![instruction.clone()],
            delay_by_sixteenth: 0,
            value,
            selected_fixtures: vec![],
        }
    }
    #[must_use]
    pub fn from_queued_instruction(
        queued_instruction: &QueuedInstruction,
    ) -> Self {
        Self {
            instructions: vec![queued_instruction.instruction.clone()],
            delay_by_sixteenth: 0,
            value: queued_instruction.value,
            selected_fixtures: vec![],
        }
    }
    #[must_use]
    pub fn from_loop_statement(
        loop_statement: &LoopWithTailInstructions,
    ) -> Self {
        let mut instructions = vec![];
        instructions.push(loop_statement.loop_instruction.clone());
        for tail_instruction in &loop_statement.tail_instructions {
            instructions.push(tail_instruction.clone());
        }
        Self {
            instructions,
            delay_by_sixteenth: 0,
            value: loop_statement.value,
            selected_fixtures: loop_statement.selected_fixtures.clone(),
        }
    }
}

#[derive(Clone, Debug)]
pub struct LoopWithTailInstructions {
    pub loop_instruction: Instruction,
    pub tail_instructions: Vec<Instruction>,
    pub value: u8,
    pub selected_fixtures: Vec<ActivatedFixture>,
}

#[derive(Debug, Clone, PartialEq, Eq)]
pub struct ActivatedFixture {
    pub id: usize,
    pub name: String,
    pub fixturetype: String,
}

pub struct InputParser {
    pub raw_inputs: Vec<RawInput>,
    pub most_recent_input_activated: Option<u16>,
    pub beat_index: Arc<Mutex<(u8, bool)>>,
    pub time_since_last_beat: Arc<Mutex<i32>>,
    pub last_calculated_bpm: Arc<Mutex<u16>>,
    pub instruction_queue: Vec<QueuedInstruction>,
    pub one_time_instructions: Vec<DelayableInstructionBatch>,
    pub queue_mode: QueueMode,
    pub selected_fixtures: Vec<ActivatedFixture>,
    pub variables: Vec<Variable>,
    pub delay_upcomming_instructions: bool,
    pub delayed_instructions: Vec<DelayableInstructionBatch>,
    pub append_upcomming_instructions_to_loop: bool,
    pub loop_with_tail_instructions: Vec<LoopWithTailInstructions>,
    pub loopstation: LoopStation,
}

#[derive(Default)]
pub struct LoopStation {
    recorded_keypresses: Vec<(Duration, RawInput)>,
    timeframe_start: Option<Instant>,
    yielded_already_in_this_pass: usize,
    timeframe_duration: Option<Duration>,
}

impl LoopStation {
    pub fn start_recording(&mut self) {
        if !self.is_recording() {
            self.timeframe_start = Some(Instant::now());
        }
    }
    pub fn stop_recording(&mut self) {
        if self.is_recording() {
            self.timeframe_duration = Some(
                self.timeframe_start.unwrap_or_else(Instant::now).elapsed(),
            );
        }
    }
    pub fn clear_recording(&mut self) {
        self.recorded_keypresses = vec![];
        self.timeframe_start = None;
        self.timeframe_duration = None;
    }
    #[must_use]
    pub fn next_due_keypresses(&mut self) -> Vec<RawInput> {
        if let Some(start) = self.timeframe_start {
            if let Some(duration) = self.timeframe_duration {
                #[allow(clippy::arithmetic_side_effects)]
                let relative_duration_since_start = Duration::from_millis(
                    start.elapsed().as_millis().try_into().unwrap_or(u64::MAX)
                        % duration.as_millis().try_into().unwrap_or(u64::MAX),
                );

                #[allow(clippy::needless_collect)]
                {
                    let could_be_yielded_keypresses: Vec<(Duration, RawInput)> =
                        self.recorded_keypresses
                            .iter()
                            .filter(|(timestamp, _)| {
                                *timestamp < relative_duration_since_start
                            })
                            .cloned()
                            .collect();

                    if could_be_yielded_keypresses.len()
                        < self.yielded_already_in_this_pass
                    {
                        self.yielded_already_in_this_pass = 0;
                    }
                }

                let result: Vec<RawInput> = self
                    .recorded_keypresses
                    .iter()
                    .skip(self.yielded_already_in_this_pass)
                    .filter(|(timestamp, _)| {
                        *timestamp < relative_duration_since_start
                    })
                    .map(|(_, keypress)| keypress)
                    .cloned()
                    .collect();

                self.yielded_already_in_this_pass = self
                    .yielded_already_in_this_pass
                    .saturating_add(result.len());
                return result;
            }
        }
        vec![]
    }
    pub fn append_keypress(&mut self, keypress: &RawInput) {
        if let Some(timeframe_start) = self.timeframe_start {
            self.recorded_keypresses
                .push((timeframe_start.elapsed(), keypress.clone()));
        }
    }
    #[must_use]
    pub const fn is_recording(&self) -> bool {
        self.timeframe_start.is_some() && self.timeframe_duration.is_none()
    }
}

#[derive(Clone, Serialize, Deserialize, Debug, TS)]
#[ts(export)]
pub struct Variable {
    pub name: String,
    pub value: u8,
}

#[derive(Clone, Serialize, Deserialize, Debug, PartialEq, Eq, TS)]
#[ts(export)]
pub struct VariableSetter {
    pub name: String,
    pub value: InstructionValue,
}

impl Default for InputParser {
    fn default() -> Self {
        Self::new()
    }
}

impl InputParser {
    #[must_use]
    pub fn new() -> Self {
        let beat_index = Arc::new(Mutex::new((1, true)));
        let last_calculated_bpm = Arc::new(Mutex::new(DEFAULT_BPM));
        let time_since_last_beat = Arc::new(Mutex::new(1));
        spawn_beat_receive_thread(
            Arc::clone(&beat_index),
            Arc::clone(&last_calculated_bpm),
            Arc::clone(&time_since_last_beat),
        );
        Self {
            raw_inputs: vec![],
            most_recent_input_activated: None,
            beat_index,
            time_since_last_beat,
            last_calculated_bpm,
            instruction_queue: vec![],
            one_time_instructions: vec![],
            queue_mode: QueueMode::Flush,
            selected_fixtures: vec![],
            variables: vec![],
            delay_upcomming_instructions: false,
            delayed_instructions: vec![],
            append_upcomming_instructions_to_loop: false,
            loop_with_tail_instructions: vec![],
            loopstation: LoopStation::default(),
        }
    }
    #[allow(clippy::too_many_lines)]
    pub fn process_input(
        &mut self,
        db_handler: &mut DbHandler,
        dmx_renderer: &mut DmxRenderer,
        execute_once_per_frame_actions: bool,
    ) {
        if let Ok(e) = self.time_since_last_beat.try_lock() {
            if *e == 0 {
                if let Ok(bpm) = self.last_calculated_bpm.try_lock() {
                    if let Ok(mut index) = self.beat_index.try_lock() {
                        dmx_renderer.set_beat_fraction_duration(*bpm);
                        *index = (1, true);
                    }
                }
            }
        }
        let mut instructions: Vec<DelayableInstructionBatch> = vec![];
        if let Some(startup_snippets) = db_handler.startup_snippets_if_needed()
        {
            self.variables = vec![];
            self.loop_with_tail_instructions = vec![];
            for snippet in &startup_snippets {
                if !snippet.do_not_use_instructions {
                    instructions.push(DelayableInstructionBatch::from_snippet(
                        snippet, 0,
                    ));
                }
            }
        }
        if execute_once_per_frame_actions {
            for snippet in &db_handler.watcher() {
                if !snippet.do_not_use_instructions {
                    instructions.push(DelayableInstructionBatch::from_snippet(
                        snippet, 0,
                    ));
                }
            }
        }
        for instruction in &self.one_time_instructions {
            instructions.push(instruction.clone());
        }
        self.one_time_instructions = vec![];

        for raw_input in &self.raw_inputs {
            let mut snippet =
                db_handler.find_snippet_by_serial_module_key(raw_input.id);

            if let Some(snippet) = snippet.as_mut() {
                if !snippet.do_not_use_instructions {
                    instructions.push(DelayableInstructionBatch::from_snippet(
                        snippet,
                        raw_input.value,
                    ));
                }
            } else {
                logging::log(
                    format!(
                        "Received key {} but found no snippet for it",
                        raw_input.id
                    ),
                    logging::LogLevel::Info,
                    false,
                );
            }
        }
        self.raw_inputs = vec![];

        for raw_input in self.loopstation.next_due_keypresses() {
            let mut snippet =
                db_handler.find_snippet_by_serial_module_key(raw_input.id);

            if let Some(snippet) = snippet.as_mut() {
                if !snippet.do_not_use_instructions {
                    instructions.push(DelayableInstructionBatch::from_snippet(
                        snippet,
                        raw_input.value,
                    ));
                }
            } else {
                logging::log(
                    format!(
                        "Loopstation activated key {} but found no snippet for it",
                        raw_input.id
                    ),
                    logging::LogLevel::Info,
                    false,
                );
            }
        }

        if self.queue_mode == QueueMode::Flush {
            for queued_instruction in self.instruction_queue.clone() {
                instructions.push(
                    DelayableInstructionBatch::from_queued_instruction(
                        &queued_instruction,
                    ),
                );
            }
            self.instruction_queue = vec![];
        }

        for delayed_instruction in &self.delayed_instructions {
            if delayed_instruction.delay_by_sixteenth == 0 {
                instructions.push(delayed_instruction.clone());
            }
        }
        self.delayed_instructions
            .retain(|batch| batch.delay_by_sixteenth != 0);

        if execute_once_per_frame_actions {
            for loop_statement in &self.loop_with_tail_instructions {
                instructions.push(
                    DelayableInstructionBatch::from_loop_statement(
                        loop_statement,
                    ),
                );
            }
            self.loop_with_tail_instructions = vec![];
        }

        for instruction_batch in &instructions {
            self.selected_fixtures =
                instruction_batch.selected_fixtures.clone();
            evaluate_instructions(
                self,
                dmx_renderer,
                instruction_batch.value,
                &instruction_batch.instructions,
                db_handler,
                false,
            );
            self.delay_upcomming_instructions = false;
            self.append_upcomming_instructions_to_loop = false;
        }
    }
    #[allow(
        clippy::cast_possible_truncation,
        clippy::cast_sign_loss,
        clippy::as_conversions
    )]
    pub fn reduce_delays(
        &mut self,
        beat_delta: f32,
        leftover_beat_delta: f32,
    ) -> f32 {
        let reduce_delays_by: f32 = (leftover_beat_delta + beat_delta).trunc();
        for batch in &mut self.delayed_instructions {
            batch.delay_by_sixteenth = batch.delay_by_sixteenth.saturating_sub(
                (reduce_delays_by.clamp(0., u8::MAX.into()) as u8)
                    .saturating_mul(4),
            );
        }
        leftover_beat_delta - reduce_delays_by
    }
    pub fn create_variable(&mut self, name: String) {
        if !name.is_empty()
            && self
                .variables
                .iter()
                .find(|variable| variable.name == name)
                .is_none()
        {
            self.variables.push(Variable { name, value: 0 });
        }
    }
    #[must_use]
    pub fn get_variable(&self, name: &String) -> Option<&Variable> {
        self.variables
            .iter()
            .find(|variable| variable.name == *name)
    }
    pub fn get_variable_mut(&mut self, name: &String) -> Option<&mut Variable> {
        self.variables
            .iter_mut()
            .find(|variable| variable.name == *name)
    }
    pub fn push_to_inputs(&mut self, raw_input: RawInput) {
        self.most_recent_input_activated = Some(raw_input.id);
        if self.loopstation.is_recording() {
            self.loopstation.append_keypress(&raw_input);
        }
        self.raw_inputs.push(raw_input);
    }
    pub fn push_one_time_instructions(
        &mut self,
        instructions: &Vec<Instruction>,
        value: u8,
    ) {
        for instruction in instructions {
            self.one_time_instructions.push(
                DelayableInstructionBatch::from_instruction(instruction, value),
            );
        }
    }
    pub fn reset_to_defaults(&mut self) {
        self.raw_inputs = vec![];
        self.most_recent_input_activated = None;
        self.instruction_queue = vec![];
        self.one_time_instructions = vec![];
        self.queue_mode = QueueMode::Flush;
        self.selected_fixtures = vec![];
        self.variables = vec![];
        self.delay_upcomming_instructions = false;
        self.delayed_instructions = vec![];
        self.append_upcomming_instructions_to_loop = false;
        self.loop_with_tail_instructions = vec![];
    }
}
