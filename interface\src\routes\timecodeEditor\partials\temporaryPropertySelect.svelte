<script lang="ts">
    import Button from "$lib/atoms/button.svelte";
    import Icon from "$lib/atoms/icon.svelte";
    import Select from "$lib/atoms/select.svelte";

    let selectedProperty: string = $state("");

    let {
        possibleProperties = $bindable(),
        addPropertyToTrack,
    }: {
        possibleProperties: string[];
        addPropertyToTrack: (property: string) => any;
    } = $props();
</script>

<div class="flex">
    <Select bind:value={selectedProperty}>
        {#each possibleProperties as property}
            <option value={property}>{property}</option>
        {/each}
    </Select>
    <Button
        id="add-property-to-track"
        onclick={() => {
            addPropertyToTrack(selectedProperty);
        }}
    >
        <Icon icon="mdi:add"></Icon>
    </Button>
</div>
