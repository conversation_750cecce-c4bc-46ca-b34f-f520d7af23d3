import { PropertyFileDescriptor } from "./types/bindings/PropertyFileDescriptor";
import { TimedPropertyFileDescriptor } from "./types/bindings/TimedPropertyFileDescriptor";
import { get } from "svelte/store";
import { networking } from "./stores/networking";

export function extractPropertyName(property: PropertyFileDescriptor | TimedPropertyFileDescriptor): string {
    if ("PanTiltPositions" in property) {
        return "PanTiltPositions";
    } else if ("ColorPropertyCoordinates" in property) {
        return "ColorTransition";
    } else if ("CallSnippet" in property) {
        return "CallSnippet";
    } else {
        return property.UnimplementedChannel[0];
    }
}

export function createDefaultTimedPropertyBasedOn(
    propertyName: string,
    xOffset: number
): TimedPropertyFileDescriptor {
    if (propertyName === "ColorTransition") {
        return {
            ColorPropertyCoordinates: [
                [
                    { x: 0, color: { red: 255, green: 0, blue: 0 } },
                    { x: 160, color: { red: 0, green: 0, blue: 255 } },
                ],
                xOffset,
                [],
            ],
        }
    } else if (propertyName === "CallSnippet") {
        return {
            CallSnippet: [
                0,
                xOffset,
                [],
            ],
        };
    } else {
        return {
            UnimplementedChannel: [
                propertyName,
                [
                    { x: 0, y: 128 },
                    { x: 160, y: 128 },
                ],
                xOffset,
                [],
            ],
        };
    }
}

export function mapRange(value: number, fromMin: number, fromMax: number, toMin: number, toMax: number): number {
    if (fromMin === fromMax) {
        throw new Error("fromMin and fromMax cannot be the same value.");
    }

    return toMin + ((value - fromMin) * (toMax - toMin)) / (fromMax - fromMin);
}

export function isLightColor(hexColor: string): boolean {
    const hex = hexColor.replace('#', '');

    const r = parseInt(hex.slice(0, 2), 16);
    const g = parseInt(hex.slice(2, 4), 16);
    const b = parseInt(hex.slice(4, 6), 16);

    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;

    return luminance > 0.5;
}

export function getThemeTextColors(currentTheme: string) {
    const themeColors = {
        dark: {
            primary: '#FFFFFF',
            complementary: '#000000'
        },
        light: {
            primary: '#000000',
            complementary: '#FFFFFF'
        },
        solar: {
            primary: '#002B36',
            complementary: '#EEE8D5'
        },
        catpuccin: {
            primary: '#FFFFFF',
            complementary: '#303446'
        }
    };

    return themeColors[currentTheme as keyof typeof themeColors] || themeColors.dark;
}
/**
 * Converts a user-facing value (0-100) to a backend DMX value (0-255)
 * TODO: remove this
 */
export function userToBackend(value: number): number {
    const clamped = Math.max(0, Math.min(100, value));
    const mapped = Math.round(mapRange(clamped, 0, 100, 0, 255));
    return Math.max(0, Math.min(255, mapped));
}

/**
 * Converts a backend DMX value (0-255) to a user-facing value (0-100)
 * TODO: remove this
 */
export function backendToUser(value: number): number {
    const clamped = Math.max(0, Math.min(255, value));
    const mapped = Math.round(mapRange(clamped, 0, 255, 0, 100));
    return Math.max(0, Math.min(100, mapped));
}

export async function fetchDmxOutput(universe: number = 1): Promise<number[]> {
    try {
        const ip = get(networking);
        if (!ip) {
            return []
        }

        const url = `http://${ip}:${networking.port}/dmxuniverse/${universe}`;
        const response = await fetch(url);

        if (response.ok) {
            return await response.json();
        } else {
            throw new Error('Unable to retreive the currect dmx universe data');
        }
    } catch (error) {
        console.error('Failed to fetch DMX output:', error);
        throw error;
    }
}
