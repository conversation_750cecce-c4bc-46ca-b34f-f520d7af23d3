use super::{
    directory_tree::{Directory, TreeItem},
    fixturegroups::FixtureGroup,
    pan_tilt_positions::ComposedPanTiltPosition,
    snippet::Snippet,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
};
use crate::{
    dmx_renderer::{
        dynamics::{
            blueprint::BlueprintFileDescriptor,
            timecode::TimecodeFileDescriptor,
        },
        fixture::DmxFixtureFileDescriptor,
    },
    logging,
};
use mysql::{prelude::Queryable, PooledConn};
use serde::{Deserialize, Serialize};
use ts_rs::TS;

#[derive(Serialize, Deserialize, TS)]
#[ts(export)]
pub struct Show {
    pub snippets_dir: Option<Directory<Snippet>>,
    pub blueprints: Vec<BlueprintFileDescriptor>,
    pub timecodes: Vec<TimecodeFileDescriptor>,
    pub fixtures: Vec<DmxFixtureFileDescriptor>,
    pub positions: Vec<ComposedPanTiltPosition>,
    pub groups: Vec<FixtureGroup>,
    pub name: String,
}

impl Show {
    #[must_use]
    pub fn enriched_groups(&self) -> Vec<FixtureGroup> {
        let mut result = self.groups.clone();

        result.push(FixtureGroup {
            id: None,
            name: "All Fixtures".to_owned(),
            fixture_ids: self
                .fixtures
                .iter()
                .filter(|fixture|
                    fixture.id.is_some()
                    ).map(|fixture|
                        fixture.id.unwrap_or_else(|| {
                            logging::log(
                                "Trying to generate fixturegroup `All Fixtures` from fixture without id".to_owned(),
                                logging::LogLevel::Warning,
                                false
                            );
                            0
                        })
                    )
                    .collect(),
            immutable: false,
        });

        let mut all_types = vec![];

        for fixture in &self.fixtures {
            if !all_types.contains(&fixture.fixturetype) {
                all_types.push(fixture.fixturetype.clone());
            }
        }

        for fx_type in &all_types {
            let all_fixtures_of_type: Vec<DmxFixtureFileDescriptor> = self
                .fixtures
                .iter()
                .filter(|fixture| fixture.fixturetype == *fx_type)
                .cloned()
                .collect();

            result.push(FixtureGroup {
                id: None,
                name: fx_type.clone(),
                fixture_ids: all_fixtures_of_type
                    .iter()
                    .map(|fixture|
                        fixture.id.unwrap_or_else(|| {
                            logging::log(
                                format!("Trying to generate fixturegroup Fixturetype `{}` from fixture without id", fx_type.clone()),
                                logging::LogLevel::Warning,
                                false
                            );
                            0
                        })
                    )
                    .collect(),
                immutable: false,
            });
        }

        result
    }
}

impl DbHandler {
    #[must_use]
    pub fn get_available_show_names_with_active_hint(
        &mut self,
    ) -> Vec<(usize, String, bool)> {
        let result = self
            .db_connection()
            .query_map(
                "SELECT id, name, active FROM shows",
                |result: (usize, String, bool)| result,
            )
            .unwrap_or_else(|err| {
                logging::log(
                    format!(
                        "{err:?}\n        (selecting shows with active hint)"
                    ),
                    logging::LogLevel::DbError,
                    true,
                );
                vec![]
            });
        if result.is_empty() {
            logging::log(
                "No shows found".to_string(),
                logging::LogLevel::Warning,
                true,
            );
        }
        result
    }
    pub fn get_active_show_name(
        db_connection: &mut PooledConn,
    ) -> Option<String> {
        db_connection
            .query_map("SELECT name FROM shows WHERE active", |name: String| {
                name
            })
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Selecting active show name)"),
                    logging::LogLevel::DbError,
                    true,
                );
                vec![]
            })
            .first()
            .cloned()
    }
    pub fn get_show(&mut self, id: usize) -> &Show {
        self.switch_show_to(id);
        &self.active_show
    }

    pub fn create_new_show(&mut self, name: &str) -> Show {
        self.db_connection()
            .query_drop(
                "
                UPDATE shows
                SET active = FALSE
            ",
            )
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (resetting active show)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        self.db_connection()
            .query_drop(format!(
                "
                INSERT INTO shows (name, active)
                VALUES ('{name}', TRUE)
            "
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (inserting new show)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        self.create_root_snippet_directory();
        Show {
            snippets_dir: Self::snippets_dir_for_active_show(
                &mut self.db_connection(),
            ),
            blueprints: vec![],
            timecodes: vec![],
            fixtures: vec![],
            positions: vec![],
            groups: vec![],
            name: name.to_owned(),
        }
    }
    pub fn upload_show(&mut self, show: &Show) {
        self.active_show = self.create_new_show(&show.name);
        for fixture in &show.fixtures {
            self.create_fixture(fixture);
        }
        for blueprint in &show.blueprints {
            let id = self.create_default_blueprint_in_active_show();
            let mut blueprint = blueprint.clone();
            blueprint.id = id;
            self.update_blueprint(&blueprint);
        }
        for timecode in &show.timecodes {
            let id = self.create_default_timecode_in_active_show();
            let mut timecode = timecode.clone();
            timecode.id = id;
            self.update_timecode(&timecode);
        }
        for position in &show.positions {
            self.create_pan_tilt_position_from_values(
                &position.name,
                &position.fixture_positions,
            );
        }
        for group in &show.groups {
            self.create_fixturegroup(group);
        }
        if let Some(snippets_dir) = show.snippets_dir.as_ref() {
            self.saturate_snippets_dir(None, snippets_dir.content.as_slice());
        }
    }
    fn saturate_snippets_dir(
        &mut self,
        parent_dir_id: Option<usize>,
        content_items: &[TreeItem<Snippet>],
    ) {
        let parent_dir_id = parent_dir_id.unwrap_or_else(|| {
            self.db_connection()
                .query_map(
                    "
                    SELECT MAX(id) FROM snippet_dirs;
                ",
                    |id: usize| id,
                )
                .unwrap_or_else(|err| {
                    logging::log(
                        format!(
                            "{err:?}\n        (Selecting root snippets_dir)"
                        ),
                        logging::LogLevel::DbError,
                        true,
                    );
                    vec![]
                })
                .first()
                .copied()
                .unwrap_or_else(|| {
                    logging::log(
                        "ERROR        (Selecting root snippets_dir)".to_owned(),
                        logging::LogLevel::DbError,
                        true,
                    );
                    0
                })
        });

        for item in content_items {
            match item {
                TreeItem::Directory(directory) => {
                    if let Some(directory_id) =
                        self.create_default_snippet_directory(parent_dir_id)
                    {
                        self.set_snippets_dir_name(
                            directory_id,
                            &directory.name,
                        );
                        self.saturate_snippets_dir(
                            Some(parent_dir_id),
                            content_items,
                        );
                    }
                }
                TreeItem::Item(snippet) => {
                    if let Some(snippet_id) =
                        self.create_default_snippet(parent_dir_id)
                    {
                        self.set_snippet_name(snippet_id, &snippet.name);
                        self.set_snippet_category(snippet_id, snippet.category);
                        self.set_snippet_do_not_use_instructions(
                            snippet_id,
                            snippet.do_not_use_instructions,
                        );
                        self.set_snippet_serial_module_key(
                            snippet_id,
                            snippet.serial_module_key,
                        );
                        self.set_snippet_requires_user_action_reason(
                            snippet_id,
                            &snippet.requires_user_action_reason,
                        );
                    }
                }
            }
        }
    }
    pub fn rename_show(&mut self, id: usize, new_name: &str) {
        self.db_connection()
            .query_drop(format!(
                "
                    UPDATE shows
                    SET name = '{new_name}'
                    WHERE id = {id}
                    "
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (updating show)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
    }
    pub fn delete_show(&mut self, id: usize) {
        self.db_connection()
            .query_drop(format!(
                "
                    DELETE FROM shows
                    WHERE id = {id}
                    "
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (deleting show)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
    }
    pub fn switch_show_to(&mut self, id: usize) {
        self.db_connection()
            .query_drop(
                "
                UPDATE shows
                SET active = FALSE
                ",
            )
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (resetting active show)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        self.db_connection()
            .query_drop(format!(
                "
                    UPDATE shows
                    SET active = TRUE
                    WHERE id = {id}
                    "
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (setting show to active)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        self.active_show = Show {
            fixtures: Self::fixtures_for_active_show(&mut self.db_connection()),
            snippets_dir: Self::snippets_dir_for_active_show(
                &mut self.db_connection(),
            ),
            blueprints: Self::blueprints_for_active_show(
                &mut self.db_connection(),
            ),
            timecodes: Self::timecodes_for_active_show(
                &mut self.db_connection(),
            ),
            positions: Self::pan_tilt_positions(&mut self.db_connection()),
            groups: Self::fixturegroups_for_active_show(
                &mut self.db_connection(),
            ),
            name: Self::get_active_show_name(&mut self.db_connection())
                .unwrap_or_default(),
        };
        self.pending_publish_to
            .push(super::PendingPublishTo::FixturesSse);
        self.pending_publish_to
            .push(super::PendingPublishTo::DmxRenderer);
        self.pending_publish_to
            .push(super::PendingPublishTo::DashboardSse);
    }
}
