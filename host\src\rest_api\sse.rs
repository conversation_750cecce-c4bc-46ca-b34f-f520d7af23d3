use crate::database_handler::PendingPublishTo;

use super::RouteState;
use crate::<PERSON>b<PERSON>and<PERSON>;
use async_stream;
use axum::extract::State;
use axum::response::sse::{Event, KeepAlive, Sse};
use core::convert::Infallible;
use core::time::Duration;
use futures_util::stream::Stream;

pub async fn fixtures_sse(
    State(state): State<RouteState>,
) -> Sse<impl Stream<Item = Result<Event, Infallible>>> {
    let stream = async_stream::stream! {
        #[allow(clippy::needless_continue)]
        loop {
            let mut serialized_fixtures = String::new();
            if let Ok(db_handler) = state.db_handler.lock() {
                if let Ok(temp_serialized_fixtures) = serde_json::to_string(&DbHandler::fixtures_for_active_show(&mut db_handler.db_connection())) {
                    serialized_fixtures = temp_serialized_fixtures;
                }
            } else {
                continue;
            }

            yield Ok(Event::default().data(serialized_fixtures));
            let mut waiting_for_change = true;
            while waiting_for_change {
                if let Ok(mut db_handler) = state.db_handler.lock() {
                    if db_handler.check_and_reset_if_new_state_was_received_for(
                        &PendingPublishTo::FixturesSse,
                    ) {
                        waiting_for_change = false;
                    }
                }
                tokio::time::sleep(Duration::from_millis(10)).await;
            }
        }
    };

    Sse::new(stream).keep_alive(KeepAlive::default())
}

pub async fn dashboard_sse(
    State(handlers): State<RouteState>,
) -> Sse<impl Stream<Item = Result<Event, Infallible>>> {
    let stream = async_stream::stream! {
        #[allow(clippy::needless_continue)]
        loop {
            let mut serialized_dashboard = String::new();
            if let Ok(dashboard) = handlers.dashboard.lock() {
                if let Ok(temp_serialized_dashboard) = serde_json::to_string(&(*dashboard)) {
                    serialized_dashboard = temp_serialized_dashboard;
                }
            } else {
                continue;
            }

            yield Ok(Event::default().data(serialized_dashboard));
            let mut waiting_for_change = true;
            while waiting_for_change {
                if let Ok(mut db_handler) = handlers.db_handler.lock() {
                    if db_handler.check_and_reset_if_new_state_was_received_for(
                        &PendingPublishTo::DashboardSse,
                    ) {
                        waiting_for_change = false;
                    }
                }
                tokio::time::sleep(Duration::from_millis(10)).await;
            }
        }
    };

    Sse::new(stream).keep_alive(KeepAlive::default())
}
