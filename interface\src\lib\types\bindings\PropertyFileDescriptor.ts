// This file was generated by [ts-rs](https://github.com/Aleph-Alpha/ts-rs). Do not edit this file manually.
import type { ColorPropertyCoordinate } from "./ColorPropertyCoordinate";
import type { InterpolationMethod } from "./InterpolationMethod";
import type { PanTiltPositionPropertyCoordinate } from "./PanTiltPositionPropertyCoordinate";
import type { SnippetCall } from "./SnippetCall";
import type { UnimplementedChannelPropertyCoordinate } from "./UnimplementedChannelPropertyCoordinate";

export type PropertyFileDescriptor = { "UnimplementedChannel": [string, Array<UnimplementedChannelPropertyCoordinate>, InterpolationMethod] } | { "PanTiltPositions": [Array<PanTiltPositionPropertyCoordinate>, InterpolationMethod] } | { "ColorPropertyCoordinates": [Array<ColorPropertyCoordinate>, InterpolationMethod] } | { "CallSnippet": SnippetCall };