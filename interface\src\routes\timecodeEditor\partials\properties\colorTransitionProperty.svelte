<script lang="ts">
    import P5jssketch from "$lib/molecules/p5jssketch.svelte";
    import { getContext, onMount, untrack } from "svelte";
    import type { ColorPropertyCoordinate } from "$lib/types/bindings/ColorPropertyCoordinate";
    import { remap } from "@anselan/maprange";

    interface Point {
        x: number;
        hue: number;
        moving: boolean;
        border: boolean;
    }

    const HEIGHT = 72;

    let rootElement: HTMLElement | undefined = $state();

    let {
        points = $bindable(),
    }: {
        points: ColorPropertyCoordinate[];
    } = $props();

    let processedPoints: Point[] = $state([]);

    let width = $derived(
        points && points.length >= 2 ? points[points.length - 1].x : 0,
    );

    $effect(() => {
        if (width && untrack(() => processedPoints.length >= 2)) {
            untrack(() => {
                processedPoints[processedPoints.length - 1].x = width;
                shouldRedraw = true;
            });
        }
    });

    $effect(() => {
        if (processedPoints.length) {
            points = processedPoints.map((pnt) => {
                return {
                    x: pnt.x,
                    color: rgbFromHue(pnt.hue),
                };
            });
        }
    });

    let scrollLeft = $state(0);
    let clientWidth = $state(0);
    let hovering = $state(false);
    let shouldRedraw = $state(true);

    onMount(() => {
        points.forEach((pnt) =>
            processedPoints.push({
                x: pnt.x,
                moving: false,
                border: false,
                hue: rgbToHsv(pnt.color.red, pnt.color.green, pnt.color.blue)
                    .hue,
            }),
        );
        processedPoints[0].border = true;
        processedPoints[processedPoints.length - 1].border = true;

        scrollLeft = rootElement?.scrollLeft ?? 0;
        clientWidth = rootElement?.clientWidth ?? 0;
    });

    function mouseLeave() {
        processedPoints.forEach((pnt) => (pnt.moving = false));
        processedPoints = processedPoints.sort((a, b) => (a.x > b.x ? 1 : -1));
        hovering = false;
    }

    function mouseEnter() {
        hovering = true;
    }

    let interaction_type = $state(0);
    const sketch = (p5: any) => {
        p5.setup = () => {
            p5.createCanvas(width, HEIGHT);
        };

        p5.draw = () => {
            if (!hovering && !shouldRedraw) return;
            p5.resizeCanvas(width, HEIGHT);
            p5.background(0);

            p5.strokeWeight(2);
            for (let i = 0; i < width - 1; i++) {
                let prev: { hue: number; x: number };
                let filteredProcessedPoint = processedPoints.filter(
                    (point) => point.x < i,
                );
                if (filteredProcessedPoint.length) {
                    prev =
                        filteredProcessedPoint[
                            filteredProcessedPoint.length - 1
                        ];
                } else {
                    prev = { hue: 0, x: 0 };
                }
                let next: { hue: number; x: number };
                filteredProcessedPoint = processedPoints.filter(
                    (point) => point.x > i,
                );
                if (filteredProcessedPoint.length) {
                    next = filteredProcessedPoint[0];
                } else {
                    next = { hue: 0, x: width };
                }

                let interpolatedHue: number = 0;
                try {
                    interpolatedHue = Math.floor(
                        remap(i, [prev.x, next.x], [prev.hue, next.hue]),
                    );
                } catch {
                    interpolatedHue = prev.hue;
                }
                p5.stroke(hexColorFromObject(rgbFromHue(interpolatedHue)));
                p5.line(i, 0, i, HEIGHT);
            }
            interaction_type = 0;
            processedPoints.forEach((pnt) => {
                if (p5.mouseX > pnt.x - 10 && p5.mouseX < pnt.x + 10) {
                    interaction_type = 1;
                }
            });
            p5.cursor(interaction_type === 0 ? p5.ARROW : p5.MOVE);
            processedPoints.forEach((pnt) => {
                if (
                    pnt.moving &&
                    !pnt.border &&
                    p5.mouseY < HEIGHT &&
                    p5.mouseY > 0 &&
                    p5.mouseX < width &&
                    p5.mouseX > 0
                ) {
                    pnt.x = p5.mouseX;
                }
            });

            processedPoints.forEach((pnt) =>
                pnt.x > width ? (pnt.x = width) : null,
            );

            let point = processedPoints.find((point) => point.moving);
            if (point) {
                if (p5.keyIsDown(171 /* + */)) {
                    point.hue += 1;
                } else if (p5.keyIsDown(173 /* - */)) {
                    point.hue -= 1;
                } else if (p5.keyIsDown(p5.UP_ARROW)) {
                    point.hue += 10;
                } else if (p5.keyIsDown(p5.DOWN_ARROW)) {
                    point.hue -= 10;
                }
                if (point.hue < 0) point.hue = 0;
                if (point.hue > 359) point.hue = 359;
            }
            p5.stroke(0);
            processedPoints.forEach((pnt) => {
                p5.strokeWeight(2);
                p5.line(pnt.x, 0, pnt.x, HEIGHT);
                p5.strokeWeight(6);
                p5.fill(hexColorFromObject(rgbFromHue(pnt.hue)));

                p5.circle(pnt.x, HEIGHT / 2, 15);
            });
            processedPoints = processedPoints.filter((pnt) => pnt.x >= 0);

            processedPoints.forEach((pnt) => (pnt.border = false));
            processedPoints[0].border = true;
            processedPoints[processedPoints.length - 1].border = true;
            shouldRedraw = false;
        };

        p5.mousePressed = () => {
            if (hovering) {
                if (interaction_type === 0) {
                    processedPoints.push({
                        x: p5.mouseX,
                        hue: p5.mouseY,
                        moving: false,
                        border: false,
                    });
                } else {
                    processedPoints.forEach((pnt) => (pnt.moving = false));
                    processedPoints.sort((a, b) =>
                        p5.dist(a.x, 0, p5.mouseX, 0) >
                        p5.dist(b.x, 0, p5.mouseX, 0)
                            ? 1
                            : -1,
                    )[0].moving = true;
                }
                processedPoints = processedPoints.sort((a, b) =>
                    a.x > b.x ? 1 : -1,
                );
            }
        };

        p5.mouseReleased = () => {
            processedPoints.forEach((pnt) => (pnt.moving = false));
            processedPoints = processedPoints.sort((a, b) =>
                a.x > b.x ? 1 : -1,
            );
        };

        p5.mouseDragged = () => {
            processedPoints = processedPoints.sort((a, b) =>
                a.x > b.x ? 1 : -1,
            );
        };

        p5.mouseWheel = (e: WheelEvent) => {
            if (interaction_type === 1) {
                let point = processedPoints.find((point) => point.moving);
                if (!point) return;
                point.hue += e.deltaY / 10;
                if (point.hue < 0) point.hue = 0;
                if (point.hue > 360) point.hue = 360;
            }
        };

        p5.keyPressed = (e: KeyboardEvent) => {
            if (hovering) {
                if (e.code === "Delete" && interaction_type === 1) {
                    processedPoints = processedPoints.filter(
                        (point) => !point.moving,
                    );
                }
            }
        };

        processedPoints = processedPoints;
    };

    function hexColorFromObject(objColor: {
        red: number;
        green: number;
        blue: number;
    }) {
        let red = objColor.red.toString(16);
        if (red.length === 1) {
            red = `0${red}`;
        }
        let green = objColor.green.toString(16);
        if (green.length === 1) {
            green = `0${green}`;
        }
        let blue = objColor.blue.toString(16);
        if (blue.length === 1) {
            blue = `0${blue}`;
        }
        return `#${red}${green}${blue}`;
    }

    function rgbFromHue(h: number) {
        if (h >= 0 && h < 360) {
            let s = 1;
            let v = 1;
            // @ts-ignore
            let f = (n, k = (n + h / 60) % 6) =>
                v - v * s * Math.max(Math.min(k, 4 - k, 1), 0);
            return {
                red: Math.floor(f(5) * 255),
                green: Math.floor(f(3) * 255),
                blue: Math.floor(f(1) * 255),
            };
        }
        return { red: 0, green: 0, blue: 0 };
    }
    function rgbToHsv(
        r: number,
        g: number,
        b: number,
    ): { hue: number; saturation: number; value: number } {
        r = r / 255;
        g = g / 255;
        b = b / 255;

        let max = Math.max(Math.max(r, g), b);
        let min = Math.min(Math.min(r, g), b);
        let delta = max - min;

        let hue;
        if (delta == 0) {
            hue = 0;
        } else if (max == r) {
            hue = (60.0 * ((g - b) / delta)) % 6.0;
        } else if (max == g) {
            hue = 60.0 * ((b - r) / delta + 2.0);
        } else {
            hue = 60.0 * ((r - g) / delta + 4.0);
        }

        let saturation = max === 0 ? 0 : delta / max;

        let value = max;

        return { hue, saturation, value };
    }
</script>

<div
    bind:this={rootElement}
    onmouseenter={() => mouseEnter()}
    onmouseleave={() => mouseLeave()}
    onscroll={(e) => {
        // @ts-ignore -> for some reason ts thinks, that there is no outerWidth
        clientWidth = e.target?.clientWidth ? e.target.clientWidth : 0;
        // @ts-ignore -> for some reason ts thinks, that there is no scrollLeft
        scrollLeft = e.target?.scrollLeft ? e.target.scrollLeft : 0;
    }}
    role="cell"
    tabindex={1}
>
    <P5jssketch {sketch} />
</div>
