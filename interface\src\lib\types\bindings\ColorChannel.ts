// This file was generated by [ts-rs](https://github.com/Aleph-Alpha/ts-rs). Do not edit this file manually.
import type { CmyChannels } from "./CmyChannels";
import type { ColorWheel } from "./ColorWheel";
import type { HsvChannels } from "./HsvChannels";
import type { RgbChannels } from "./RgbChannels";
import type { XyYChannels } from "./XyYChannels";

export type ColorChannel = { "RgbChannels": RgbChannels } | { "ColorWheel": ColorWheel } | { "CmyChannels": CmyChannels } | { "HsvChannels": HsvChannels } | { "XyzChannels": XyYChannels };