<script lang="ts" module>
    export const TRACK_HEIGHT = 90;
    export const PROPERTY_MIN_WIDTH = 25;
    export type Priority = "updateTrack" | "updateProperty" | "saveToBe";
</script>

<script lang="ts">
    import UnimplementedChannelProperty from "./properties/unimplementedChannelProperty.svelte";
    import ColorTransitionProperty from "./properties/colorTransitionProperty.svelte";
    import type { Track } from "$lib/types/bindings/Track";
    import Icon from "$lib/atoms/icon.svelte";
    import MultiselectDropdown from "$lib/molecules/multiselectDropdown.svelte";
    import { fixtures } from "$lib/stores/fixtures";
    import type { TimedPropertyFileDescriptor } from "$lib/types/bindings/TimedPropertyFileDescriptor";
    import CallSnippetProperty from "./properties/callSnippetProperty.svelte";
    import {
        applyMousemovementToProperty,
        calculateMaxPropertyWidth,
        getAllOtherXCoordinates,
        getSelectedPropertyBounds,
    } from "../+page.svelte";
    import { BEAT_INDEX_POINTS } from "./constants";
    import {mapRange} from "$lib/utils"

    let {
        track,
        removePropertyFromTrack,
        addPropertyToTrack,
        oninput,
        onchange,
        width,
        bpm,
    }: {
        track: Track;
        removePropertyFromTrack: (index: number) => any;
        addPropertyToTrack: (
            property: TimedPropertyFileDescriptor,
            xOffset: number,
        ) => any;
        oninput: (
            item:
                | { track: Track }
                | {
                      property: TimedPropertyFileDescriptor;
                      propertyIndex: number;
                  },
        ) => any;
        onchange: () => Promise<any>;
        width: number;
        bpm?: number;
    } = $props();

    let propertyMouseStates: {
        selectedIndex: number | null;
        hoveringIndex: number | null;
        moving: boolean;
        resizing: boolean;
    } = $state({
        selectedIndex: null,
        hoveringIndex: null,
        moving: false,
        resizing: false,
    });

    let isShiftKeyPressed = $state(false);

    export function calculateGridLines(width: number): number[] {
        if (!bpm || bpm <= 0) return [];

        let beatLengthInPixels = BEAT_INDEX_POINTS * mapRange(bpm, 180, 60, 0, 2)

        const gridLines: number[] = [];
        for (let i = 0; i * beatLengthInPixels < width; i++) {
            gridLines.push(
                i * beatLengthInPixels
            );
        }

        return gridLines;
    }
</script>

<svelte:window
    onkeydown={(e) => {
        if (e.key === 'Shift') {
            isShiftKeyPressed = true;
        }
    }}
    onkeyup={(e) => {
        if (e.key === 'Shift') {
            isShiftKeyPressed = false;
        }
    }}
    onmousemove={(e) => {
        if (propertyMouseStates.selectedIndex !== null) {
            let property = track.properties[propertyMouseStates.selectedIndex];
            if (propertyMouseStates.resizing) {
                if ("ColorPropertyCoordinates" in property) {
                    const allOtherXCoordinates = getAllOtherXCoordinates(
                        track,
                        propertyMouseStates.selectedIndex,
                    );
                    const maxPropertyWidth = calculateMaxPropertyWidth(
                        allOtherXCoordinates,
                        property,
                    );

                    const oldWidth =
                        property.ColorPropertyCoordinates[0][
                            property.ColorPropertyCoordinates[0].length - 1
                        ].x;
                    const newWidth = Math.min(
                        Math.max(oldWidth + e.movementX, PROPERTY_MIN_WIDTH),
                        maxPropertyWidth,
                    );
                    property.ColorPropertyCoordinates[0][
                        property.ColorPropertyCoordinates[0].length - 1
                    ].x = newWidth;
                } else if ("UnimplementedChannel" in property) {
                    const allOtherXCoordinates = getAllOtherXCoordinates(
                        track,
                        propertyMouseStates.selectedIndex,
                    );
                    const maxPropertyWidth = calculateMaxPropertyWidth(
                        allOtherXCoordinates,
                        property,
                    );

                    const oldWidth =
                        property.UnimplementedChannel[1][
                            property.UnimplementedChannel[1].length - 1
                        ].x;

                    const newWidth = Math.min(
                        Math.max(oldWidth + e.movementX, PROPERTY_MIN_WIDTH),
                        maxPropertyWidth,
                    );
                    property.UnimplementedChannel[1][
                        property.UnimplementedChannel[1].length - 1
                    ].x = newWidth;
                } else {
                    // CallSnippet blocks have no width
                    // PanTiltPositions are not present on timecodes
                }
                oninput({
                    property,
                    propertyIndex: propertyMouseStates.selectedIndex,
                });
            } else if (propertyMouseStates.moving) {
                const allOtherXCoordinates = getAllOtherXCoordinates(
                    track,
                    propertyMouseStates.selectedIndex,
                );
                const selectedPropertyBounds = getSelectedPropertyBounds(
                    allOtherXCoordinates,
                    property,
                );

                if (bpm && bpm > 0 && !isShiftKeyPressed) {
                    const gridLines = calculateGridLines(width);

                    let currentPosition = 0;
                    if ("ColorPropertyCoordinates" in property) {
                        currentPosition = property.ColorPropertyCoordinates[1];
                    } else if ("UnimplementedChannel" in property) {
                        currentPosition = property.UnimplementedChannel[2];
                    } else if ("CallSnippet" in property) {
                        currentPosition = property.CallSnippet[1];
                    }

                    const newPositionBeforeSnap = currentPosition + e.movementX;

                    let nearestGridLine = gridLines[0] || 0;
                    let minDistance = Math.abs(newPositionBeforeSnap - nearestGridLine);

                    for (const gridLine of gridLines) {
                        const distance = Math.abs(newPositionBeforeSnap - gridLine);
                        if (distance < minDistance) {
                            minDistance = distance;
                            nearestGridLine = gridLine;
                        }
                    }

                    const snapThreshold = 2;
                    if (minDistance <= snapThreshold) {
                        const snapMovement = nearestGridLine - currentPosition;

                        applyMousemovementToProperty(
                            snapMovement,
                            selectedPropertyBounds,
                            property,
                        );
                    } else {
                        applyMousemovementToProperty(
                            e.movementX,
                            selectedPropertyBounds,
                            property,
                        );
                    }
                } else {
                    applyMousemovementToProperty(
                        e.movementX,
                        selectedPropertyBounds,
                        property,
                    );
                }
            }
            oninput({ track });
        }
    }}
    onmouseup={() => {
        propertyMouseStates.moving = false;
        propertyMouseStates.resizing = false;
        isShiftKeyPressed = false;
    }}
/>

<div
    id={`track${track.id}`}
    class="relative flex w-full border-black border-solid border"
    style="height: {TRACK_HEIGHT}px;"
    role="row"
    tabindex={1}
    onmouseup={() => {
        onchange();
    }}
    onmousedown={async (e) => {
        const trackDiv = document.getElementById(`track${track.id}`);
        if (
            trackDiv &&
            propertyMouseStates.selectedIndex !== null &&
            e.target === trackDiv
        ) {
            addPropertyToTrack(
                track.properties[propertyMouseStates.selectedIndex],
                e.layerX,
            ).then(() => {
                propertyMouseStates.selectedIndex = track.properties.length - 1;
                propertyMouseStates.moving = true;
            });
        }
    }}
>
    {#if bpm && bpm > 0}
        {#each calculateGridLines(width) as gridLine}
            <div
                class="absolute h-full border-l border-gray-400 opacity-50"
                style="left: {gridLine}px;"
            ></div>
        {/each}
    {/if}
    {#if track}
        {#each track.properties as property, i}
            <div
                role="row"
                tabindex={1}
                onmousedown={() => {
                    // TODO: remove when app is mature
                    console.log({
                        xOffset:
                            "ColorPropertyCoordinates" in property
                                ? property.ColorPropertyCoordinates[1]
                                : "CallSnippet" in property
                                  ? property.CallSnippet[1]
                                  : property.UnimplementedChannel[2],
                        width:
                            "ColorPropertyCoordinates" in property
                                ? property.ColorPropertyCoordinates[0][
                                      property.ColorPropertyCoordinates[0]
                                          .length - 1
                                  ].x
                                : "CallSnippet" in property
                                  ? PROPERTY_MIN_WIDTH
                                  : property.UnimplementedChannel[1][
                                        property.UnimplementedChannel[1]
                                            .length - 1
                                    ].x,
                        xOffsetLast:
                            "ColorPropertyCoordinates" in property
                                ? property.ColorPropertyCoordinates[1] +
                                  property.ColorPropertyCoordinates[0][
                                      property.ColorPropertyCoordinates[0]
                                          .length - 1
                                  ].x
                                : "CallSnippet" in property
                                  ? property.CallSnippet[1] + PROPERTY_MIN_WIDTH
                                  : property.UnimplementedChannel[2] +
                                    property.UnimplementedChannel[1][
                                        property.UnimplementedChannel[1]
                                            .length - 1
                                    ].x,
                    });
                    propertyMouseStates.selectedIndex = i;
                }}
                onmouseenter={() => (propertyMouseStates.hoveringIndex = i)}
                onmouseleave={() => {
                    propertyMouseStates.hoveringIndex = null;
                }}
                class="absolute"
                style={`left:
                    ${
                        "ColorPropertyCoordinates" in property
                            ? property.ColorPropertyCoordinates[1]
                            : "CallSnippet" in property
                              ? property.CallSnippet[1]
                              : property.UnimplementedChannel[2]
                    }
                    px`
                    .replaceAll(" ", "")
                    .replaceAll("\n", "")}
            >
                <div
                    onmousedown={(e) => {
                        if (e.buttons === 4) {
                            removePropertyFromTrack(i);
                        } else if (e.buttons === 1) {
                            propertyMouseStates.moving = true;
                        }
                    }}
                    class="flex h-4 border-solid border-black border-2 bg-accent"
                    class:border-selected={propertyMouseStates.selectedIndex ===
                        i}
                    class:border-x-4={propertyMouseStates.selectedIndex === i}
                    class:border-solid={propertyMouseStates.selectedIndex === i}
                    role="row"
                    tabindex={1}
                >
                    <p class="text-xs -mt-0.5 overflow-hidden">
                        {"ColorPropertyCoordinates" in property
                            ? "Color"
                            : "CallSnippet" in property
                              ? "Snippet"
                              : property.UnimplementedChannel[0]}
                    </p>
                    <div class="ml-auto">
                        {#if "ColorPropertyCoordinates" in property}
                            <MultiselectDropdown
                                checkedIds={property
                                    .ColorPropertyCoordinates[2]}
                                onchange={(checkedIds) => {
                                    property.ColorPropertyCoordinates[2] =
                                        checkedIds;
                                    oninput({
                                        property,
                                        propertyIndex: i,
                                    });
                                }}
                                items={$fixtures}
                            >
                                {#snippet button()}
                                    <div class="flex">
                                        <Icon
                                            icon="cbi--bulb-group-golfball-e14"
                                        ></Icon>
                                        <p class="ml-auto">
                                            {property
                                                .ColorPropertyCoordinates[2]
                                                .length}
                                        </p>
                                    </div>
                                {/snippet}
                            </MultiselectDropdown>
                        {:else if "CallSnippet" in property}
                            <MultiselectDropdown
                                checkedIds={property.CallSnippet[2]}
                                onchange={(checkedIds) => {
                                    property.CallSnippet[2] = checkedIds;
                                    oninput({
                                        property,
                                        propertyIndex: i,
                                    });
                                }}
                                items={$fixtures}
                            >
                                {#snippet button()}
                                    <div class="flex">
                                        <Icon
                                            icon="cbi--bulb-group-golfball-e14"
                                        ></Icon>
                                        <p class="ml-auto">
                                            {property.CallSnippet[2].length}
                                        </p>
                                    </div>
                                {/snippet}
                            </MultiselectDropdown>
                        {:else}
                            <MultiselectDropdown
                                checkedIds={property.UnimplementedChannel[3]}
                                onchange={(checkedIds) => {
                                    property.UnimplementedChannel[3] =
                                        checkedIds;
                                    oninput({
                                        property,
                                        propertyIndex: i,
                                    });
                                }}
                                items={$fixtures}
                            >
                                {#snippet button()}
                                    <div class="flex h-[18px]">
                                        <Icon
                                            icon="cbi--bulb-group-golfball-e14"
                                        ></Icon>
                                        <p class="ml-auto">
                                            {property.UnimplementedChannel[3]
                                                .length}
                                        </p>
                                    </div>
                                {/snippet}
                            </MultiselectDropdown>
                        {/if}
                    </div>
                </div>
                <div class="flex flex-row">
                    {#if "ColorPropertyCoordinates" in property}
                        <ColorTransitionProperty
                            bind:points={property.ColorPropertyCoordinates[0]}
                        />
                    {:else if "CallSnippet" in property}
                        <CallSnippetProperty
                            bind:snippetId={property.CallSnippet[0]}
                        ></CallSnippetProperty>
                    {:else}
                        <UnimplementedChannelProperty
                            bind:points={property.UnimplementedChannel[1]}
                        />
                    {/if}
                    <div
                        class="w-1 hover:cursor-ew-resize bg-accent border-black border"
                        role="row"
                        tabindex={1}
                        onmousedown={() =>
                            (propertyMouseStates.resizing = true)}
                    ></div>
                </div>
            </div>
        {/each}
    {/if}
</div>
