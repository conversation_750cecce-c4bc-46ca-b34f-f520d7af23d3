#!/bin/bash

# This is intended for the rpi to create its own wifi network without direct acces to the internet.
# This is more of a POC and needs further testing.

# Update and upgrade the system
echo "Updating and upgrading the system..."
sudo apt update && sudo apt upgrade -y

# Install required packages
echo "Installing hostapd and dnsmasq..."
sudo apt install -y hostapd dnsmasq
sudo apt install -y dhcpcd5
sudo systemctl unmask hostapd

# Disable NetworkManager
sudo systemctl disable NetworkManager

# Configure a static IP address
echo "Configuring static IP address..."
cat <<EOL | sudo tee -a /etc/dhcpcd.conf
interface wlan0
static ip_address=***********/24
nohook wpa_supplicant
EOL

# Backup original dnsmasq configuration
echo "Backing up original dnsmasq configuration..."
sudo mv /etc/dnsmasq.conf /etc/dnsmasq.conf.orig

# Configure dnsmasq
echo "Configuring dnsmasq..."
cat <<EOL | sudo tee /etc/dnsmasq.conf
interface=wlan0      # Use the required interface
dhcp-range=***********,************,*************,24h
EOL

# Configure hostapd
echo "Configuring hostapd..."
cat <<EOL | sudo tee /etc/hostapd/hostapd.conf
interface=wlan0
driver=nl80211
ssid=rpi-test
hw_mode=g
channel=6
wmm_enabled=0
macaddr_acl=0
auth_algs=1
ignore_broadcast_ssid=0
wpa=2
wpa_passphrase=Lockwood
rsn_pairwise=CCMP
EOL

# Point hostapd to the configuration file
echo "Configuring hostapd default file..."
sudo sed -i 's|^DAEMON_CONF=.*|DAEMON_CONF="/etc/hostapd/hostapd.conf"|' /etc/default/hostapd

# Enable IP forwarding
echo "Enabling IP forwarding..."
sudo sed -i '/^#net.ipv4.ip_forward=1/s/^#//g' /etc/sysctl.conf

# Restart services
echo "Restarting services..."
sudo systemctl restart dhcpcd
sudo systemctl start dnsmasq
sudo systemctl start hostapd

# Enable services on boot
echo "Enabling services on boot..."
sudo systemctl enable dnsmasq
sudo systemctl enable hostapd

# Reboot the system
echo "Setup complete! Rebooting the system..."
sudo reboot
