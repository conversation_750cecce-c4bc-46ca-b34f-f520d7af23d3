<script>
    import { dashboard } from "$lib/stores/dashboard";
    import Card from "./partials/card.svelte";
    import { remap } from "@anselan/maprange";

    let bpmModifierText = $derived.by(() => {
        if ($dashboard.bpm_modifier > 128) {
            return `+ ${Math.round(remap($dashboard.bpm_modifier, [128, 255], [0, 100]))}%`;
        } else {
            return `- ${Math.round(remap($dashboard.bpm_modifier, [128, 0], [0, 100]))}%`;
        }
    });
</script>

{#if $dashboard && $dashboard.fixture_count !== undefined}
    <div class="grid grid-flow-row grid-cols-4 gap-4">
        <Card>
            <p class="text-9xl">{$dashboard.fixture_count}</p>
            <p class="text-right italic">fixtures</p>
        </Card>
        <Card>
            <div
                class="flex"
                class:text-9xl={$dashboard.universes.length <= 2}
                class:text-6xl={$dashboard.universes.length > 2}
                class:mb-16={$dashboard.universes.length > 2}
            >
                {#each $dashboard.universes as universe, index}
                    {#if index > 0},&nbsp;{/if}
                    <p>{universe}</p>
                {/each}
            </div>
            <div class="flex flex-col justify-end italic">
                {#if $dashboard.universes.length === 1}
                    <p class="text-right">universe</p>
                {:else}
                    <p class="text-right">universes</p>
                {/if}
            </div>
        </Card>
        <Card>
            <div class="flex h-[8rem] flex-col overflow-scroll">
                <table>
                    <thead> </thead>
                    <tbody>
                        {#each $dashboard.snippets as snippet}
                            <tr class="odd:bg-primary/25 hover:bg-surface/80">
                                <td>
                                    <p>{snippet[0]}</p>
                                </td>
                                <td>
                                    <p>{snippet[1]}</p>
                                </td>
                            </tr>
                        {/each}
                    </tbody>
                </table>
            </div>
            <p class="text-right italic">snippets</p>
        </Card>
        <Card
            bgColor={$dashboard.dbg_mode === "RELEASE" ? "#770000" : "#007700"}
        >
            <p class="mb-16 text-6xl">{$dashboard.dbg_mode}</p>
            <p class="text-right italic">mode</p>
        </Card>
        <div class:col-span-2={$dashboard.bpm_modifier !== 128}>
            <Card>
                <p class="text-9xl">
                    {$dashboard.bpm}
                    {$dashboard.bpm_modifier !== 128
                        ? `(${bpmModifierText})`
                        : ""}
                </p>
                <p class="text-right italic">bpm + modifier</p>
            </Card>
        </div>
    </div>
{/if}
