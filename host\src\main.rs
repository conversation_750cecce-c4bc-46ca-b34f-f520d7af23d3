extern crate alloc;

use alloc::sync::Arc;
use core::time::Duration;
use database_handler::{<PERSON><PERSON><PERSON><PERSON><PERSON>, PendingPublishTo};
use input_parser::modules::spawn_usb_port_collector;
use rest_api::spawn_rest_api;
use std::sync::Mutex;
use std::thread;
use std::time::Instant;

#[allow(
    clippy::useless_format,
    clippy::print_with_newline,
    clippy::expect_fun_call,
    clippy::search_is_some,
    clippy::single_match
)]
pub mod input_parser;
use input_parser::structs::InputParser;

#[allow(
    clippy::useless_format,
    clippy::new_without_default,
    clippy::print_with_newline,
    clippy::expect_fun_call,
    clippy::search_is_some
)]
pub mod dmx_renderer;
use dmx_renderer::{DmxRenderer, DMX_FRAME_RATE_IN_MS};

pub mod rest_api;

pub mod database_handler;

pub mod logging;
use logging::Dashboard;

#[tokio::main]
async fn main() -> Result<(), Box<dyn core::error::Error + Send + Sync>> {
    logging::mark_start(get_mode_string().as_str());

    //? setup
    let db_handler = Arc::new(Mutex::new(DbHandler::new()));
    #[allow(clippy::unreachable)]
    let mut db_handler_locked = db_handler.lock().unwrap_or_else(|e| {
        logging::log(
            format!("Failed to setup a locked db_handler with {e:?}"),
            logging::LogLevel::Warning,
            true,
        );
        std::process::exit(1);
    });
    let dmx_renderer =
        Arc::new(Mutex::new(DmxRenderer::new(&mut db_handler_locked)));
    let input_parser = Arc::new(Mutex::new(InputParser::new()));
    let dashboard = Arc::new(Mutex::new(Dashboard::default()));
    let mut dashboard_locked = dashboard.lock().unwrap_or_else(|e| {
        logging::log(
            format!("Failed to setup a locked dashboard with {e:?}"),
            logging::LogLevel::Warning,
            true,
        );
        std::process::exit(1);
    });
    dashboard_locked.dbg_mode = get_mode_string();

    drop(db_handler_locked);
    drop(dashboard_locked);

    spawn_usb_port_collector(Arc::clone(&input_parser));
    spawn_rest_api(
        Arc::clone(&db_handler),
        Arc::clone(&dmx_renderer),
        Arc::clone(&input_parser),
        Arc::clone(&dashboard),
    );

    let mut leftover_beat_delta = 0.;
    loop {
        let fps_limit_timestamp = Instant::now();
        let Ok(mut dmx_renderer_locked) = dmx_renderer.try_lock() else {
            continue;
        };
        let Ok(mut input_parser_locked) = input_parser.try_lock() else {
            continue;
        };
        let Ok(mut db_handler_locked) = db_handler.try_lock() else {
            continue;
        };
        let Ok(mut dashboard_locked) = dashboard.try_lock() else {
            continue;
        };

        let beat_delta = dmx_renderer_locked.compute_beat_delta();
        leftover_beat_delta += beat_delta;

        let bpm = dmx_renderer_locked.bpm();
        let bpm_modifier = dmx_renderer_locked.bpm_modifier;
        #[allow(clippy::as_conversions, clippy::cast_precision_loss)]
        input_parser_locked.process_input(
            &mut db_handler_locked,
            &mut dmx_renderer_locked,
            leftover_beat_delta
                >= DmxRenderer::sixteenth_duration(bpm, bpm_modifier)
                    .as_millis() as f32,
        );

        dmx_renderer_locked.render(
            &mut db_handler_locked,
            &mut input_parser_locked,
            beat_delta,
        );

        leftover_beat_delta =
            input_parser_locked.reduce_delays(beat_delta, leftover_beat_delta);

        if db_handler_locked.check_and_reset_if_new_state_was_received_for(
            &PendingPublishTo::DmxRenderer,
        ) {
            dmx_renderer_locked.merge_new_fixtures_with_state(
                DbHandler::fixtures_for_active_show(
                    &mut db_handler_locked.db_connection(),
                ),
            );
        }

        if dmx_renderer_locked.fill_dashboard(&mut dashboard_locked)
            || db_handler_locked.fill_dashboard(&mut dashboard_locked)
        {
            db_handler_locked.trigger_dashboard_update();
        }

        // Give `rest-api` time to aquire locks
        drop(dmx_renderer_locked);
        drop(input_parser_locked);
        drop(db_handler_locked);
        drop(dashboard_locked);

        thread::sleep(Duration::from_millis(
            u128::from(DMX_FRAME_RATE_IN_MS)
                .saturating_sub(fps_limit_timestamp.elapsed().as_millis())
                .try_into()
                .unwrap_or(u64::MAX),
        ));
    }
}

#[cfg(debug_assertions)]
fn get_mode_string() -> String {
    String::from("DEBUG")
}
#[cfg(not(debug_assertions))]
fn get_mode_string() -> String {
    String::from("RELEASE")
}
