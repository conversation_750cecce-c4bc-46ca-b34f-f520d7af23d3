import { get, writable } from 'svelte/store';
import type { ComposedPanTiltPosition } from '$lib/types/bindings/ComposedPanTiltPosition';
import { shows } from './shows';
import type { PanTiltPositionRenamePayload } from '$lib/types/bindings/PanTiltPositionRenamePayload';
import type { PanTiltPositionDeletePayload } from '$lib/types/bindings/PanTiltPositionDeletePayload';
import { networking } from './networking';

function createPositionsStore() {
    const { subscribe, set } = writable<ComposedPanTiltPosition[]>([]);

    shows.subscribe(() => fetchPositions().then(data => set(data)));

    return {
        subscribe,
        createNew: (name: string) =>
            createNewPosition(name).then(data => set(data)),
        updateFixturePositions: (
            position_id: number,
        ) => updateFixturePositions(position_id).then(data => {
            set(data);
            return true;
        }),
        rename: (
            name: string,
            position: ComposedPanTiltPosition | undefined,
        ) => rename(name, position).then(data => set(data)),
        deletePosition: (position: ComposedPanTiltPosition | undefined) =>
            deletePosition(position).then(data => set(data)),
    };
}

export const positions = createPositionsStore();

async function fetchPositions(): Promise<ComposedPanTiltPosition[]> {
    return new Promise(async (resolve, _) => {
        const ip = get(networking);
        if (ip) {
            const response = await fetch(`http://${ip}:${networking.port}/positions`);
            resolve(response.json());
        } else {
            setTimeout(async () => resolve(await fetchPositions()), 1000)
        }
    })
}

async function createNewPosition(name: string): Promise<ComposedPanTiltPosition[]> {
    await fetch(`http://${get(networking)}:${networking.port}/createpantiltposition`, {
        method: 'POST',
        body: JSON.stringify({ position_name: name }),
        headers: new Headers({ 'content-type': 'application/json' }),
    });
    return fetchPositions();
}
async function rename(
    name: string,
    position: ComposedPanTiltPosition | undefined,
): Promise<ComposedPanTiltPosition[]> {
    if (position) {
        let renamePositionPayload: PanTiltPositionRenamePayload = {
            name,
            id: position.id,
        };
        await fetch(`http://${get(networking)}:${networking.port}/position`, {
            method: 'PATCH',
            body: JSON.stringify(renamePositionPayload),
            headers: new Headers({ 'content-type': 'application/json' }),
        });
    }
    return fetchPositions();
}
async function updateFixturePositions(
    id: number
): Promise<ComposedPanTiltPosition[]> {
    await fetch(`http://${get(networking)}:${networking.port}/updatepantiltposition/${id}`, {
        method: 'PATCH',
    });
    return fetchPositions();
}
async function deletePosition(
    position: ComposedPanTiltPosition | undefined,
): Promise<ComposedPanTiltPosition[]> {
    if (position) {
        let panTiltPositionDeletePayload: PanTiltPositionDeletePayload = {
            id: position.id,
        };
        await fetch(`http://${get(networking)}:${networking.port}/position`, {
            method: 'DELETE',
            body: JSON.stringify(panTiltPositionDeletePayload),
            headers: new Headers({ 'content-type': 'application/json' }),
        });
    }
    return fetchPositions();
}
