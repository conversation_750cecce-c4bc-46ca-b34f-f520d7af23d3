#![no_std]

use core::cell::RefCell;
use rwm_package_definitions::RawInput;

use arduino_hal::port::{
    mode::{Input, Output, PullUp},
    Pin,
};

pub trait ButtonTick {
    fn tick(&mut self, id: u16) -> Option<RawInput>;
}

pub struct Button<'a, IP, OP> {
    pressed: bool,
    in_pin: &'a RefCell<Pin<Input<PullUp>, IP>>,
    out_pin: &'a RefCell<Pin<Output, OP>>,
}
impl<'a, IP, OP> Button<'a, IP, OP>
where
    IP: arduino_hal::port::PinOps,
    OP: arduino_hal::port::PinOps,
{
    pub fn new(
        in_pin: &'a RefCell<Pin<Input<PullUp>, IP>>,
        out_pin: &'a RefCell<Pin<Output, OP>>,
    ) -> Self {
        Self {
            pressed: false,
            in_pin,
            out_pin,
        }
    }
}
impl<'a, IP, OP> ButtonTick for Button<'a, IP, OP>
where
    IP: arduino_hal::port::PinOps,
    OP: arduino_hal::port::PinOps,
{
    fn tick(&mut self, id: u16) -> Option<RawInput> {
        let in_pin = self.in_pin.borrow_mut();
        let mut out_pin = self.out_pin.borrow_mut();

        out_pin.set_low();
        avr_device::asm::delay_cycles(16); // ~<1 microsecond(unconfirmed)
        let currently_pressed = in_pin.is_low();
        out_pin.set_high();

        if currently_pressed && !self.pressed {
            self.pressed = true;
            Some(RawInput { id, value: 1 })
        } else if !currently_pressed && self.pressed {
            self.pressed = false;
            Some(RawInput { id, value: 0 })
        } else {
            None
        }
    }
}
