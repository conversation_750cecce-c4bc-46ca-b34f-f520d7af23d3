<script lang="ts">
    import Button from "$lib/atoms/button.svelte";
    import Icon from "$lib/atoms/icon.svelte";
    import LabeledTextinput from "$lib/molecules/labeled_textinput.svelte";
    import LabeledNumberinput from "$lib/molecules/labeled_numberinput.svelte";
    import LabeledDropdown from "$lib/molecules/labeled_dropdown.svelte";
    import type { FixtureType, ShowFixtureInstance } from "$lib/types/bindings/FixtureType";
    import { fixtureTypes, showFixtureInstances } from "$lib/stores/fixtureTypes";
    import { onMount } from "svelte";

    let selectedFixtureTypeId: number = $state(0);
    let quantity: number = $state(1);
    let namePrefix: string = $state("");

    onMount(async () => {
        await fixtureTypes.refresh();
        await showFixtureInstances.refresh();
    });

    async function createFixtureInstances() {
        if (selectedFixtureTypeId === 0 || !namePrefix.trim()) {
            return;
        }

        const newInstance: ShowFixtureInstance = {
            show_id: 1, // This would come from the active show
            fixture_type_id: selectedFixtureTypeId,
            quantity: quantity,
            name_prefix: namePrefix.trim()
        };

        await showFixtureInstances.create(newInstance);
        
        // Reset form
        selectedFixtureTypeId = 0;
        quantity = 1;
        namePrefix = "";
    }

    let selectedFixtureType = $derived($fixtureTypes.find(ft => ft.id === selectedFixtureTypeId));
</script>

<div class="fixture-instances-manager">
    <h2>Create Fixtures from Types</h2>
    
    <div class="create-section rounded-lg bg-object p-6">
        <h3>Add Fixtures to Show</h3>
        
        <div class="form-grid">
            <LabeledDropdown label="Fixture Type" bind:value={selectedFixtureTypeId}>
                <option value={0}>Select a fixture type...</option>
                {#each $fixtureTypes as fixtureType}
                    <option value={fixtureType.id}>{fixtureType.name} ({fixtureType.fixturetype})</option>
                {/each}
            </LabeledDropdown>

            <LabeledNumberinput 
                label="Quantity" 
                bind:value={quantity} 
                min={1} 
                max={100}
            />
            
            <LabeledTextinput 
                label="Name Prefix" 
                bind:value={namePrefix}
                placeholder="e.g. 'Stage Left', 'Wash'"
            />
        </div>

        {#if selectedFixtureType}
            <div class="fixture-type-preview">
                <h4>Selected Type: {selectedFixtureType.name}</h4>
                <div class="type-info">
                    <span><strong>Type:</strong> {selectedFixtureType.fixturetype}</span>
                    <span><strong>Footprint:</strong> {selectedFixtureType.footprint_size} channels</span>
                    <span><strong>Movement:</strong> {selectedFixtureType.movement_channels ? 'Yes' : 'No'}</span>
                    <span><strong>Color:</strong> {selectedFixtureType.color ? 'Yes' : 'No'}</span>
                </div>
                
                {#if quantity > 1}
                    <p>Will create {quantity} fixtures named: {namePrefix}_1, {namePrefix}_2, ...</p>
                {:else}
                    <p>Will create 1 fixture named: {namePrefix}</p>
                {/if}
            </div>
        {/if}

        <div class="form-actions">
            <Button 
                id="create-fixtures" 
                onclick={createFixtureInstances}
                disabled={selectedFixtureTypeId === 0 || !namePrefix.trim()}
            >
                <Icon icon="mdi:add" />
                Create {quantity > 1 ? `${quantity} Fixtures` : 'Fixture'}
            </Button>
        </div>
    </div>

    <div class="existing-instances">
        <h3>Current Show Fixtures</h3>
        
        <div class="instances-grid">
            {#each $showFixtureInstances as instance}
                {@const fixtureType = $fixtureTypes.find(ft => ft.id === instance.fixture_type_id)}
                <div class="instance-card rounded-lg bg-object p-4">
                    <div class="card-header">
                        <h4>{instance.name_prefix}</h4>
                        <span class="quantity-badge">{instance.quantity}x</span>
                    </div>
                    
                    {#if fixtureType}
                        <div class="card-content">
                            <p><strong>Type:</strong> {fixtureType.name}</p>
                            <p><strong>Fixture Type:</strong> {fixtureType.fixturetype}</p>
                            <p><strong>Footprint:</strong> {fixtureType.footprint_size} channels each</p>
                        </div>
                    {/if}
                    
                    <div class="card-actions">
                        <!-- Future: Add edit/delete actions -->
                    </div>
                </div>
            {/each}
            
            {#if $showFixtureInstances.length === 0}
                <div class="empty-state">
                    <Icon icon="mdi:lightbulb-outline" />
                    <p>No fixtures in this show yet. Create some using the form above!</p>
                </div>
            {/if}
        </div>
    </div>
</div>

<style>
    .fixture-instances-manager {
        display: flex;
        flex-direction: column;
        gap: 2rem;
    }

    .form-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .fixture-type-preview {
        @apply rounded-lg border border-input bg-primary p-4;
        margin-bottom: 1rem;
    }

    .type-info {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
        margin: 1rem 0;
    }

    .type-info span {
        @apply rounded px-2 py-1 bg-object;
        font-size: 0.875rem;
    }

    .form-actions {
        display: flex;
        justify-content: flex-end;
    }

    .instances-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 1rem;
    }

    .instance-card {
        border: 1px solid var(--color-border);
    }

    .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }

    .quantity-badge {
        @apply rounded-full bg-primary px-2 py-1;
        font-size: 0.75rem;
        font-weight: bold;
    }

    .empty-state {
        grid-column: 1 / -1;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1rem;
        padding: 3rem;
        color: var(--color-text-secondary);
    }

    .empty-state :global(svg) {
        font-size: 3rem;
        opacity: 0.5;
    }
</style>