<script lang="ts">
    import { onMount } from "svelte";
    import { fixtures } from "$lib/stores/fixtures";
    import { networking } from "$lib/stores/networking";
    import type { DmxFixtureFileDescriptor } from "$lib/types/bindings/DmxFixtureFileDescriptor";
    import { get } from "svelte/store";

    onMount(async () => {
        await_ip();
    });

    async function await_ip() {
        return new Promise(async (resolve, _) => {
            const ip = get(networking);
            if (ip) {
                attach_sse(ip);
            } else {
                setTimeout(async () => resolve(await await_ip()), 1000);
            }
        });
    }

    async function attach_sse(ip: string) {
        const eventSource = new EventSource(
            `http://${ip}:${networking.port}/fixtures/sse`,
        );
        eventSource.addEventListener("message", (event) => {
            try {
                let unsorted_fixtures: DmxFixtureFileDescriptor[] = JSON.parse(
                    event.data,
                );
                let sorted_fixtures = unsorted_fixtures
                    .sort((a, b) => {
                        const a_name_id = parseInt(
                            a.name.split("_").slice(-1)[0] ?? "0",
                        );
                        const b_name_id = parseInt(
                            b.name.split("_").slice(-1)[0] ?? "0",
                        );
                        return a_name_id - b_name_id;
                    })
                    .sort((a, b) => {
                        let a_name = weight(a.name.split("_")[0]);
                        let b_name = weight(b.name.split("_")[0]);
                        return a_name - b_name;
                    });

                $fixtures = sorted_fixtures;
            } catch {
                console.error("Unable to parse fixtures from the backend");
            }
        });
    }

    function weight(stringToParse: string): number {
        let splittedString = stringToParse.split("");
        let sum = splittedString
            .map((str) => str.charCodeAt(0))
            .reduce((ps, a) => ps + a, 0);
        return sum;
    }
</script>
