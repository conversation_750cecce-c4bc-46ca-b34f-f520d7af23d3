<script lang="ts" module>
    import { javascriptGenerator } from "blockly/javascript";

    export function generateCode() {
        let code = javascriptGenerator.workspaceToCode();
        code = code.replaceAll("}{", "},{");
        code = code.replaceAll("TRUE", "true");
        code = code.replaceAll("FALSE", "false");
        code = code.replace("MovingHeadBack", "Moving Head Back");
        console.assert(
            !code.includes("} "),
            `Found an instance of "} " on char ${code.indexOf("} ")}. This should be replaced with "}".
		          \n(Warning: Do not trim the 'code' string, as this leads to other bugs.)`,
        );
        return code;
    }
    export function generateWorkspaceSnapshot() {
        const workspace = Blockly.getMainWorkspace();
        const state = Blockly.serialization.workspaces.save(workspace);
        return state;
    }
    export function restoreBackpack(backpack: backpackPkg.Backpack) {
        if ("blockly-backpackcontent" in localStorage) {
            backpack.setContents(
                JSON.parse(
                    localStorage.getItem("blockly-backpackcontent") ?? "",
                ),
            );
        }
    }
</script>

<script lang="ts">
    import { onMount } from "svelte";
    import * as Blockly from "blockly/core";
    import * as field_color_svg_sliders from "@blockly/field-colour-hsv-sliders";
    import * as field_slider from "@blockly/field-slider";
    import * as backpackPkg from "@blockly/workspace-backpack";
    // @ts-ignore -> For whatever reason, ts cant find this in `node_modules` (but its there)
    import DarkTheme from "@blockly/theme-dark";
    import { JavascriptGenerator } from "blockly/javascript";
    import { fixtures } from "$lib/stores/fixtures";
    import { blueprints } from "$lib/stores/blueprints";
    import { positions } from "$lib/stores/positions";
    import { fixtureGroups } from "$lib/stores/fixtureGroups";
    import { TOAST } from "$lib/stores/toast";
    import {
        CATEGORY_COLOR,
        getLastConnectionOfConnection,
        type BlockInstanciator,
    } from "../+page.svelte";
    import type { InstructionValue } from "$lib/types/bindings/InstructionValue";
    import {
        snippetDir,
        allLeafInstructionsOfAllSnippets,
        allSnippetsOf,
        allCallableSnippets,
    } from "$lib/stores/snippets";
    import type { Instruction } from "$lib/types/bindings/Instruction";
    import type { Snippet } from "$lib/types/bindings/Snippet";
    import type { UnimplementedChannel } from "$lib/types/bindings/UnimplementedChannel";
    import type { DmxFixtureFileDescriptor } from "$lib/types/bindings/DmxFixtureFileDescriptor";

    const { FieldColourHsvSliders } = field_color_svg_sliders;
    const { FieldSlider } = field_slider;

    // @ts-ignore -> We do not fill this up front to save allot of repetitive code
    export let blockinstanciators: BlockInstanciator = {};

    export let isBuildable: boolean = false;

    export let blocklyBackpack: backpackPkg.Backpack | undefined;

    function uniqFixtureNames(): [number, string][] {
        if (!fixtures || !$fixtures.length) return [[0, "-"]];
        return [
            [0, "-"],
            ...new Set(
                $fixtures.map((fixture) => {
                    const id: number = fixture.id ?? 0;
                    const name: string = fixture.name ?? "";
                    return [id, name] as [number, string];
                }),
            ),
        ];
    }
    function uniqGroupNames(): string[] {
        if (!snippetDir || !allSnippetsOf($snippetDir).length) return ["-"];

        return ["-", ...$fixtureGroups.map((group) => group.name)];
    }
    function uniqEditableGroupNames(): string[] {
        if (!snippetDir || !allSnippetsOf($snippetDir).length) return ["-"];

        return [
            "-",
            ...$fixtureGroups
                .filter((group) => group.id !== null && !group.immutable)
                .map((group) => group.name),
        ];
    }
    function uniqueVariableNames(): string[] {
        if (!snippetDir || !allSnippetsOf($snippetDir).length) return ["-"];
        return [
            "-",
            ...allLeafInstructionsOfAllSnippets()
                .filter(
                    (instruction) =>
                        instruction.instruction === "CreateVariable",
                )
                .map(
                    (instruction) =>
                        // @ts-ignore -> we filtered for `CreateVariable`, so we are sure, that `instructionMod` exists
                        instruction.instructionMod,
                )
                .map((varName) => [varName])
                .flat(),
        ];
    }
    function keypointsOfChannelAsBlocklyArray(
        channel_name: string,
        fixtures: DmxFixtureFileDescriptor[],
    ): [string, string][] {
        let allChannels = allUnimplementedChannels(fixtures);
        if (!allChannels.length) return [["-", "-"]];
        let channels_stringified: [string, string][] = allChannels
            .filter((channel) => channel.name === channel_name)
            .map((channel) => channel.keypoints)
            .flat()
            .map((keypoint) => [keypoint.name, keypoint.id?.toString() ?? "0"]);
        let result: [string, string][] = [["-", "-"], ...channels_stringified];
        return result;
    }
    function couldBeSelectedFixtures(
        block: Blockly.Block,
    ): DmxFixtureFileDescriptor[] {
        while (block && block.type !== "fixtureloop") {
            const parent = block.getSurroundParent();
            if (parent) {
                block = parent;
            } else {
                return [];
            }
        }
        let fixtureloop = block;
        let descendants = fixtureloop.getDescendants(false);

        let possibleFixtureIds: (string | null)[] = [];
        let possibleFixtureTypes: string[] = [];

        descendants.forEach((descendant) => {
            if (descendant && descendant.type === "activateactionfixtureid") {
                possibleFixtureIds.push(descendant.getFieldValue("fixtureId"));
            } else if (
                descendant &&
                descendant.type === "activateactionallfixtures"
            ) {
                possibleFixtureIds = $fixtures.map(
                    (fixture) => fixture.id?.toString() ?? "-",
                );
            } else if (
                descendant &&
                descendant.type === "activateactionfixturegroup"
            ) {
                possibleFixtureIds = $fixtures.map(
                    (fixture) => fixture.id?.toString() ?? "-",
                );
            }
        });
        return $fixtures.filter(
            (fixture) =>
                possibleFixtureIds.includes(fixture.id?.toString() ?? "-") ||
                possibleFixtureTypes.includes(fixture.fixturetype),
        );
    }
    function allSnippetsAsBlocklyArray(
        snippets: Snippet[] = allSnippetsOf($snippetDir),
    ): [string, string][] {
        const blocklyfiedSnippets: [string, string][] = snippets.map(
            (snippet) => [`${snippet.name}`, `${snippet.id.toString()}`],
        );
        return [["-", "-"], ...blocklyfiedSnippets];
    }
    function allUnimplementedChannels(
        fixtures: DmxFixtureFileDescriptor[] = $fixtures,
    ): UnimplementedChannel[] {
        if (!fixtures.length) return [];
        return fixtures.map((fixture) => fixture.unimplemented_channels).flat();
    }
    function allUnimplementedChannelsAsNameStrings(
        fixtures: DmxFixtureFileDescriptor[] = $fixtures,
    ): string[] {
        let allChannels = allUnimplementedChannels(fixtures);
        if (!allChannels.length) return ["-"];
        return ["-", ...new Set(allChannels.map((channel) => channel.name))];
    }
    onMount(() => {
        const blocklyDiv = document.getElementById("blocklyDiv");
        const blocklyToolboxDiv = document.getElementById("toolbox");
        if (!blocklyDiv || !blocklyToolboxDiv) {
            console.error(
                "No `blocklyDiv` or `blocklyToolboxDiv` found in dom",
            );
            return;
        }

        const workspace = Blockly.inject(blocklyDiv, {
            toolbox: blocklyToolboxDiv,
            theme: DarkTheme,
        });

        const backpack = new backpackPkg.Backpack(workspace);
        backpack.init();
        restoreBackpack(backpack);
        const backpackSavingInterval = setInterval(
            () =>
                localStorage.setItem(
                    "blockly-backpackcontent",
                    JSON.stringify(backpack.getContents()),
                ),
            100,
        );
        blocklyBackpack = backpack;

        return () => {
            clearInterval(backpackSavingInterval);
            workspace.dispose();
        };
    });

    function shouldFixtureblockDisconnect(
        parent: Blockly.Block | null,
    ): boolean {
        if (!isBuildable) return false;

        while (parent) {
            if (
                parent.getInput("fixtures") ||
                parent.type === "addtogroup" ||
                parent.type === "removefromgroup"
            ) {
                return false;
            }
            parent = parent?.getSurroundParent();
        }
        return true;
    }
    Blockly.Blocks["activateactionallfixtures"] = {
        init: function () {
            this.appendDummyInput("activateactionallfixtures").appendField(
                "All fixtures",
            );
            this.setPreviousStatement(true, null);
            this.setNextStatement(true, null);
            this.setColour(CATEGORY_COLOR.FIXTURES);
            this.setOnChange(() => {
                let parent = this?.getSurroundParent();
                if (shouldFixtureblockDisconnect(parent)) {
                    this.unplug(true);
                }
            });
        },
    };
    javascriptGenerator.forBlock["activateactionallfixtures"] = () => {
        return `{"instruction": "ActivateAllFixtures"}`;
    };
    blockinstanciators.ActivateAllFixtures = (workspace: Blockly.Workspace) => {
        let block = workspace.newBlock("activateactionallfixtures");
        (block as Blockly.BlockSvg).initSvg();
        return block;
    };
    Blockly.Blocks["activateactionfixtureid"] = {
        init: function () {
            this.appendDummyInput("activateactionfixtureid")
                .appendField("fixturename")
                .appendField(
                    new Blockly.FieldDropdown(
                        uniqFixtureNames().map((fixture) => [
                            fixture[1],
                            fixture[0].toString(),
                        ]),
                    ),
                    "fixtureId",
                );
            this.setPreviousStatement(true, null);
            this.setNextStatement(true, null);
            this.setColour(CATEGORY_COLOR.FIXTURES);
            this.setTooltip("");
            this.setHelpUrl("");
            this.setOnChange(() => {
                let parent = this?.getSurroundParent();
                if (shouldFixtureblockDisconnect(parent)) {
                    this.unplug(true);
                }
            });
        },
        dropdownsFilled: function () {
            return this.getFieldValue("fixtureId") !== "-";
        },
    };
    javascriptGenerator.forBlock["activateactionfixtureid"] = (
        block: Blockly.Block,
    ) => {
        return `{"instruction": "ActivateFixtureById", "instructionMod": ${block.getFieldValue("fixtureId")}}`;
    };
    blockinstanciators.ActivateFixtureById = (
        workspace: Blockly.Workspace,
        thisInstruction: Instruction,
    ) => {
        if (thisInstruction.instruction !== "ActivateFixtureById") return;
        let block = workspace.newBlock("activateactionfixtureid");
        block.setFieldValue(
            thisInstruction.instructionMod.toString(),
            "fixtureId",
        );
        (block as Blockly.BlockSvg).initSvg();
        return block;
    };
    Blockly.Blocks["activateactionfixturegroup"] = {
        init: function () {
            this.appendDummyInput("activateactionfixturegroup")
                .appendField("Fixturegroup")
                .appendField(
                    new Blockly.FieldDropdown(
                        uniqGroupNames().map((groupName) => [
                            groupName,
                            groupName,
                        ]),
                    ),
                    "groupname",
                );
            this.setPreviousStatement(true, null);
            this.setNextStatement(true, null);
            this.setColour(CATEGORY_COLOR.FIXTURES);
            this.setTooltip("");
            this.setHelpUrl("");
            this.setOnChange(() => {
                let parent = this?.getSurroundParent();
                if (shouldFixtureblockDisconnect(parent)) {
                    this.unplug(true);
                }

                while (parent) {
                    if (
                        (parent.type === "addtogroup" ||
                            parent.type === "removefromgroup") &&
                        parent.getFieldValue("groupname") ===
                            this.getFieldValue("groupname")
                    ) {
                        this.unplug(true);
                        return;
                    }
                    parent = parent?.getSurroundParent();
                }
            });
        },
        dropdownsFilled: function () {
            return this.getFieldValue("groupname") !== "-";
        },
    };
    javascriptGenerator.forBlock["activateactionfixturegroup"] = (
        block: Blockly.Block,
    ) => {
        return `{"instruction": "ActivateFixtureGroup", "instructionMod": "${block.getFieldValue("groupname")}"}`;
    };
    blockinstanciators.ActivateFixtureGroup = (
        workspace: Blockly.Workspace,
        thisInstruction: Instruction,
    ) => {
        if (thisInstruction.instruction !== "ActivateFixtureGroup") return;
        let block = workspace.newBlock("activateactionfixturegroup");
        block.setFieldValue(thisInstruction.instructionMod, "groupname");
        (block as Blockly.BlockSvg).initSvg();
        return block;
    };
    Blockly.Blocks["bpmto"] = {
        init: function () {
            this.appendValueInput("bpm").appendField("set bpm to");
            this.setPreviousStatement(true, null);
            this.setNextStatement(true, null);
            this.setOutput(false, null);
            this.setColour(CATEGORY_COLOR.TIME);
            this.setTooltip("");
            this.setHelpUrl("");
        },
    };
    javascriptGenerator.forBlock["bpmto"] = function (
        block: Blockly.Block,
        generator: JavascriptGenerator,
    ) {
        const value = generator.statementToCode(block, "bpm");
        return `{"instruction": "BpmTo", "instructionMod": ${value}}`;
    };
    blockinstanciators.BpmTo = (
        workspace: Blockly.Workspace,
        thisInstruction: Instruction,
    ) => {
        if (thisInstruction.instruction !== "BpmTo") return;
        let block = workspace.newBlock("bpmto");
        let value = getInstanciatedValueBlock(
            "set bpm to",
            workspace,
            thisInstruction.instructionMod,
        );
        if (value?.outputConnection) {
            block.getInput("bpm")?.connection?.connect(value.outputConnection);
        }
        (block as Blockly.BlockSvg).initSvg();
        return block;
    };
    Blockly.Blocks["bpmmodifierto"] = {
        init: function () {
            this.appendValueInput("modifier").appendField(
                "set bpm modifier to",
            );
            this.setPreviousStatement(true, null);
            this.setNextStatement(true, null);
            this.setOutput(false, null);
            this.setColour(CATEGORY_COLOR.TIME);
            this.setTooltip(
                'Setting the Bpm Modifier requires some thoughts, as the input takes values from 0 to 100. This will be converted in the background, so that 50 is "unmodified". Below and above modifies the beat respectively. This seems like an extra step, but it makes working with faders really easy, because they will always produce values in that range.',
            );
            this.setHelpUrl("");
        },
    };
    javascriptGenerator.forBlock["bpmmodifierto"] = function (
        block: Blockly.Block,
        generator: JavascriptGenerator,
    ) {
        const value = generator.statementToCode(block, "modifier");
        return `{"instruction": "BpmModifierTo", "instructionMod": ${value}}`;
    };
    blockinstanciators.BpmModifierTo = (
        workspace: Blockly.Workspace,
        thisInstruction: Instruction,
    ) => {
        if (thisInstruction.instruction !== "BpmModifierTo") return;
        let block = workspace.newBlock("bpmmodifierto");
        let value = getInstanciatedValueBlock(
            "set bpm modifier to",
            workspace,
            thisInstruction.instructionMod,
        );
        if (value?.outputConnection) {
            block
                .getInput("modifier")
                ?.connection?.connect(value.outputConnection);
        }
        (block as Blockly.BlockSvg).initSvg();
        return block;
    };
    Blockly.Blocks["delayby"] = {
        init: function () {
            this.appendValueInput("value").appendField("delay by sixteenth");
            this.setPreviousStatement(true, null);
            this.setNextStatement(true, null);
            this.setOutput(false, null);
            this.setColour(CATEGORY_COLOR.TIME);
            this.setTooltip("");
            this.setHelpUrl("");
        },
    };
    javascriptGenerator.forBlock["delayby"] = function (
        block: Blockly.Block,
        generator: JavascriptGenerator,
    ) {
        const value = generator.statementToCode(block, "value");
        return `{"instruction": "DelayBy", "instructionMod": ${value}}`;
    };
    blockinstanciators.DelayBy = (
        workspace: Blockly.Workspace,
        thisInstruction: Instruction,
    ) => {
        if (thisInstruction.instruction !== "DelayBy") return;
        let block = workspace.newBlock("delayby");
        let value = getInstanciatedValueBlock(
            "delay by sixteenth",
            workspace,
            thisInstruction.instructionMod,
        );
        if (value?.outputConnection) {
            block
                .getInput("value")
                ?.connection?.connect(value.outputConnection);
        }
        (block as Blockly.BlockSvg).initSvg();
        return block;
    };
    Blockly.Blocks["unimplementedchannelto"] = {
        init: function () {
            this.appendValueInput("channelvalue")
                .appendField("set channel")
                .appendField(
                    new Blockly.FieldDropdown(() =>
                        allUnimplementedChannelsAsNameStrings(
                            couldBeSelectedFixtures(this),
                        ).map((channelName) => [channelName, channelName]),
                    ),
                    "channelname",
                )
                .appendField("to");
            this.setPreviousStatement(true, null);
            this.setNextStatement(true, null);
            this.setOutput(false, null);
            this.setColour(CATEGORY_COLOR.COMMANDS);
            this.setOnChange(() => {
                if (shouldCommandblockDisconnect(this)) {
                    this.unplug(true);
                }
                const field: Blockly.Field<Blockly.FieldDropdown> | null =
                    this.getField("channelname");
                if (!field) return;
                // @ts-ignore -> We know that this is a Dropdown, so this must work
                const options = field.getOptions();
                if (
                    options.length !== 1 &&
                    this.getFieldValue("channelname") === "-" &&
                    this.customData.channelname !== "-"
                ) {
                    this.setFieldValue(
                        this.customData.channelname,
                        "channelname",
                    );
                }
                this.customData.channelname = "-";
            });
        },
        dropdownsFilled: function () {
            return this.getFieldValue("channelname") !== "-";
        },
        customData: {
            channelname: "-",
        },
    };
    javascriptGenerator.forBlock["unimplementedchannelto"] = function (
        block: Blockly.Block,
        generator: JavascriptGenerator,
    ) {
        const value = generator.statementToCode(block, "channelvalue");
        return `{"instruction": "UnimplementedChannelTo", "instructionMod": {
                        "value": ${value},
                        "name": "${block.getFieldValue("channelname")}"
                    }
                }`;
    };
    blockinstanciators.UnimplementedChannelTo = (
        workspace: Blockly.Workspace,
        thisInstruction: Instruction,
    ) => {
        if (thisInstruction.instruction !== "UnimplementedChannelTo") return;
        let block = workspace.newBlock("unimplementedchannelto");

        if (!block) return;

        // @ts-ignore -> We know, that we have customData
        block.customData.channelname = thisInstruction.instructionMod.name;

        setTimeout(() => {
            block.setFieldValue(
                thisInstruction.instructionMod.name,
                "channelname",
            );
        }, 10);

        let value = getInstanciatedValueBlock(
            "set channel to",
            workspace,
            thisInstruction.instructionMod.value,
        );
        if (value?.outputConnection) {
            block
                .getInput("channelvalue")
                ?.connection?.connect(value.outputConnection);
        }
        (block as Blockly.BlockSvg).initSvg();
        return block;
    };
    Blockly.Blocks["positionto"] = {
        init: function () {
            this.appendDummyInput("positionsetter")
                .appendField("set Pan/Tilt position to")
                .appendField(
                    new Blockly.FieldDropdown(
                        $positions.length
                            ? $positions.map((position) => [
                                  position.name,
                                  position.id.toString(),
                              ])
                            : [["-", "0"]],
                    ),
                    "position",
                );
            this.setPreviousStatement(true, null);
            this.setNextStatement(true, null);
            this.setColour(CATEGORY_COLOR.COMMANDS);
            this.setOnChange(() => {
                if (shouldCommandblockDisconnect(this)) {
                    this.unplug(true);
                }
            });
        },
        dropdownsFilled: function () {
            return this.getFieldValue("position") !== "0";
        },
    };
    javascriptGenerator.forBlock["positionto"] = (block: Blockly.Block) => {
        return `{"instruction": "PositionTo", "instructionMod": ${block.getFieldValue("position")}}`;
    };
    blockinstanciators.PositionTo = (
        workspace: Blockly.Workspace,
        thisInstruction: Instruction,
    ) => {
        if (thisInstruction.instruction !== "PositionTo") return;
        let block = workspace.newBlock("positionto");
        block.setFieldValue(
            thisInstruction.instructionMod.toString(),
            "position",
        );
        (block as Blockly.BlockSvg).initSvg();
        return block;
    };
    Blockly.Blocks["ifstatement"] = {
        init: function () {
            this.appendDummyInput("if").appendField("if");
            this.appendValueInput("left_value");
            this.appendDummyInput().appendField(
                new Blockly.FieldDropdown([
                    ["==", "Equal"],
                    ["!=", "NotEqual"],
                    [">", "Greater"],
                    [">=", "GreaterOrEqual"],
                    ["<", "Less"],
                    ["<=", "LessOrEqual"],
                ]),
                "comparator",
            );
            this.appendValueInput("right_value");
            this.appendStatementInput("truthy_branch").appendField("do");
            this.appendStatementInput("falsy_branch").appendField("else");
            this.setPreviousStatement(true, null);
            this.setNextStatement(true, null);
            this.setInputsInline(true, null);
            this.setColour(CATEGORY_COLOR.CONTROLFLOW);
            this.setTooltip("");
            this.setHelpUrl("");
        },
        forEachChildBlock: function (
            predicate: (block: Blockly.Block) => void,
        ) {
            {
                let block =
                    this.getInput("truthy_branch").connection.targetBlock();
                while (block) {
                    predicate(block);
                    if (block.forEachChildBlock)
                        block.forEachChildBlock(predicate);
                    block = block.nextConnection.targetBlock();
                }
            }
            {
                let block =
                    this.getInput("falsy_branch").connection.targetBlock();
                while (block) {
                    predicate(block);
                    if (block.forEachChildBlock)
                        block.forEachChildBlock(predicate);
                    block = block.nextConnection.targetBlock();
                }
            }
        },
    };
    javascriptGenerator.forBlock["ifstatement"] = (
        block: Blockly.Block,
        generator: JavascriptGenerator,
    ) => {
        const left_value = generator.statementToCode(block, "left_value");
        const right_value = generator.statementToCode(block, "right_value");
        return `{"instruction": "IfStatement",
                    "instructionMod": {
                        "comparison": {
                            "left_value": ${left_value},
                            "right_value": ${right_value},
                            "comparator": "${block.getFieldValue("comparator")}"
                        },
                        "truthy_branch": [${generator.statementToCode(block, "truthy_branch")}],
                        "falsy_branch": [${generator.statementToCode(block, "falsy_branch")}]
                    }
                }`;
    };
    blockinstanciators.IfStatement = (
        workspace: Blockly.Workspace,
        thisInstruction: Instruction,
    ) => {
        if (thisInstruction.instruction !== "IfStatement") return;

        let block = workspace.newBlock("ifstatement");

        thisInstruction.instructionMod.truthy_branch.forEach((instruction) => {
            let childBlock = blockinstanciators[instruction.instruction](
                workspace,
                instruction,
            );
            if (childBlock?.previousConnection) {
                getLastConnectionOfConnection(
                    block.getInput("truthy_branch")?.connection,
                )?.connect(childBlock.previousConnection);
            }
        });

        thisInstruction.instructionMod.falsy_branch.forEach((instruction) => {
            let childBlock = blockinstanciators[instruction.instruction](
                workspace,
                instruction,
            );
            if (childBlock?.previousConnection) {
                getLastConnectionOfConnection(
                    block.getInput("falsy_branch")?.connection,
                )?.connect(childBlock.previousConnection);
            }
        });
        block.setFieldValue(
            thisInstruction.instructionMod.comparison.comparator,
            "comparator",
        );
        let left_value = getInstanciatedValueBlock(
            "left side of if",
            workspace,
            thisInstruction.instructionMod.comparison.left_value,
        );
        if (left_value && left_value.outputConnection) {
            block
                .getInput("left_value")
                ?.connection?.connect(left_value.outputConnection);
        }
        let right_value = getInstanciatedValueBlock(
            "right side of if",
            workspace,
            thisInstruction.instructionMod.comparison.right_value,
        );
        if (right_value && right_value.outputConnection) {
            block
                .getInput("right_value")
                ?.connection?.connect(right_value.outputConnection);
        }

        (block as Blockly.BlockSvg).initSvg();

        return block;
    };
    Blockly.Blocks["loopstatement"] = {
        init: function () {
            this.appendDummyInput("desc").appendField("as long as");
            this.appendValueInput("left_value");
            this.appendDummyInput().appendField(
                new Blockly.FieldDropdown([
                    ["==", "Equal"],
                    ["!=", "NotEqual"],
                    [">", "Greater"],
                    [">=", "GreaterOrEqual"],
                    ["<", "Less"],
                    ["<=", "LessOrEqual"],
                ]),
                "comparator",
            );
            this.appendValueInput("right_value");
            this.appendStatementInput("content").appendField("do");
            this.setPreviousStatement(true, null);
            this.setNextStatement(true, null);
            this.setInputsInline(true, null);
            this.setColour(CATEGORY_COLOR.CONTROLFLOW);
            this.setTooltip("");
            this.setHelpUrl("");
            this.setOnChange(() => {
                if (!isBuildable) return false;
                this.forEachChildBlock((block: Blockly.Block) => {
                    if (
                        block.type === "delaybyeights" ||
                        block.type === "loopstatement" ||
                        (block.type === "blueprintto" &&
                            block.getFieldValue("delay"))
                    ) {
                        block.unplug(true);
                    }
                });
            });
        },
        forEachChildBlock: function (
            predicate: (block: Blockly.Block) => void,
        ) {
            let block = this.getInput("content").connection.targetBlock();
            while (block) {
                predicate(block);
                if (block.forEachChildBlock) block.forEachChildBlock(predicate);
                block = block.nextConnection.targetBlock();
            }
        },
    };
    javascriptGenerator.forBlock["loopstatement"] = (
        block: Blockly.Block,
        generator: JavascriptGenerator,
    ) => {
        const left_value = generator.statementToCode(block, "left_value");
        const right_value = generator.statementToCode(block, "right_value");
        return `{"instruction": "LoopStatement",
                    "instructionMod": {
                        "comparison": {
                            "left_value": ${left_value},
                            "right_value": ${right_value},
                            "comparator": "${block.getFieldValue("comparator")}"
                        },
                        "content": [${generator.statementToCode(block, "content")}]
                    }
                }`;
    };
    blockinstanciators.LoopStatement = (
        workspace: Blockly.Workspace,
        thisInstruction: Instruction,
    ) => {
        if (thisInstruction.instruction !== "LoopStatement") return;

        let block = workspace.newBlock("loopstatement");

        thisInstruction.instructionMod.content.forEach((instruction) => {
            let childBlock = blockinstanciators[instruction.instruction](
                workspace,
                instruction,
            );
            if (childBlock?.previousConnection) {
                getLastConnectionOfConnection(
                    block.getInput("content")?.connection,
                )?.connect(childBlock.previousConnection);
            }
        });

        block.setFieldValue(
            thisInstruction.instructionMod.comparison.comparator,
            "comparator",
        );
        let left_value = getInstanciatedValueBlock(
            "left side of loop",
            workspace,
            thisInstruction.instructionMod.comparison.left_value,
        );
        if (left_value && left_value.outputConnection) {
            block
                .getInput("left_value")
                ?.connection?.connect(left_value.outputConnection);
        }
        let right_value = getInstanciatedValueBlock(
            "right side of loop",
            workspace,
            thisInstruction.instructionMod.comparison.right_value,
        );
        if (right_value && right_value.outputConnection) {
            block
                .getInput("right_value")
                ?.connection?.connect(right_value.outputConnection);
        }

        (block as Blockly.BlockSvg).initSvg();

        return block;
    };
    Blockly.Blocks["fixtureloop"] = {
        init: function () {
            this.appendStatementInput("fixtures").appendField(
                "For fixtures in",
            );
            this.appendStatementInput("statements").appendField("do");
            this.setPreviousStatement(true, null);
            this.setNextStatement(true, null);
            this.setOutput(false, null);
            this.setColour(CATEGORY_COLOR.CONTROLFLOW);
            this.setTooltip(
                "The first input should contain a list of fixtures.\nThis can include individual fixtures, lists of fixtures or dynamic statemenst like function calls.\nThe Second input contains anything, that should be done to those fixtures, like setting channels...",
            );
            this.setHelpUrl("");
            this.setOnChange(() => {
                if (!isBuildable) return false;
                this.forEachChildBlock((block: Blockly.Block) => {
                    if (
                        (block.getColour() !==
                            CATEGORY_COLOR.FIXTURES.toLowerCase() &&
                            block.getColour() !==
                                CATEGORY_COLOR.CONTROLFLOW.toLowerCase()) ||
                        block.type === "fixtureloop"
                    ) {
                        block.unplug(true);
                    }
                }, "fixtures");

                this.forEachChildBlock((block: Blockly.Block) => {
                    if (
                        (block.getColour() !==
                            CATEGORY_COLOR.COMMANDS.toLowerCase() &&
                            block.getColour() !==
                                CATEGORY_COLOR.CONTROLFLOW.toLowerCase() &&
                            block.getColour() !==
                                CATEGORY_COLOR.TIME.toLowerCase()) ||
                        block.type === "fixtureloop"
                    ) {
                        block.unplug(true);
                    }
                }, "statements");
            });
        },
        forEachChildBlock: function (
            predicate: (block: Blockly.Block) => void,
            input?: string,
        ) {
            {
                if (input && input === "fixtures") {
                    let block =
                        this.getInput("fixtures").connection.targetBlock();
                    while (block) {
                        predicate(block);
                        if (block.forEachChildBlock)
                            block.forEachChildBlock(predicate);
                        block = block.nextConnection.targetBlock();
                    }
                }
            }
            {
                if (input && input === "statements") {
                    let block =
                        this.getInput("statements").connection.targetBlock();
                    while (block) {
                        predicate(block);
                        if (block.forEachChildBlock)
                            block.forEachChildBlock(predicate);
                        block = block.nextConnection.targetBlock();
                    }
                }
            }
        },
    };
    javascriptGenerator.forBlock["fixtureloop"] = function (
        block: Blockly.Block,
        generator: JavascriptGenerator,
    ) {
        return `{"instruction": "FixtureLoop",
        "instructionMod": {
            "fixtures": [${generator.statementToCode(block, "fixtures")}],
            "instructions": [${generator.statementToCode(block, "statements")}]
        }}`;
    };
    blockinstanciators.FixtureLoop = (
        workspace: Blockly.Workspace,
        thisInstruction: Instruction,
    ) => {
        if (thisInstruction.instruction !== "FixtureLoop") return;

        let block = workspace.newBlock("fixtureloop");

        thisInstruction.instructionMod.fixtures.forEach((instruction) => {
            let childBlock = blockinstanciators[instruction.instruction](
                workspace,
                instruction,
            );
            if (childBlock?.previousConnection) {
                getLastConnectionOfConnection(
                    block.getInput("fixtures")?.connection,
                )?.connect(childBlock.previousConnection);
            }
        });
        thisInstruction.instructionMod.instructions.forEach((instruction) => {
            let childBlock = blockinstanciators[instruction.instruction](
                workspace,
                instruction,
            );
            if (childBlock?.previousConnection) {
                getLastConnectionOfConnection(
                    block.getInput("statements")?.connection,
                )?.connect(childBlock.previousConnection);
            }
        });

        (block as Blockly.BlockSvg).initSvg();

        return block;
    };
    Blockly.Blocks["executecallablesnippet"] = {
        init: function () {
            this.appendDummyInput()
                .appendField("Execute snippet")
                .appendField(
                    new Blockly.FieldDropdown(
                        allSnippetsAsBlocklyArray(allCallableSnippets()),
                    ),
                    "snippetId",
                );
            this.setPreviousStatement(true, null);
            this.setNextStatement(true, null);
            this.setOutput(false, null);
            this.setColour(CATEGORY_COLOR.CONTROLFLOW);
        },
        dropdownsFilled: function () {
            return this.getFieldValue("snippetId") !== "-";
        },
    };
    javascriptGenerator.forBlock["executecallablesnippet"] = function (
        block: Blockly.Block,
    ) {
        return `{"instruction": "ExecuteCallableSnippet", "instructionMod":
                    ${block.getFieldValue("snippetId")}
                }`;
    };
    blockinstanciators.ExecuteCallableSnippet = (
        workspace: Blockly.Workspace,
        thisInstruction: Instruction,
    ) => {
        if (thisInstruction.instruction !== "ExecuteCallableSnippet") return;
        let block = workspace.newBlock("executecallablesnippet");
        block.setFieldValue(
            thisInstruction.instructionMod.toString(),
            "snippetId",
        );
        (block as Blockly.BlockSvg).initSvg();
        return block;
    };
    Blockly.Blocks["addtogroup"] = {
        init: function () {
            this.appendStatementInput("fixtures")
                .appendField("Add to fixturegroup")
                .appendField(
                    new Blockly.FieldDropdown(
                        uniqEditableGroupNames().map((groupName) => [
                            groupName,
                            groupName,
                        ]),
                    ),
                    "groupname",
                );
            this.setPreviousStatement(true, null);
            this.setNextStatement(true, null);
            this.setOutput(false, null);
            this.setColour(CATEGORY_COLOR.FIXTURES);
            this.setOnChange(() => {
                if (!isBuildable) return false;
                this.forEachChildBlock((block: Blockly.Block) => {
                    if (
                        block.type !== "activateactionfixtureid" &&
                        block.type !== "activateactionfixturegroup" &&
                        block.type !== "activateactionallfixtures" &&
                        block.type !== "ifstatement"
                    ) {
                        block.unplug(true);
                    }
                }, "fixtures");
            });
        },
        dropdownsFilled: function () {
            return this.getFieldValue("groupname") !== "-";
        },
        forEachChildBlock: function (
            predicate: (block: Blockly.Block) => void,
        ) {
            let block = this.getInput("fixtures").connection.targetBlock();
            while (block) {
                predicate(block);
                if (block.forEachChildBlock) block.forEachChildBlock(predicate);
                block = block.nextConnection.targetBlock();
            }
        },
    };
    javascriptGenerator.forBlock["addtogroup"] = function (
        block: Blockly.Block,
        generator: JavascriptGenerator,
    ) {
        return `{"instruction": "AddToGroup", "instructionMod": {
                        "name": "${block.getFieldValue("groupname")}",
                        "fixtures": [${generator.statementToCode(block, "fixtures")}]
                    }
                }`;
    };
    blockinstanciators.AddToGroup = (
        workspace: Blockly.Workspace,
        thisInstruction: Instruction,
    ) => {
        if (thisInstruction.instruction !== "AddToGroup") return;
        let block = workspace.newBlock("addtogroup");
        block.setFieldValue(
            thisInstruction.instructionMod.name.toString(),
            "groupname",
        );
        thisInstruction.instructionMod.fixtures.forEach((instruction) => {
            let childBlock = blockinstanciators[instruction.instruction](
                workspace,
                instruction,
            );
            if (childBlock?.previousConnection) {
                getLastConnectionOfConnection(
                    block.getInput("fixtures")?.connection,
                )?.connect(childBlock.previousConnection);
            }
        });

        (block as Blockly.BlockSvg).initSvg();
        return block;
    };
    Blockly.Blocks["removefromgroup"] = {
        init: function () {
            this.appendStatementInput("fixtures")
                .appendField("Remove from fixturegroup")
                .appendField(
                    new Blockly.FieldDropdown(
                        uniqEditableGroupNames().map((groupName) => [
                            groupName,
                            groupName,
                        ]),
                    ),
                    "groupname",
                );
            this.setPreviousStatement(true, null);
            this.setNextStatement(true, null);
            this.setOutput(false, null);
            this.setColour(CATEGORY_COLOR.FIXTURES);
            this.setOnChange(() => {
                if (!isBuildable) return false;
                this.forEachChildBlock((block: Blockly.Block) => {
                    if (
                        block.type !== "activateactionfixturename" &&
                        block.type !== "activateactionfixturegroup" &&
                        block.type !== "activateactionallfixtures" &&
                        block.type !== "ifstatement"
                    ) {
                        block.unplug(true);
                    }
                }, "fixtures");
            });
        },
        dropdownsFilled: function () {
            return this.getFieldValue("groupname") !== "-";
        },
        forEachChildBlock: function (
            predicate: (block: Blockly.Block) => void,
        ) {
            let block = this.getInput("fixtures").connection.targetBlock();
            while (block) {
                predicate(block);
                if (block.forEachChildBlock) block.forEachChildBlock(predicate);
                block = block.nextConnection.targetBlock();
            }
        },
    };
    javascriptGenerator.forBlock["removefromgroup"] = function (
        block: Blockly.Block,
        generator: JavascriptGenerator,
    ) {
        return `{"instruction": "RemoveFromGroup", "instructionMod": {
                        "name": "${block.getFieldValue("groupname")}",
                        "fixtures": [${generator.statementToCode(block, "fixtures")}]
                    }
                }`;
    };
    blockinstanciators.RemoveFromGroup = (
        workspace: Blockly.Workspace,
        thisInstruction: Instruction,
    ) => {
        if (thisInstruction.instruction !== "RemoveFromGroup") return;
        let block = workspace.newBlock("removefromgroup");
        block.setFieldValue(
            thisInstruction.instructionMod.name.toString(),
            "groupname",
        );
        thisInstruction.instructionMod.fixtures.forEach((instruction) => {
            let childBlock = blockinstanciators[instruction.instruction](
                workspace,
                instruction,
            );
            if (childBlock?.previousConnection) {
                getLastConnectionOfConnection(
                    block.getInput("fixtures")?.connection,
                )?.connect(childBlock.previousConnection);
            }
        });

        (block as Blockly.BlockSvg).initSvg();
        return block;
    };
    // TODO: rename to something that makes clear, that this appends to the queue
    Blockly.Blocks["ifqueueallowscontinue"] = {
        init: function () {
            this.appendStatementInput("content").appendField(
                "Hold back when queue is active",
            );
            this.setPreviousStatement(true, null);
            this.setNextStatement(true, null);
            this.setOutput(false, null);
            this.setColour(CATEGORY_COLOR.CONTROLFLOW);
            this.setTooltip("");
            this.setHelpUrl("");
            this.setHelpUrl(
                "If activated, everything in the `Hold back when queue is active` block will wailt, until the block `toggle queue mode` is called.\nThis of course works accross all Snippets, so you could hold back all actions and flood them at once. This is usefull e.g. on a drop.",
            );
        },
        forEachChildBlock: function (
            predicate: (block: Blockly.Block) => void,
        ) {
            let block = this.getInput("content").connection.targetBlock();
            while (block) {
                predicate(block);
                if (block.forEachChildBlock) block.forEachChildBlock(predicate);
                block = block.nextConnection.targetBlock();
            }
        },
    };
    javascriptGenerator.forBlock["ifqueueallowscontinue"] = function (
        block: Blockly.Block,
        generator: JavascriptGenerator,
    ) {
        return `{"instruction": "IfQueueAllowsContinue", "instructionMod":
                [${generator.statementToCode(block, "content")}]
        }`;
    };
    blockinstanciators.IfQueueAllowsContinue = (
        workspace: Blockly.Workspace,
        thisInstruction: Instruction,
    ) => {
        if (thisInstruction.instruction !== "IfQueueAllowsContinue") return;
        let block = workspace.newBlock("ifqueueallowscontinue");

        thisInstruction.instructionMod.forEach((instruction) => {
            let childBlock = blockinstanciators[instruction.instruction](
                workspace,
                instruction,
            );
            if (childBlock?.previousConnection) {
                getLastConnectionOfConnection(
                    block.getInput("content")?.connection,
                )?.connect(childBlock.previousConnection);
            }
        });

        (block as Blockly.BlockSvg).initSvg();

        return block;
    };
    Blockly.Blocks["inputchanged"] = {
        init: function () {
            this.appendStatementInput("content").appendField("Input changed");
            this.setColour(CATEGORY_COLOR.TRIGGER);
            this.setTooltip(
                'This block will run all code that you fill it with.\nThis blocks text is directly tied to the overall category of this snippet and purely visual. So if the dropdown says "Startup" but this block says "Input changed" (or vice versa), the dropdown is correct.',
            );
            this.setHelpUrl("");
        },
    };
    javascriptGenerator.forBlock["inputchanged"] = function (
        block: Blockly.Block,
        generator: JavascriptGenerator,
    ) {
        return `[${generator.statementToCode(block, "content")}]`;
    };
    blockinstanciators.InputChanged = (
        workspace: Blockly.Workspace,
        childs: Instruction[],
    ) => {
        let block = workspace.newBlock("inputchanged");

        childs.forEach((instruction) => {
            let childBlock = blockinstanciators[instruction.instruction](
                workspace,
                instruction,
            );
            if (childBlock?.previousConnection) {
                getLastConnectionOfConnection(
                    block.getInput("content")?.connection,
                )?.connect(childBlock.previousConnection);
            }
        });

        block.setDeletable(false);

        (block as Blockly.BlockSvg).initSvg();

        return block;
    };
    Blockly.Blocks["startup"] = {
        init: function () {
            this.appendStatementInput("content").appendField("On startup");
            this.setColour(CATEGORY_COLOR.TRIGGER);
            this.setTooltip(
                'This block will run all code that you fill it with.\nThis blocks text is directly tied to the overall category of this snippet and purely visual. So if the dropdown says "Startup" but this block says "Input changed" (or vice versa), the dropdown is correct.\nThis block will run with the `input` block always set to 0',
            );
        },
    };
    javascriptGenerator.forBlock["startup"] = function (
        block: Blockly.Block,
        generator: JavascriptGenerator,
    ) {
        return `[${generator.statementToCode(block, "content")}]`;
    };
    blockinstanciators.Startup = (
        workspace: Blockly.Workspace,
        childs: Instruction[],
    ) => {
        let block = workspace.newBlock("startup");

        childs.forEach((instruction) => {
            let childBlock = blockinstanciators[instruction.instruction](
                workspace,
                instruction,
            );
            if (childBlock?.previousConnection) {
                getLastConnectionOfConnection(
                    block.getInput("content")?.connection,
                )?.connect(childBlock.previousConnection);
            }
        });

        block.setDeletable(false);

        (block as Blockly.BlockSvg).initSvg();

        return block;
    };
    Blockly.Blocks["watcher"] = {
        init: function () {
            this.appendStatementInput("content").appendField("On each frame");
            this.setColour(CATEGORY_COLOR.TRIGGER);
            this.setTooltip(
                "This block will run all code that you fill it with on each dmx frame.\nThis block will run with the `input` block always set to 0",
            );
        },
    };
    javascriptGenerator.forBlock["watcher"] = function (
        block: Blockly.Block,
        generator: JavascriptGenerator,
    ) {
        return `[${generator.statementToCode(block, "content")}]`;
    };
    blockinstanciators.Watcher = (
        workspace: Blockly.Workspace,
        childs: Instruction[],
    ) => {
        let block = workspace.newBlock("watcher");

        childs.forEach((instruction) => {
            let childBlock = blockinstanciators[instruction.instruction](
                workspace,
                instruction,
            );
            if (childBlock?.previousConnection) {
                getLastConnectionOfConnection(
                    block.getInput("content")?.connection,
                )?.connect(childBlock.previousConnection);
            }
        });

        block.setDeletable(false);

        (block as Blockly.BlockSvg).initSvg();

        return block;
    };
    Blockly.Blocks["callable"] = {
        init: function () {
            this.appendStatementInput("content").appendField("Callable");
            this.setColour(CATEGORY_COLOR.TRIGGER);
            this.setTooltip(
                "This block will run when you call the snippet from another snippet or timecode.\nThis block will run with the `input` block always set to 0",
            );
        },
    };
    javascriptGenerator.forBlock["callable"] = function (
        block: Blockly.Block,
        generator: JavascriptGenerator,
    ) {
        return `[${generator.statementToCode(block, "content")}]`;
    };
    blockinstanciators.Callable = (
        workspace: Blockly.Workspace,
        childs: Instruction[],
    ) => {
        let block = workspace.newBlock("callable");

        childs.forEach((instruction) => {
            let childBlock = blockinstanciators[instruction.instruction](
                workspace,
                instruction,
            );
            if (childBlock?.previousConnection) {
                getLastConnectionOfConnection(
                    block.getInput("content")?.connection,
                )?.connect(childBlock.previousConnection);
            }
        });

        block.setDeletable(false);

        (block as Blockly.BlockSvg).initSvg();

        return block;
    };
    Blockly.Blocks["createvariable"] = {
        init: function () {
            this.appendDummyInput()
                .appendField("Create variable")
                .appendField(new Blockly.FieldTextInput(), "name");
            this.setColour(CATEGORY_COLOR.VARIABLES);
            this.setPreviousStatement(true, null);
            this.setNextStatement(true, null);
        },
    };
    javascriptGenerator.forBlock["createvariable"] = function (
        block: Blockly.Block,
    ) {
        return `{"instruction": "CreateVariable", "instructionMod": "${block.getFieldValue("name")}"}`;
    };
    blockinstanciators.CreateVariable = (
        workspace: Blockly.Workspace,
        thisInstruction: Instruction,
    ) => {
        if (thisInstruction.instruction !== "CreateVariable") return;
        let block = workspace.newBlock("createvariable");
        block.setFieldValue(thisInstruction.instructionMod, "name");
        (block as Blockly.BlockSvg).initSvg();
        return block;
    };
    Blockly.Blocks["setvariable"] = {
        init: function () {
            this.appendValueInput("value")
                .appendField("Set variable")
                .appendField(
                    new Blockly.FieldDropdown(
                        uniqueVariableNames().map((varName) => [
                            varName,
                            varName,
                        ]),
                    ),
                    "name",
                )
                .appendField("to");
            this.setColour(CATEGORY_COLOR.VARIABLES);
            this.setPreviousStatement(true, null);
            this.setNextStatement(true, null);
        },
        dropdownsFilled: function () {
            return this.getFieldValue("name") !== "-";
        },
    };
    javascriptGenerator.forBlock["setvariable"] = function (
        block: Blockly.Block,
        generator: JavascriptGenerator,
    ) {
        const value = generator.statementToCode(block, "value");
        return `{"instruction": "SetVariable", "instructionMod": {
                "name": "${block.getFieldValue("name")}",
                "value": ${value}
            }}`;
    };
    blockinstanciators.SetVariable = (
        workspace: Blockly.Workspace,
        thisInstruction: Instruction,
    ) => {
        if (thisInstruction.instruction !== "SetVariable") return;
        let block = workspace.newBlock("setvariable");
        block.setFieldValue(thisInstruction.instructionMod.name, "name");
        let value = getInstanciatedValueBlock(
            "Set variable",
            workspace,
            thisInstruction.instructionMod.value,
        );
        if (value && value.outputConnection) {
            block
                .getInput("value")
                ?.connection?.connect(value.outputConnection);
        }
        (block as Blockly.BlockSvg).initSvg();
        return block;
    };
    Blockly.Blocks["getvariable"] = {
        init: function () {
            this.appendDummyInput()
                .appendField("Get variable")
                .appendField(
                    new Blockly.FieldDropdown(
                        uniqueVariableNames().map((variableName) => [
                            variableName,
                            variableName,
                        ]),
                    ),
                    "name",
                );
            this.setColour(CATEGORY_COLOR.VARIABLES);
            this.setOutput(true, "InstructionValue");
        },
        dropdownsFilled: function () {
            return this.getFieldValue("name") !== "-";
        },
    };
    javascriptGenerator.forBlock["getvariable"] = function (
        block: Blockly.Block,
    ) {
        return `{"Variable": "${block.getFieldValue("name")}"}`;
    };
    function shouldCommandblockDisconnect(
        block: Blockly.Block | null,
    ): boolean {
        if (!isBuildable) return false;

        let parent = block?.getSurroundParent();
        while (parent) {
            if (
                (parent.getInput("statements") &&
                    parent.type === "fixtureloop") ||
                parent.type === "blueprintto"
            ) {
                return false;
            }
            parent = parent?.getSurroundParent();
        }
        return true;
    }
    function shouldRecordingBlockDisconnect(block: Blockly.Block) {
        if (!isBuildable) return false;

        let parent = block?.getSurroundParent();
        while (parent) {
            if (
                parent.type !== "inputchanged" &&
                parent.type !== "startup" &&
                parent.type !== "watcher" &&
                parent.type !== "callable" &&
                parent.type !== "ifstatement"
            ) {
                return true;
            }
            parent = parent?.getSurroundParent();
        }
        return false;
    }
    Blockly.Blocks["colorto"] = {
        init: function () {
            this.appendDummyInput()
                .appendField("set color to")
                .appendField(new FieldColourHsvSliders("#0000FF"), "color");
            this.appendDummyInput("fade")
                .appendField("fade")
                .appendField(new Blockly.FieldCheckbox(), "fade");
            this.appendValueInput("fade_duration");
            this.setPreviousStatement(true, null);
            this.setNextStatement(true, null);
            this.setOutput(false, null);
            this.setColour(CATEGORY_COLOR.COMMANDS);
            this.setOnChange(() => {
                if (shouldCommandblockDisconnect(this)) {
                    this.unplug(true);
                }
                if (
                    this.getFieldValue("fade") === "TRUE" &&
                    this.getInput("fade_duration") === null
                ) {
                    this.appendValueInput("fade_duration").appendField(
                        "in beats",
                    );
                } else if (
                    this.getFieldValue("fade") === "FALSE" &&
                    this.getInput("fade_duration")
                ) {
                    this.removeInput("fade_duration");
                }
            });
        },
    };
    javascriptGenerator.forBlock["colorto"] = function (
        block: Blockly.Block,
        generator: JavascriptGenerator,
    ) {
        const color = block.getFieldValue("color");
        const fade = block.getFieldValue("fade") === "TRUE";
        const parsed_color = {
            red: parseInt(color.substring(1, 3), 16),
            green: parseInt(color.substring(3, 5), 16),
            blue: parseInt(color.substring(5, 7), 16),
        };

        if (fade) {
            const fadeDuration = generator.statementToCode(
                block,
                "fade_duration",
            );
            return `{"instruction": "ColorTo",
                        "instructionMod": {
                            "color": {
                                "red": ${parsed_color.red},
                                "green": ${parsed_color.green},
                                "blue": ${parsed_color.blue}
                            },
                            "fade_duration": ${fadeDuration}
                        }
                    }`;
        } else {
            return `{"instruction": "ColorTo",
                        "instructionMod": {
                            "color": {
                                "red": ${parsed_color.red},
                                "green": ${parsed_color.green},
                                "blue": ${parsed_color.blue}
                            },
                            "fade_duration": null
                        }
                    }`;
        }
    };
    blockinstanciators.ColorTo = (
        workspace: Blockly.Workspace,
        thisInstruction: Instruction,
    ) => {
        if (thisInstruction.instruction !== "ColorTo") return;
        let block = workspace.newBlock("colorto");
        let hex_rgb_color_code = "#";
        let red = thisInstruction.instructionMod.color.red.toString(16);
        if (parseInt(red, 16) < 10) {
            red = "0" + red;
        }
        let green = thisInstruction.instructionMod.color.green.toString(16);
        if (parseInt(green, 16) < 10) {
            green = "0" + green;
        }
        let blue = thisInstruction.instructionMod.color.blue.toString(16);
        if (parseInt(blue, 16) < 10) {
            blue = "0" + blue;
        }
        hex_rgb_color_code += red + green + blue;
        block.setFieldValue(hex_rgb_color_code, "color");

        block.setFieldValue(
            thisInstruction.instructionMod.fade_duration ? "TRUE" : "FALSE",
            "fade",
        );

        if (thisInstruction.instructionMod.fade_duration) {
            let fadeDuration = getInstanciatedValueBlock(
                "fade to color in beats",
                workspace,
                thisInstruction.instructionMod.fade_duration,
            );
            if (fadeDuration?.outputConnection) {
                block
                    .getInput("fade_duration")
                    ?.connection?.connect(fadeDuration.outputConnection);
            }
        }

        (block as Blockly.BlockSvg).initSvg();
        return block;
    };
    Blockly.Blocks["colortorandom"] = {
        init: function () {
            this.appendDummyInput().appendField("set color to random");
            this.setPreviousStatement(true, null);
            this.setNextStatement(true, null);
            this.setOutput(false, null);
            this.setColour(CATEGORY_COLOR.COMMANDS);
            this.appendDummyInput("fade")
                .appendField("fade")
                .appendField(new Blockly.FieldCheckbox(), "fade");
            this.appendValueInput("fade_duration");
            this.setOnChange(() => {
                if (shouldCommandblockDisconnect(this)) {
                    this.unplug(true);
                }
                if (
                    this.getFieldValue("fade") === "TRUE" &&
                    this.getInput("fade_duration") === null
                ) {
                    this.appendValueInput("fade_duration").appendField(
                        "in beats",
                    );
                } else if (
                    this.getFieldValue("fade") === "FALSE" &&
                    this.getInput("fade_duration")
                ) {
                    this.removeInput("fade_duration");
                }
            });
        },
    };
    javascriptGenerator.forBlock["colortorandom"] = function (
        block: Blockly.Block,
        generator: JavascriptGenerator,
    ) {
        const fade = block.getFieldValue("fade") === "TRUE";

        if (fade) {
            const fadeDuration = generator.statementToCode(
                block,
                "fade_duration",
            );
            return `{"instruction": "ColorToRandom",
                        "instructionMod": ${fadeDuration}
                    }`;
        } else {
            return `{"instruction": "ColorToRandom",
                        "instructionMod": null
                    }`;
        }
    };
    blockinstanciators.ColorToRandom = (
        workspace: Blockly.Workspace,
        thisInstruction: Instruction,
    ) => {
        if (thisInstruction.instruction !== "ColorToRandom") return;

        let block = workspace.newBlock("colortorandom");

        block.setFieldValue(
            thisInstruction.instructionMod ? "TRUE" : "FALSE",
            "fade",
        );

        if (thisInstruction.instructionMod) {
            let fadeDuration = getInstanciatedValueBlock(
                "fade to random color in beats",
                workspace,
                thisInstruction.instructionMod,
            );
            if (fadeDuration?.outputConnection) {
                block
                    .getInput("fade_duration")
                    ?.connection?.connect(fadeDuration.outputConnection);
            }
        }
        (block as Blockly.BlockSvg).initSvg();
        return block;
    };
    Blockly.Blocks["blueprintto"] = {
        init: function () {
            this.appendDummyInput("dummy")
                .appendField("set blueprint to")
                .appendField(
                    new Blockly.FieldDropdown([
                        ...$blueprints.map(
                            (blueprint) =>
                                [blueprint.name, blueprint.id.toString()] as [
                                    string,
                                    string,
                                ],
                        ),
                        ["-", "0"],
                    ]),
                    "blueprint",
                );
            this.appendDummyInput("dummy_2")
                .appendField("run only once")
                .appendField(new Blockly.FieldCheckbox(), "oneshot");
            this.appendDummyInput("dummy_3")
                .appendField("delay until blueprint finished")
                .appendField(new Blockly.FieldCheckbox(), "delay");
            this.setPreviousStatement(true, null);
            this.setNextStatement(true, null);
            this.setOutput(false, null);
            this.setColour(CATEGORY_COLOR.COMMANDS);
            this.setOnChange(() => {
                if (!isBuildable) return;
                if (
                    this.getFieldValue("oneshot") === "TRUE" &&
                    this.getInput("dummy_3") === null
                ) {
                    this.appendDummyInput("dummy_3")
                        .appendField("delay until blueprint finished")
                        .appendField(new Blockly.FieldCheckbox(), "delay");
                } else if (
                    this.getFieldValue("oneshot") === "FALSE" &&
                    this.getInput("dummy_3")
                ) {
                    this.removeInput("dummy_3");
                }
                if (shouldCommandblockDisconnect(this)) {
                    this.unplug(true);
                }
            });
        },
        dropdownsFilled: function () {
            return this.getFieldValue("blueprint") !== "-";
        },
    };
    javascriptGenerator.forBlock["blueprintto"] = function (
        block: Blockly.Block,
    ) {
        return `{
                "instruction": "BlueprintTo",
                "instructionMod": {
                    "id": ${block.getFieldValue("blueprint")},
                    "oneshot": ${block.getFieldValue("oneshot")},
                    "delay": ${block.getFieldValue("delay") ?? false}
                }
            }`;
    };
    blockinstanciators.BlueprintTo = (
        workspace: Blockly.Workspace,
        thisInstruction: Instruction,
    ) => {
        if (thisInstruction.instruction !== "BlueprintTo") return;
        let block = workspace.newBlock("blueprintto");
        block.setFieldValue(
            thisInstruction.instructionMod.id.toString(),
            "blueprint",
        );
        block.setFieldValue(thisInstruction.instructionMod.oneshot, "oneshot");
        block.setFieldValue(thisInstruction.instructionMod.delay, "delay");
        (block as Blockly.BlockSvg).initSvg();
        return block;
    };
    Blockly.Blocks["setblueprintpositionindexoffsetmode"] = {
        init: function () {
            this.appendDummyInput("dummy")
                .appendField("set blueprint shuffle mode")
                .appendField(
                    new Blockly.FieldDropdown([
                        ["Matching", "Matching"],
                        ["CustomOffset", "CustomOffset"],
                        ["Random", "Random"],
                    ]),
                    "mode",
                );
            this.setPreviousStatement(true, null);
            this.setNextStatement(true, null);
            this.setOutput(false, null);
            this.setColour(CATEGORY_COLOR.COMMANDS);
            this.setTooltip(
                "Either the fixtures move along with theyr animations together, or every fixture moves, with its own `offset` on the animation.\nThis can look pretty random, but executed correctly, this can give you a unique effect!",
            );
            this.setOnChange(() => {
                if (shouldCommandblockDisconnect(this)) {
                    this.unplug(true);
                }
            });
        },
    };
    javascriptGenerator.forBlock["setblueprintpositionindexoffsetmode"] =
        function (block: Blockly.Block) {
            return `{"instruction": "SetBlueprintPositionIndexOffsetMode", "instructionMod": "${block.getFieldValue(
                "mode",
            )}"}`;
        };
    blockinstanciators.SetBlueprintPositionIndexOffsetMode = (
        workspace: Blockly.Workspace,
        thisInstruction: Instruction,
    ) => {
        if (
            thisInstruction.instruction !==
            "SetBlueprintPositionIndexOffsetMode"
        )
            return;
        let block = workspace.newBlock("setblueprintpositionindexoffsetmode");
        block.setFieldValue(thisInstruction.instructionMod, "mode");
        (block as Blockly.BlockSvg).initSvg();
        return block;
    };
    Blockly.Blocks["setspeedofblueprints"] = {
        init: function () {
            this.appendValueInput("speed").appendField(
                "set speed of blueprints to",
            );
            this.setPreviousStatement(true, null);
            this.setNextStatement(true, null);
            this.setOutput(false, null);
            this.setColour(CATEGORY_COLOR.COMMANDS);
            this.setTooltip(
                "Sets the speed at which fixtures process their blueprints. 0 = paused, 100 = normal speed, >100 = faster than normal.",
            );
            this.setOnChange(() => {
                if (shouldCommandblockDisconnect(this)) {
                    this.unplug(true);
                }
            });
        },
    };
    javascriptGenerator.forBlock["setspeedofblueprints"] = function (
        block: Blockly.Block,
        generator: JavascriptGenerator,
    ) {
        const speed = generator.statementToCode(block, "speed");
        return `{"instruction": "SetSpeedOfBlueprints", "instructionMod": ${speed}}`;
    };
    blockinstanciators.SetSpeedOfBlueprints = (
        workspace: Blockly.Workspace,
        thisInstruction: Instruction,
    ) => {
        if (thisInstruction.instruction !== "SetSpeedOfBlueprints") return;
        let block = workspace.newBlock("setspeedofblueprints");
        let value = getInstanciatedValueBlock(
            "set speed of blueprints to",
            workspace,
            thisInstruction.instructionMod,
        );
        if (value?.outputConnection) {
            block
                .getInput("speed")
                ?.connection?.connect(value.outputConnection);
        }
        (block as Blockly.BlockSvg).initSvg();
        return block;
    };
    Blockly.Blocks["togglequeuemode"] = {
        init: function () {
            this.appendDummyInput("dummy")
                .appendField("set queue mode to")
                .appendField(
                    new Blockly.FieldDropdown([
                        ["HoldBack", "Queue"],
                        ["Flush", "Flush"],
                    ]),
                    "queuemode",
                );
            this.setPreviousStatement(true, null);
            this.setNextStatement(true, null);
            this.setOutput(false, null);
            this.setColour(CATEGORY_COLOR.CONTROLFLOW);
            this.setTooltip("");
            this.setHelpUrl(
                "If activated, everything in the `Hold back when queue is active` block will wailt, until this is called again.\nThis of course works accross all Snippets, so you could hold back all actions and flood them at once. This is usefull e.g. on a drop.",
            );
        },
    };
    javascriptGenerator.forBlock["togglequeuemode"] = function (
        block: Blockly.Block,
    ) {
        return `{"instruction": "ToggleQueueMode", "instructionMod": "${block.getFieldValue("queuemode")}"}`;
    };
    blockinstanciators.ToggleQueueMode = (
        workspace: Blockly.Workspace,
        thisInstruction: Instruction,
    ) => {
        if (thisInstruction.instruction !== "ToggleQueueMode") return;
        let block = workspace.newBlock("togglequeuemode");
        block.setFieldValue(thisInstruction.instructionMod, "queuemode");
        (block as Blockly.BlockSvg).initSvg();
        return block;
    };
    Blockly.Blocks["panto"] = {
        init: function () {
            this.appendValueInput("pan").appendField("set pan to");
            this.setPreviousStatement(true, null);
            this.setNextStatement(true, null);
            this.setOutput(false, null);
            this.setColour(CATEGORY_COLOR.COMMANDS);
            this.setOnChange(() => {
                if (shouldCommandblockDisconnect(this)) {
                    this.unplug(true);
                }
            });
        },
    };
    javascriptGenerator.forBlock["panto"] = function (
        block: Blockly.Block,
        generator: JavascriptGenerator,
    ) {
        const channelValue = generator.statementToCode(block, "pan");
        return `{"instruction": "PanTo", "instructionMod": ${channelValue}}`;
    };
    blockinstanciators.PanTo = (
        workspace: Blockly.Workspace,
        thisInstruction: Instruction,
    ) => {
        if (thisInstruction.instruction !== "PanTo") return;
        let block = workspace.newBlock("panto");
        let value = getInstanciatedValueBlock(
            "Set pan to",
            workspace,
            thisInstruction.instructionMod,
        );
        if (value && value.outputConnection) {
            block.getInput("pan")?.connection?.connect(value.outputConnection);
        }
        (block as Blockly.BlockSvg).initSvg();
        return block;
    };
    Blockly.Blocks["tiltto"] = {
        init: function () {
            this.appendValueInput("tilt").appendField("set tilt to");
            this.setPreviousStatement(true, null);
            this.setNextStatement(true, null);
            this.setOutput(false, null);
            this.setColour(CATEGORY_COLOR.COMMANDS);
            this.setOnChange(() => {
                if (shouldCommandblockDisconnect(this)) {
                    this.unplug(true);
                }
            });
        },
    };
    javascriptGenerator.forBlock["tiltto"] = function (
        block: Blockly.Block,
        generator: JavascriptGenerator,
    ) {
        const channelValue = generator.statementToCode(block, "tilt");
        return `{"instruction": "TiltTo", "instructionMod": ${channelValue}}`;
    };
    blockinstanciators.TiltTo = (
        workspace: Blockly.Workspace,
        thisInstruction: Instruction,
    ) => {
        if (thisInstruction.instruction !== "TiltTo") return;
        let block = workspace.newBlock("tiltto");
        let value = getInstanciatedValueBlock(
            "Set tilt to",
            workspace,
            thisInstruction.instructionMod,
        );
        if (value && value.outputConnection) {
            block.getInput("tilt")?.connection?.connect(value.outputConnection);
        }
        (block as Blockly.BlockSvg).initSvg();
        return block;
    };
    Blockly.Blocks["dimmerto"] = {
        init: function () {
            this.appendValueInput("value").appendField("set master-dimmer to");
            this.setPreviousStatement(true, null);
            this.setNextStatement(true, null);
            this.setOutput(false, null);
            this.setColour(CATEGORY_COLOR.CONTROLFLOW);
            this.setOnChange(() => {
                if (shouldCommandblockDisconnect(this)) {
                    this.unplug(true);
                }
            });
            this.setHelpUrl(
                "Sets the master-dimmer of all dimmer-enabled-fixturechannels. This does not set the 'Dimmer' channel of a fixture, but sort-of limits its brightness",
            );
        },
    };
    javascriptGenerator.forBlock["dimmerto"] = function (
        block: Blockly.Block,
        generator: JavascriptGenerator,
    ) {
        const channelValue = generator.statementToCode(block, "value");
        return `{"instruction": "DimmerTo", "instructionMod": ${channelValue} }`;
    };
    blockinstanciators.DimmerTo = (
        workspace: Blockly.Workspace,
        thisInstruction: Instruction,
    ) => {
        if (thisInstruction.instruction !== "DimmerTo") return;
        let block = workspace.newBlock("dimmerto");
        let value = getInstanciatedValueBlock(
            "Set dimmer to",
            workspace,
            thisInstruction.instructionMod,
        );
        if (value && value.outputConnection) {
            block
                .getInput("value")
                ?.connection?.connect(value.outputConnection);
        }
        (block as Blockly.BlockSvg).initSvg();
        return block;
    };
    Blockly.Blocks["clearblueprint"] = {
        init: function () {
            this.appendDummyInput("dummy")
                .appendField("clear blueprint")
                .appendField(
                    new Blockly.FieldDropdown([
                        ...$blueprints.map(
                            (blueprint) =>
                                [blueprint.name, blueprint.id.toString()] as [
                                    string,
                                    string,
                                ],
                        ),
                        ["-", "0"],
                    ]),
                    "blueprint",
                );
            this.setPreviousStatement(true, null);
            this.setNextStatement(true, null);
            this.setOutput(false, null);
            this.setColour(CATEGORY_COLOR.COMMANDS);
            this.setTooltip(
                "Clear the specified blueprint on the selected fixtures",
            );
            this.setOnChange(() => {
                if (shouldCommandblockDisconnect(this)) {
                    this.unplug(true);
                }
            });
        },
        dropdownsFilled: function () {
            return this.getFieldValue("blueprint") !== "0";
        },
    };
    javascriptGenerator.forBlock["clearblueprint"] = function (
        block: Blockly.Block,
    ) {
        return `{"instruction": "ClearBlueprint", "instructionMod": ${block.getFieldValue("blueprint")}}`;
    };
    blockinstanciators.ClearBlueprint = (
        workspace: Blockly.Workspace,
        thisInstruction: Instruction,
    ) => {
        if (thisInstruction.instruction !== "ClearBlueprint") return;
        let block = workspace.newBlock("clearblueprint");
        block.setFieldValue(
            thisInstruction.instructionMod.toString(),
            "blueprint",
        );
        (block as Blockly.BlockSvg).initSvg();
        return block;
    };

    Blockly.Blocks["startrecording"] = {
        init: function () {
            this.appendDummyInput("dummy").appendField("start recording");
            this.setPreviousStatement(true, null);
            this.setNextStatement(true, null);
            this.setOutput(false, null);
            this.setColour(CATEGORY_COLOR.CONTROLFLOW);
            this.setTooltip(
                "Warning: The 'record...' blocks record KEYPRESSES, not other Blocks. Use a blueprint, when you want to automate a channel",
            );
            this.setOnChange(() => {
                if (shouldRecordingBlockDisconnect(this)) {
                    this.unplug(true);
                }
            });
        },
    };
    javascriptGenerator.forBlock["startrecording"] = function () {
        return `{"instruction": "StartRecording"}`;
    };
    blockinstanciators.StartRecording = (workspace: Blockly.Workspace) => {
        let block = workspace.newBlock("startrecording");
        (block as Blockly.BlockSvg).initSvg();
        return block;
    };

    Blockly.Blocks["stoprecording"] = {
        init: function () {
            this.appendDummyInput("dummy").appendField("stop recording");
            this.setPreviousStatement(true, null);
            this.setNextStatement(true, null);
            this.setOutput(false, null);
            this.setColour(CATEGORY_COLOR.CONTROLFLOW);
            this.setTooltip(
                "Warning: The 'record...' blocks record KEYPRESSES, not other Blocks. Use a blueprint, when you want to automate a channel",
            );
            this.setOnChange(() => {
                if (shouldRecordingBlockDisconnect(this)) {
                    this.unplug(true);
                }
            });
        },
    };
    javascriptGenerator.forBlock["stoprecording"] = function () {
        return `{"instruction": "StopRecording"}`;
    };
    blockinstanciators.StopRecording = (workspace: Blockly.Workspace) => {
        let block = workspace.newBlock("stoprecording");
        (block as Blockly.BlockSvg).initSvg();
        return block;
    };

    Blockly.Blocks["clearrecording"] = {
        init: function () {
            this.appendDummyInput("dummy").appendField("clear recording");
            this.setPreviousStatement(true, null);
            this.setNextStatement(true, null);
            this.setOutput(false, null);
            this.setColour(CATEGORY_COLOR.CONTROLFLOW);
            this.setTooltip(
                "Warning: The 'record...' blocks record KEYPRESSES, not other Blocks. Use a blueprint, when you want to automate a channel",
            );
            this.setOnChange(() => {
                if (shouldRecordingBlockDisconnect(this)) {
                    this.unplug(true);
                }
            });
        },
    };
    javascriptGenerator.forBlock["clearrecording"] = function () {
        return `{"instruction": "ClearRecording"}`;
    };
    blockinstanciators.ClearRecording = (workspace: Blockly.Workspace) => {
        let block = workspace.newBlock("clearrecording");
        (block as Blockly.BlockSvg).initSvg();
        return block;
    };

    Blockly.Blocks["getfixturefunction"] = {
        init: function () {
            this.appendDummyInput("dummy")
                .appendField("fixture function")
                .appendField(
                    new Blockly.FieldDropdown(() => [
                        ...allUnimplementedChannelsAsNameStrings(
                            $fixtures.filter(
                                (fixture) =>
                                    fixture.id?.toString() ===
                                    this.getFieldValue("fixture_id"),
                            ),
                        ).map(
                            (channelName) =>
                                [channelName, channelName] as [string, string],
                        ),
                        ["Pan", "pan"],
                        ["Tilt", "tilt"],
                        ["Red", "red"],
                        ["Green", "green"],
                        ["Blue", "blue"],
                    ]),
                    "function",
                )
                .appendField("of fixture")
                .appendField(
                    new Blockly.FieldDropdown(() =>
                        uniqFixtureNames().map((fixture) => [
                            fixture[1],
                            fixture[0].toString(),
                        ]),
                    ),
                    "fixture_id",
                );
            this.setOutput(true, "number");
            this.setColour(CATEGORY_COLOR.VALUES);
            this.setTooltip("The value of the selected fixture`s function`.");

            this.setOnChange((e: Blockly.Events.Click) => {
                if (e.type === "move") {
                    const fieldValue = this.getFieldValue("fixture_id");

                    const dropdownOptions =
                        this.getField("fixture_id").getOptions();

                    if (dropdownOptions.length === 1) {
                        this.setFieldValue("0", "fixture_id");
                    } else if (
                        fieldValue !== "0" &&
                        this.customData.shaddowFixtureId >= 0
                    ) {
                    } else if (
                        fieldValue === "0" &&
                        this.customData.shaddowFixtureId >= 0
                    ) {
                        this.setFieldValue(
                            this.customData.shaddowFixtureId.toString(),
                            "fixture_id",
                        );
                    }
                    if (
                        fieldValue !== "0" &&
                        this.customData.shaddowFixtureId === -1
                    ) {
                        this.customData.shaddowFixtureId =
                            fieldValue.parseInt();
                    }
                }
                if (e.type === "move") {
                    const fieldValue = this.getFieldValue("function");

                    const dropdownOptions =
                        this.getField("function").getOptions();

                    if (dropdownOptions.length === 1) {
                        this.setFieldValue("-", "function");
                    } else if (
                        fieldValue !== "-" &&
                        this.customData.shaddowFunction !== "-"
                    ) {
                    } else if (
                        fieldValue === "-" &&
                        this.customData.shaddowFunction !== "-"
                    ) {
                        this.setFieldValue(
                            this.customData.shaddowFunction,
                            "function",
                        );
                    }
                    if (
                        fieldValue !== "-" &&
                        this.customData.shaddowFunction === "-"
                    ) {
                        this.customData.shaddowFunction = fieldValue;
                    }
                }
            });
        },
        dropdownsFilled: function () {
            return (
                this.getFieldValue("function") !== "-" &&
                this.getFieldValue("fixture_id") !== "-"
            );
        },
        customData: { shaddowFunction: "-", shaddowFixtureId: -1 },
    };
    javascriptGenerator.forBlock["getfixturefunction"] = function (
        block: Blockly.Block,
    ) {
        const fixture_id = block.getFieldValue("fixture_id");
        const func = block.getFieldValue("function");
        return `{"Function": { "fixture_id": ${fixture_id}, "function": "${func}"}}`;
    };
    Blockly.Blocks["keypoint"] = {
        init: function () {
            this.appendDummyInput("dummy")
                .appendField("keypoint")
                .appendField(
                    new Blockly.FieldDropdown(() =>
                        keypointsOfChannelAsBlocklyArray(
                            this.getParent() &&
                                this.getParent().type ===
                                    "unimplementedchannelto"
                                ? this.getParent().getFieldValue("channelname")
                                : "",
                            couldBeSelectedFixtures(this),
                        ),
                    ),
                    "keypoint",
                );
            this.setOutput(true, "number");
            this.setColour(CATEGORY_COLOR.VALUES);
            this.setTooltip("The keypoint that is configured for this channel");
            this.setOnChange((e: Blockly.Events.Click) => {
                if (e.type === "move") {
                    const fieldValue = this.getFieldValue("keypoint");

                    const dropdownOptions =
                        this.getField("keypoint").getOptions();

                    if (dropdownOptions.length === 1) {
                        this.setFieldValue("-", "keypoint");
                    } else if (
                        fieldValue !== "-" &&
                        this.customData.shaddowKeypoint >= 0
                    ) {
                    } else if (
                        fieldValue === "-" &&
                        this.customData.shaddowKeypoint >= 0
                    ) {
                        this.setFieldValue(
                            this.customData.shaddowKeypoint.toString(),
                            "keypoint",
                        );
                    } else if (fieldValue !== "-" && this.customData === -1) {
                        this.customData.shaddowKeypoint =
                            this.getFieldValue("keypoint").parseInt();
                    }
                }
            });
        },
        dropdownsFilled: function () {
            return this.getFieldValue("keypoint") !== "-";
        },
        customData: { shaddowKeypoint: -1 },
    };
    javascriptGenerator.forBlock["keypoint"] = function (block: Blockly.Block) {
        return `{"Keypoint": ${block.getFieldValue("keypoint")} }`;
    };
    Blockly.Blocks["bpm"] = {
        init: function () {
            this.appendDummyInput("dummy").appendField("bpm");
            this.setOutput(true, "number");
            this.setColour(CATEGORY_COLOR.VALUES);
            this.setTooltip();
        },
    };
    javascriptGenerator.forBlock["bpm"] = function () {
        return `"Bpm"`;
    };
    Blockly.Blocks["bpmmodifier"] = {
        init: function () {
            this.appendDummyInput("dummy").appendField("bpm modifier");
            this.setOutput(true, "number");
            this.setColour(CATEGORY_COLOR.VALUES);
            this.setTooltip();
        },
    };
    javascriptGenerator.forBlock["bpmmodifier"] = function () {
        return `"BpmModifier"`;
    };
    Blockly.Blocks["valueconst"] = {
        init: function () {
            this.appendDummyInput("dummy").appendField("input");
            this.setOutput(true, "number");
            this.setColour(CATEGORY_COLOR.VALUES);
            this.setTooltip(
                "The value for the button/fader that triggered this code. A button could be compared with the blocks `Pressed` or `Released`, any fader will have a value from `0 to 100`.",
            );
        },
    };
    javascriptGenerator.forBlock["valueconst"] = function () {
        return `"Value"`;
    };
    Blockly.Blocks["random"] = {
        init: function () {
            this.appendValueInput("from").appendField("random from");
            this.appendValueInput("to").appendField("to");
            this.setInputsInline(true, null);
            this.setOutput(true, "number");
            this.setColour(CATEGORY_COLOR.VALUES);
            this.setTooltip(
                "A randomly generated value. This random value is generated each time the block is encountered. Even on multiple runns from the same snippet.",
            );
        },
    };
    javascriptGenerator.forBlock["random"] = function (
        block: Blockly.Block,
        generator: JavascriptGenerator,
    ) {
        return `{"Random": [${generator.statementToCode(block, "from")}, ${generator.statementToCode(block, "to")}]}`;
    };
    blockinstanciators.PanTo = (
        workspace: Blockly.Workspace,
        thisInstruction: Instruction,
    ) => {
        if (thisInstruction.instruction !== "PanTo") return;
        let block = workspace.newBlock("panto");
        let value = getInstanciatedValueBlock(
            "Set pan to",
            workspace,
            thisInstruction.instructionMod,
        );
        if (value && value.outputConnection) {
            block.getInput("pan")?.connection?.connect(value.outputConnection);
        }
        (block as Blockly.BlockSvg).initSvg();
        return block;
    };
    Blockly.Blocks["valueinput"] = {
        init: function () {
            this.appendDummyInput("dummy").appendField(
                new FieldSlider(0, 0, 100),
                "value",
            );
            this.appendDummyInput("dmx_precision_input")
                .appendField("DMX Precision")
                .appendField(new Blockly.FieldCheckbox(false), "dmx_precision");
            this.setOutput(true, "InstructionValue");
            this.setColour(CATEGORY_COLOR.VALUES);
            this.setTooltip(
                "When dmx_precision is enabled, values range from 0 to 255. When disabled, values range from 0 to 100.",
            );
            this.setOnChange(() => {
                const outputConnection = this.outputConnection;
                let parentBlock = null;
                if (outputConnection && outputConnection.targetConnection) {
                    parentBlock =
                        outputConnection.targetConnection.getSourceBlock();
                }

                const parentBlockType = parentBlock ? parentBlock.type : null;
                const allowedBlocks = [
                    "setvariable",
                    "mathoperator",
                    "ifstatement",
                    "loopstatement",
                    "unimplementedchannelto",
                    "panto",
                    "tiltto",
                    "random",
                ];

                const shouldShowPrecision =
                    allowedBlocks.includes(parentBlockType);
                const precisionInput = this.getInput("dmx_precision_input");

                if (shouldShowPrecision) {
                    if (precisionInput && !precisionInput.isVisible()) {
                        precisionInput.setVisible(true);
                        this.render();
                    }
                } else {
                    if (precisionInput && precisionInput.isVisible()) {
                        precisionInput.setVisible(false);
                        this.setFieldValue("FALSE", "dmx_precision");
                        this.render();
                    }
                }

                const isDmxPrecision =
                    this.getFieldValue("dmx_precision") === "TRUE";
                const currentValue = this.getFieldValue("value");
                const valueField = this.getField("value") as any;

                if (isDmxPrecision) {
                    if (valueField && valueField.max_ !== 255) {
                        if (valueField.setConstraints) {
                            valueField.setConstraints(0, 255);
                        }
                        const clampedValue = Math.max(
                            0,
                            Math.min(255, currentValue),
                        );
                        this.setFieldValue(clampedValue, "value");
                    }
                } else {
                    if (valueField && valueField.max_ !== 100) {
                        if (valueField.setConstraints) {
                            valueField.setConstraints(0, 100);
                        }
                        const clampedValue = Math.max(
                            0,
                            Math.min(100, currentValue),
                        );
                        this.setFieldValue(clampedValue, "value");
                    }
                }
            });
        },
    };
    javascriptGenerator.forBlock["valueinput"] = function (
        block: Blockly.Block,
    ) {
        const value = block.getFieldValue("value");
        const isDmxPrecision = block.getFieldValue("dmx_precision") === "TRUE";

        return `{"Number":{"value":${value},"dmx_precision":${isDmxPrecision}}}`;
    };
    Blockly.Blocks["pressedconst"] = {
        init: function () {
            this.appendDummyInput("dummy").appendField("pressed");
            this.setOutput(true, "InstructionValue");
            this.setColour(CATEGORY_COLOR.VALUES);
            this.setTooltip("The button, that triggered this code was pressed");
        },
    };
    javascriptGenerator.forBlock["pressedconst"] = () => {
        return `"Pressed"`;
    };
    Blockly.Blocks["releasedconst"] = {
        init: function () {
            this.appendDummyInput("dummy").appendField("released");
            this.setOutput(true, "InstructionValue");
            this.setColour(CATEGORY_COLOR.VALUES);
            this.setTooltip(
                "The button, that triggered this code was released",
            );
        },
    };
    javascriptGenerator.forBlock["releasedconst"] = () => {
        return `"Released"`;
    };
    Blockly.Blocks["defaultconst"] = {
        init: function () {
            this.appendDummyInput("dummy").appendField("default");
            this.setOutput(true, "InstructionValue");
            this.setColour(CATEGORY_COLOR.VALUES);
            this.setTooltip(
                "This contains the default value for the channel it is connected to, a bpm of 120, ",
            );
        },
    };
    javascriptGenerator.forBlock["defaultconst"] = () => {
        return `"Default"`;
    };
    Blockly.Blocks["mathoperator"] = {
        init: function () {
            this.appendValueInput("left_value");
            this.appendDummyInput().appendField(
                new Blockly.FieldDropdown([
                    ["+", "Addition"],
                    ["-", "Subtraction"],
                    ["*", "Multiplication"],
                    ["/", "Division"],
                    ["%", "Modulus"],
                ]),
                "operand",
            );
            this.appendValueInput("right_value");
            this.setInputsInline(true, null);
            this.setOutput(true, null);
            this.setColour(CATEGORY_COLOR.VALUES);
            this.setTooltip("");
            this.setHelpUrl("");
        },
    };
    javascriptGenerator.forBlock["mathoperator"] = (
        block: Blockly.Block,
        generator: JavascriptGenerator,
    ) => {
        const left_value = generator.statementToCode(block, "left_value");
        const right_value = generator.statementToCode(block, "right_value");
        return `{"MathOperator": {
                        "left_value": ${left_value},
                        "right_value": ${right_value},
                        "operand": "${block.getFieldValue("operand")}"
                    }}`;
    };

    // TODO: more power to the blocks!
    function getInstanciatedValueBlock(
        name: string,
        workspace: Blockly.Workspace,
        instructionValue: InstructionValue,
    ): Blockly.Block | undefined {
        let valueInput: Blockly.Block | null = null;
        if (instructionValue.hasOwnProperty("Number")) {
            valueInput = workspace.newBlock("valueinput");

            // @ts-ignore - TypeScript can't properly infer the union type structure for instructionValue.Number
            const numberValue = instructionValue.Number.value;
            // @ts-ignore - TypeScript can't properly infer the union type structure for instructionValue.Number
            const dmxPrecision = instructionValue.Number.dmx_precision;

            valueInput.setFieldValue(
                dmxPrecision ? "TRUE" : "FALSE",
                "dmx_precision",
            );
            const valueField = valueInput.getField("value") as any;

            if (dmxPrecision) {
                if (valueField && valueField.setConstraints) {
                    valueField.setConstraints(0, 255);
                }
            } else {
                if (valueField && valueField.setConstraints) {
                    valueField.setConstraints(0, 100);
                }
            }
            valueInput.setFieldValue(numberValue, "value");
        } else if (instructionValue === "Value") {
            valueInput = workspace.newBlock("valueconst");
        } else if (instructionValue.hasOwnProperty("Keypoint")) {
            valueInput = workspace.newBlock("keypoint");

            // @ts-ignore -> We filtered for `Keypoint` so we are sure, that this works, but `ts` is not
            valueInput.customData = {
                // @ts-ignore -> We filtered for `Keypoint` so we are sure, that this works, but `ts` is not
                shaddowKeypoint: instructionValue.Keypoint,
            };
        } else if (instructionValue.hasOwnProperty("Random")) {
            valueInput = workspace.newBlock("random");

            let fromValue = getInstanciatedValueBlock(
                "from-value for random block",
                workspace,
                // @ts-ignore -> We filtered for `Random` so we are sure, that this works, but `ts` is not
                instructionValue.Random[0],
            );
            if (fromValue && fromValue.outputConnection) {
                valueInput
                    .getInput("from")
                    ?.connection?.connect(fromValue.outputConnection);
            }
            let toValue = getInstanciatedValueBlock(
                "to-value for random block",
                workspace,
                // @ts-ignore -> We filtered for `Random` so we are sure, that this works, but `ts` is not
                instructionValue.Random[1],
            );
            if (toValue && toValue.outputConnection) {
                valueInput
                    .getInput("to")
                    ?.connection?.connect(toValue.outputConnection);
            }
        } else if (instructionValue.hasOwnProperty("MathOperator")) {
            valueInput = workspace.newBlock("mathoperator");

            valueInput.setFieldValue(
                // @ts-ignore -> We filtered for `MathOperator` so we are sure, that this works, but `ts` is not
                instructionValue.MathOperator.operand,
                "operand",
            );
            let left_value = getInstanciatedValueBlock(
                "left side of math operator",
                workspace,
                // @ts-ignore -> We filtered for `MathOperator` so we are sure, that this works, but `ts` is not
                instructionValue.MathOperator.left_value,
            );
            if (left_value && left_value.outputConnection) {
                valueInput
                    .getInput("left_value")
                    ?.connection?.connect(left_value.outputConnection);
            }
            let right_value = getInstanciatedValueBlock(
                "right side of math operator",
                workspace,
                // @ts-ignore -> We filtered for `MathOperator` so we are sure, that this works, but `ts` is not
                instructionValue.MathOperator.right_value,
            );
            if (right_value && right_value.outputConnection) {
                valueInput
                    .getInput("right_value")
                    ?.connection?.connect(right_value.outputConnection);
            }
        } else if (instructionValue === "Bpm") {
            valueInput = workspace.newBlock("bpm");
        } else if (instructionValue === "BpmModifier") {
            valueInput = workspace.newBlock("bpmmodifier");
        } else if (instructionValue === "Pressed") {
            valueInput = workspace.newBlock("pressedconst");
        } else if (instructionValue === "Released") {
            valueInput = workspace.newBlock("releasedconst");
        } else if (instructionValue === "Default") {
            valueInput = workspace.newBlock("defaultconst");
        } else if (instructionValue.hasOwnProperty("Variable")) {
            valueInput = workspace.newBlock("getvariable");
            // @ts-ignore -> We filtered for `Variable` so we are sure, that this works, but `ts` is not
            valueInput.setFieldValue(instructionValue.Variable, "name");
        } else if (instructionValue.hasOwnProperty("Function")) {
            valueInput = workspace.newBlock("getfixturefunction");
            // @ts-ignore -> We filtered for `Keypoint` so we are sure, that this works, but `ts` is not
            valueInput.customData = {
                // @ts-ignore -> We filtered for `Keypoint` so we are sure, that this works, but `ts` is not
                shaddowFunction: instructionValue.Function.function,
                // @ts-ignore -> We filtered for `Keypoint` so we are sure, that this works, but `ts` is not
                shaddowFixtureId: instructionValue.Function.fixture_id,
            };
        }
        if (!valueInput) {
            TOAST.warning(
                `Please review the block "${name}" and make sure to connect a value input to it.`,
            );
            return;
        } else {
            (valueInput as Blockly.BlockSvg).initSvg();
            return valueInput;
        }
    }
    let innerWidth = 0;

    $: {
        innerWidth;
        updateBlocklyWidth();
    }
    onMount(() => {
        updateBlocklyWidth();
    });
    function updateBlocklyWidth() {
        const rootElement = document.getElementById("blocklyDiv");
        if (rootElement) {
            rootElement.style.width = `${innerWidth - rootElement.offsetLeft}px`;
            const resizeEvent = new UIEvent("resize", {
                view: window,
                bubbles: true,
                cancelable: true,
            });

            window.dispatchEvent(resizeEvent);
        } else {
            setTimeout(() => updateBlocklyWidth(), 100);
        }
    }
</script>

<svelte:window bind:innerWidth />

<div class="top-navbar" id="blocklyDiv"></div>
<xml id="toolbox" style="display:none">
    <slot />
</xml>

<style scoped>
    #blocklyDiv {
        height: calc(100% - 4rem);
        position: fixed;
        bottom: 0;
        text-align: left;
    }
</style>
