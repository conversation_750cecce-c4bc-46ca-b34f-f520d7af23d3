#include "Arduino.h"
#include "rw_utils.h"

#define MOTOR_OFF 0
#define MOTOR_SLOW 120
#define MOTOR_FAST 200

#define POTI_MIN 0
#define POTI_MAX 1023

#define FADER_READ_THRESHHOLD 6

#define FADER_ENABLE_MOTOR_DURATION 300

int map_range(int input, int from_0, int from_1, int to_0, int to_1) {
  return to_0 + (input - from_0) * (to_1 - from_0) / (from_1 - from_0);
}

Fader::Fader(int p_poti_pin, int p_motor_r_pin, int p_motor_l_pin) {
  dest = 0;
  motor_enabled = false;
  motor_enabled_since = 0;
  last_seen_position = 0;
  poti_pin = p_poti_pin;
  motor_r_pin = p_motor_r_pin;
  motor_l_pin = p_motor_l_pin;
};
void Fader::set_new_dest(int p_new_dest) {
  dest = map_range(p_new_dest, 0, 255, POTI_MIN, POTI_MAX);
  // dest = p_new_dest;
  motor_enabled = true;
  motor_enabled_since = millis();
}
int Fader::get_value() {
  if (motor_enabled) {
    return map_range(dest, POTI_MIN, POTI_MAX, 0, 255);
  } else {
    return map_range(analogRead(poti_pin), POTI_MIN, POTI_MAX, 0, 255);
  }
}
int Fader::get_dest() { return dest; }
void Fader::tick(void (*on_move_callback)(int, int), int i) {
  int current_position = analogRead(poti_pin);
  int position_dest_diff = abs(current_position - dest);
  if (motor_enabled) {
    if (position_dest_diff < FADER_READ_THRESHHOLD ||
        millis() - motor_enabled_since > FADER_ENABLE_MOTOR_DURATION) {
      motor_enabled = false;
      analogWrite(motor_r_pin, MOTOR_OFF);
      analogWrite(motor_l_pin, MOTOR_OFF);
      return;
    }
    if (current_position < dest) {
      analogWrite(motor_r_pin, map_range(position_dest_diff, POTI_MIN, POTI_MAX,
                                         MOTOR_SLOW, MOTOR_FAST));
      analogWrite(motor_l_pin, MOTOR_OFF);
    } else {
      analogWrite(motor_l_pin, map_range(position_dest_diff, POTI_MIN, POTI_MAX,
                                         MOTOR_SLOW, MOTOR_FAST));
      analogWrite(motor_r_pin, MOTOR_OFF);
    }
  } else if (abs(last_seen_position - current_position) >
             FADER_READ_THRESHHOLD) {
    on_move_callback(i,
                     map_range(current_position, POTI_MIN, POTI_MAX, 0, 255));
    last_seen_position = current_position;
  }
}
