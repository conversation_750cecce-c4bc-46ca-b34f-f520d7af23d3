use mysql::{Pool, PooledConn};
use refinery::Migration;
use show::Show;
use snippet::{Snippet, SnippetCategory};

use crate::logging::{self, Dashboard};

pub mod audiofiles;
pub mod blueprints;
pub mod directory_tree;
pub mod fixturegroups;
pub mod fixtures;
pub mod gdtf;
pub mod library;
pub mod pan_tilt_positions;
pub mod show;
pub mod snippet;
pub mod timecodes;

refinery::embed_migrations!("./src/database_handler/migrations");

#[derive(PartialEq, Eq, Clone)]
pub enum PendingPublishTo {
    FixturesSse,
    DmxRenderer,
    DashboardSse,
}

pub struct DbHandler {
    pending_publish_to: Vec<PendingPublishTo>,
    pub db_pool: Pool,
    startup_snippets_waiting_for_read: bool,
    pub active_show: Show,
}

impl DbHandler {
    #[must_use]
    #[allow(clippy::arithmetic_side_effects)]
    pub fn db_connection(&self) -> PooledConn {
        const MAX_RETRIES: u8 = 3;
        const RETRY_DELAY_MS: u64 = 100;

        for attempt in 1..=MAX_RETRIES {
            match self.db_pool.get_conn() {
                Ok(conn) => return conn,
                Err(e) => {
                    logging::log(
                        format!("Database connection attempt {attempt}/{MAX_RETRIES} failed: {e:?}"),
                        logging::LogLevel::Warning,
                        true,
                    );

                    if attempt < MAX_RETRIES {
                        std::thread::sleep(core::time::Duration::from_millis(
                            RETRY_DELAY_MS * u64::from(attempt),
                        ));
                    }
                }
            }
        }
        logging::log(
            "Unable to get database connection. Aborting".to_string(),
            logging::LogLevel::Warning,
            true,
        );
        std::process::exit(1);
    }

    #[must_use]
    pub fn new() -> Self {
        let db_pool = create_db_pool();

        {
            let mut db_connection = db_pool.get_conn().unwrap_or_else(|e| {
                logging::log(
                    format!("Unable to get connection from pool during setup: {e:?}"),
                    logging::LogLevel::Warning,
                    true,
                );
                std::process::exit(1);
            });

            run_migrations(&mut db_connection);
            logging::log(
                "Database connection established and tested successfully"
                    .to_owned(),
                logging::LogLevel::Info,
                false,
            );
            logging::log_show_metadata(&mut db_connection);
        }

        let active_show = {
            let mut db_connection = db_pool.get_conn().unwrap_or_else(|e| {
                logging::log(
                    format!("Unable to get connection from pool for active show: {e:?}"),
                    logging::LogLevel::Warning,
                    true,
                );
                std::process::exit(1);
            });

            Show {
                fixtures: Self::fixtures_for_active_show(&mut db_connection),
                snippets_dir: Self::snippets_dir_for_active_show(
                    &mut db_connection,
                ),
                blueprints: Self::blueprints_for_active_show(
                    &mut db_connection,
                ),
                timecodes: Self::timecodes_for_active_show(&mut db_connection),
                positions: Self::pan_tilt_positions(&mut db_connection),
                groups: Self::fixturegroups_for_active_show(&mut db_connection),
                name: Self::get_active_show_name(&mut db_connection)
                    .unwrap_or_default(),
            }
        };

        Self {
            pending_publish_to: vec![],
            db_pool,
            startup_snippets_waiting_for_read: true,
            active_show,
        }
    }
    #[must_use]
    pub fn check_and_reset_if_new_state_was_received_for(
        &mut self,
        publish_target: &PendingPublishTo,
    ) -> bool {
        if self.pending_publish_to.contains(publish_target) {
            self.pending_publish_to
                .retain(|target| target != publish_target);
            true
        } else {
            false
        }
    }
    pub fn fill_dashboard(&mut self, dashboard: &mut Dashboard) -> bool {
        let fixtures = &self.active_show.fixtures;
        let old_fixture_count = dashboard.fixture_count;
        let old_universes = dashboard.universes.clone();
        let old_snippets = dashboard.snippets.clone();

        dashboard.fixture_count = fixtures.len();
        let mut universes: Vec<usize> = fixtures
            .iter()
            .map(|fixture| fixture.dmx_universe.into())
            .collect();
        universes.sort_unstable();
        universes.dedup();
        dashboard.universes = universes;

        if let Some(snippets) = &self.active_show.snippets_dir {
            dashboard.snippets = snippets
                .items_recursive()
                .iter()
                .map(|snippet| {
                    (snippet.name.clone(), format!("{:?}", snippet.category))
                })
                .collect();
        }

        old_fixture_count != dashboard.fixture_count
            || old_universes != dashboard.universes
            || old_snippets != dashboard.snippets
    }
    #[must_use]
    pub fn find_snippet_by_id(&mut self, id: usize) -> Option<&Snippet> {
        #[allow(clippy::option_if_let_else)]
        if let Some(directory) = &self.active_show.snippets_dir {
            directory
                .items_recursive()
                .iter()
                .find(|snippet| snippet.id == id)
                .map(|snippet| &**snippet)
        } else {
            None
        }
    }
    #[must_use]
    pub fn find_snippet_by_serial_module_key(
        &mut self,
        key: u16,
    ) -> Option<Snippet> {
        #[allow(clippy::option_if_let_else)]
        if let Some(directory) = &self.active_show.snippets_dir {
            directory
                .items_recursive()
                .iter()
                .find(|snippet| snippet.serial_module_key == Some(key))
                .copied()
                .cloned()
        } else {
            None
        }
    }
    #[must_use]
    pub fn startup_snippets_if_needed(&mut self) -> Option<Vec<Snippet>> {
        if self.startup_snippets_waiting_for_read {
            self.startup_snippets_waiting_for_read = false;
            self.active_show.snippets_dir.as_ref().map(|directory| {
                directory
                    .items_recursive()
                    .iter()
                    .filter(|snippet| {
                        snippet.category == SnippetCategory::Startup
                    })
                    .copied()
                    .cloned()
                    .collect()
            })
        } else {
            None
        }
    }
    pub fn watcher(&mut self) -> Vec<Snippet> {
        #[allow(clippy::option_if_let_else)]
        if let Some(directory) = &self.active_show.snippets_dir {
            directory
                .items_recursive()
                .iter()
                .filter(|snippet| snippet.category == SnippetCategory::Watcher)
                .copied()
                .cloned()
                .collect()
        } else {
            vec![]
        }
    }
    pub const fn request_snippet_state_reset(&mut self) {
        self.startup_snippets_waiting_for_read = true;
    }
    fn publish_changes(&mut self) {
        self.pending_publish_to.push(PendingPublishTo::DmxRenderer);
        self.pending_publish_to.push(PendingPublishTo::FixturesSse);
        self.pending_publish_to.push(PendingPublishTo::DashboardSse);
    }
    pub fn trigger_dashboard_update(&mut self) {
        self.pending_publish_to.push(PendingPublishTo::DashboardSse);
    }
}

impl Default for DbHandler {
    fn default() -> Self {
        Self::new()
    }
}

fn create_db_pool() -> Pool {
    let url = format!(
        "mysql://rw_usr:rw_pass@127.0.0.1:3306/{}",
        if cfg!(debug_assertions) {
            "rw_db_dev"
        } else {
            "rw_db"
        }
    );

    let opts = mysql::OptsBuilder::from_opts(
        mysql::Opts::from_url(&url).unwrap_or_else(|e| {
            logging::log(
                format!("Invalid database URL: {e:?}"),
                logging::LogLevel::Warning,
                true,
            );
            std::process::exit(1);
        }),
    )
    .tcp_connect_timeout(Some(core::time::Duration::from_secs(30)))
    .read_timeout(Some(core::time::Duration::from_secs(30)))
    .write_timeout(Some(core::time::Duration::from_secs(30)));

    Pool::new(opts).unwrap_or_else(|e| {
        logging::log(
            format!("Unable to create connection pool: {e:?}"),
            logging::LogLevel::Warning,
            true,
        );
        std::process::exit(1);
    })
}

fn run_migrations(db_connection: &mut PooledConn) {
    let migrations_runner = migrations::runner();
    let all_migrations = migrations_runner.get_migrations();
    if let Ok(Some(last_applied_migration)) =
        migrations_runner.get_last_applied_migration(db_connection)
    {
        let pending_migrations: Vec<&Migration> = all_migrations
            .iter()
            .filter(|migration| {
                migration.version() > last_applied_migration.version()
            })
            .collect();

        if !pending_migrations.is_empty() {
            let newest_migration = pending_migrations
                .iter()
                .max_by(|a, b| a.version().cmp(&b.version()))
                .copied();

            logging::log(
                    format!(
                        "DB is on version {} ({}). Migrating to version  {} ({})...",
                        last_applied_migration.version(),
                        last_applied_migration.name(),
                        newest_migration.map(Migration::version).unwrap_or_default(),
                        newest_migration.map(Migration::name).unwrap_or_default(),
                    ),
                    logging::LogLevel::Info,
                    false,
                );
        }
    } else {
        logging::log(
                "Unable to obtain DB version. This might be due to it beeing fresh or the db is missing or inaccessible".to_owned(),
                logging::LogLevel::Warning,
                true,
            );
    }

    for done_migration in migrations_runner.run_iter(db_connection) {
        match done_migration {
            Ok(done_migration) => logging::log(
                format!(
                    "Successfully migrated to db-version {} ({})",
                    done_migration.version(),
                    done_migration.name()
                ),
                logging::LogLevel::Info,
                true,
            ),
            Err(err) => {
                logging::log(
                    format!("Error while migrating: {err}",),
                    logging::LogLevel::Warning,
                    true,
                );
                std::process::exit(1)
            }
        }
    }
}
