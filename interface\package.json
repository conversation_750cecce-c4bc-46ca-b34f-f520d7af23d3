{"name": "rw_interface", "version": "0.0.0", "private": true, "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "lint": "prettier --plugin-search-dir . --check . && eslint .", "format": "prettier --plugin-search-dir . --write .", "tauri": "tauri"}, "devDependencies": {"@sveltejs/adapter-auto": "^3.0.0", "@sveltejs/adapter-node": "^5.2.12", "@sveltejs/kit": "^2.0.0", "@sveltejs/vite-plugin-svelte": "^3.0.0", "@tauri-apps/cli": "^2.0.0-rc.18", "@types/eslint": "^8.56.0", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "autoprefixer": "^10.4.18", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-svelte": "^2.36.0-next.4", "filedrop-svelte": "^0.1.2", "postcss": "^8.4.35", "prettier": "^3.2.5", "prettier-plugin-svelte": "^3.1.2", "prettier-plugin-tailwindcss": "^0.5.11", "svelte": "^5.0.0-next.1", "svelte-check": "^3.6.0", "tailwindcss": "^3.4.1", "tailwindcss-themer": "^4.0.0", "tslib": "^2.4.1", "typescript": "^5.0.0", "vite-plugin-version-mark": "^0.1.4", "wavesurfer.js": "7.9.3"}, "type": "module", "dependencies": {"@anselan/maprange": "^0.9.2", "@blockly/field-colour-hsv-sliders": "^5.0.9", "@blockly/field-slider": "^7.0.4", "@blockly/theme-dark": "^7.0.7", "@blockly/workspace-backpack": "^6.0.4", "@sveltejs/adapter-static": "^3.0.5", "@tauri-apps/api": "^2.0.2", "@types/p5": "^1.7.6", "blockly": "11.0.0", "javascript-time-ago": "^2.5.11", "just-clone": "^6.2.0", "p5": "^1.9.2", "svelte-click-outside": "^1.0.0"}}