use super::channel::{
    movement_channels::MovementChannels,
    unimplemented_channels::UnimplementedChannel, DmxChannelEmitter,
};
use super::dynamics::property::SnippetCall;
use super::dynamics::timecode::{
    all_properties_from_tracks, merge_by_type_for_fixture,
    TimecodeFileDescriptor, TimedPropertyFileDescriptor,
};
use rand::random;
use serde::{Deserialize, Serialize};
use ts_rs::TS;

use super::dynamics::blueprint::BlueprintFileDescriptor;
use super::dynamics::IsDynamic;
use crate::database_handler::pan_tilt_positions::{
    ComposedPanTiltPosition, FixturePosition,
};
use crate::database_handler::Db<PERSON>andler;
use crate::dmx_renderer::channel::color_channels::{DmxFixtureColor, RgbColor};
use crate::dmx_renderer::DMX_RENDERER_BEAT_IS_INDEX_POINTS;
use crate::input_parser::structs::InputParser;
use crate::logging::{self};

use core::fmt;

const DEFAULT_BLUEPRINT_SPEED: u8 = 100;

#[derive(Clone, Copy, PartialEq, Eq, Debug, Serialize, Deserialize, TS)]
#[ts(export)]
pub enum PositionIndexOffsetMode {
    Matching,
    CustomOffset,
    Random,
    StageLeftToRight,
    StageRightToLeft,
    StageUpToDown,
    StageDownToUp,
}
impl From<String> for PositionIndexOffsetMode {
    fn from(value: String) -> Self {
        match value.as_str() {
            "CustomOffset" => Self::CustomOffset,
            "Random" => Self::Random,
            "StageLeftToRight" => Self::StageLeftToRight,
            "StageRightToLeft" => Self::StageRightToLeft,
            "StageUpToDown" => Self::StageUpToDown,
            "StageDownToUp" => Self::StageDownToUp,
            _ /* | "Matching" */ => Self::Matching,
        }
    }
}
impl fmt::Display for PositionIndexOffsetMode {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            Self::Matching => write!(f, "Matching"),
            Self::CustomOffset => write!(f, "CustomOffset"),
            Self::Random => write!(f, "Random"),
            Self::StageRightToLeft => {
                write!(f, "StageRightToLeft")
            }
            Self::StageLeftToRight => {
                write!(f, "StageLeftToRight")
            }
            Self::StageUpToDown => {
                write!(f, "StageUpToDown")
            }
            Self::StageDownToUp => {
                write!(f, "StageDownToUp")
            }
        }
    }
}

#[derive(Clone, Serialize, Deserialize, Debug, TS)]
#[ts(export)]
pub struct DmxFixtureInstanceFileDescriptor {
    pub id: usize,
    pub name: String,
    pub dmx_address: Option<usize>,
    pub dmx_universe: u16,
    pub stage_coordinates: (usize, usize),
}
#[derive(Clone, Serialize, Deserialize, Debug, TS)]
#[ts(export)]
// TODO: We take a mutable reference to this in each fixture. We should probably restrict the mutability in some way
pub struct DmxFixtureFileTypeDescriptor {
    pub name: String,
    pub movement_channels: Option<MovementChannels>,
    pub stage_coordinates: (usize, usize),
    pub footprint_size: usize,
    pub unimplemented_channels: Vec<UnimplementedChannel>,
    pub color: Option<DmxFixtureColor>,
}
pub struct DmxFixture<'a> {
    fixture_instance: &'a DmxFixtureInstanceFileDescriptor,
    fixture_type: &'a mut DmxFixtureFileTypeDescriptor,
    position_index_offset_mode: PositionIndexOffsetMode,
    enabled: bool,
    blueprint_delay_eights: f32,
    blueprint_random_delay: f32,
    dimmer_value: u8,
    snippet_calls_of_current_timecode: Vec<SnippetCall>,
    speed_of_blueprints: u8,
    index: f32,
}

impl<'a> DmxFixture<'a> {
    #[must_use]
    pub fn new(
        fixture_instance: &'a DmxFixtureInstanceFileDescriptor,
        fixture_type: &'a mut DmxFixtureFileTypeDescriptor,
    ) -> Self {
        Self {
            fixture_instance,
            fixture_type,
            index: 0.,
            position_index_offset_mode: PositionIndexOffsetMode::Matching,
            enabled: true,
            blueprint_delay_eights: 0.,
            blueprint_random_delay: 0.,
            dimmer_value: u8::MAX,
            snippet_calls_of_current_timecode: vec![],
            speed_of_blueprints: DEFAULT_BLUEPRINT_SPEED,
        }
    }
    #[must_use]
    pub const fn id(&self) -> usize {
        self.fixture_instance.id
    }
    #[must_use]
    pub const fn movement_channels(&self) -> &Option<MovementChannels> {
        &self.fixture_type.movement_channels
    }
    pub fn merge_file_descriptor_with_state(
        &mut self,
        // file_descriptor: &DmxFixtureFileDescriptor,
    ) {
        todo!("merge_file_descriptor_with_state")
        // let mut mutable_file_descriptor = file_descriptor.clone();
        // self.name.clone_from(&file_descriptor.name);
        // self.color = file_descriptor.color.clone();
        // self.dmx_address = file_descriptor.dmx_address;
        // self.dmx_universe = file_descriptor.dmx_universe;
        // self.fixturetype.clone_from(&file_descriptor.fixturetype);
        // self.stage_coordinates = file_descriptor.stage_coordinates;
        // self.footprint_size = file_descriptor.footprint_size;
        // self.movement_channels
        //     .clone_from(&file_descriptor.movement_channels);
        // self.unimplemented_channels =
        //     UnimplementedChannel::from_unimplemented_channel_file_descriptors(
        //         mutable_file_descriptor
        //             .unimplemented_channels
        //             .iter_mut()
        //             .collect(),
        //     );
    }
    #[must_use]
    #[allow(clippy::missing_const_for_fn)]
    pub fn get_name(&self) -> &str {
        &self.fixture_instance.name
    }
    #[must_use]
    pub const fn get_type(&self) -> &String {
        &self.fixture_type.name
    }
    pub const fn set_pan_tilt_position(&mut self, position: &FixturePosition) {
        if let Some(movement_channels) =
            self.fixture_type.movement_channels.as_mut()
        {
            movement_channels.set_pan(position.pan());
            movement_channels.set_tilt(position.tilt());
        }
    }
    pub const fn set_position_index_offset_mode(
        &mut self,
        position_index_offset_mode: PositionIndexOffsetMode,
    ) {
        self.position_index_offset_mode = position_index_offset_mode;
    }
    pub fn apply_blueprint(
        &mut self,
        blueprint: &BlueprintFileDescriptor,
        oneshot: bool,
        all_positions: &[ComposedPanTiltPosition],
    ) {
        self.snippet_calls_of_current_timecode = vec![];

        let self_id = self.id();

        if let Some(movement_channels) =
            self.fixture_type.movement_channels.as_mut()
        {
            movement_channels.extract_internal_spline_from_positions_in(
                &blueprint.properties,
                all_positions,
                self_id,
                Some(blueprint.id),
                oneshot,
            );
            movement_channels.extract_internal_spline_from(
                &blueprint.properties,
                oneshot,
                Some(blueprint.id),
            );
        }

        if let Some(ref mut color) = self.fixture_type.color {
            color.extract_internal_spline_from(
                &blueprint.properties,
                oneshot,
                Some(blueprint.id),
            );
        }

        for unimplemented_channel in
            &mut self.fixture_type.unimplemented_channels
        {
            unimplemented_channel.extract_internal_spline_from(
                &blueprint.properties,
                oneshot,
                Some(blueprint.id),
            );
        }
        #[allow(clippy::cast_precision_loss, clippy::as_conversions)]
        if let Some(delay_by_eights) = blueprint
            .registered_fixture_delays
            .iter()
            .find(|blueprint_fx_id| blueprint_fx_id.fixture_id == self.id())
        {
            self.blueprint_delay_eights = delay_by_eights.delay_eights as f32;
        } else {
            self.blueprint_delay_eights = 0.;
        }
        self.blueprint_random_delay = random::<u8>().into();
    }
    pub fn apply_timecode(
        &mut self,
        timecode: &TimecodeFileDescriptor,
        dmx_renderer: &mut DbHandler,
    ) {
        dmx_renderer.request_snippet_state_reset();

        let all_properties = all_properties_from_tracks(&timecode.tracks);

        let mut snippets_in_timecode = vec![];
        for snippet in &all_properties {
            if let TimedPropertyFileDescriptor::CallSnippet(
                snippet_id,
                x_offset,
                fixture_ids,
            ) = snippet
            {
                if fixture_ids.contains(&self.id()) {
                    #[allow(
                        clippy::cast_precision_loss,
                        clippy::as_conversions
                    )]
                    snippets_in_timecode.push(SnippetCall {
                        x: (*x_offset) as f32,
                        snippet_id: *snippet_id,
                    });
                }
            }
        }
        snippets_in_timecode.sort_by(|a, b| a.x.total_cmp(&b.x));
        self.snippet_calls_of_current_timecode = snippets_in_timecode;

        let merged_properties =
            merge_by_type_for_fixture(&all_properties, self.id());

        if let Some(ref mut color) = self.fixture_type.color {
            color.extract_internal_spline_from(&merged_properties, true, None);
        }

        for unimplemented_channel in
            &mut self.fixture_type.unimplemented_channels
        {
            unimplemented_channel.extract_internal_spline_from(
                &merged_properties,
                true,
                None,
            );
        }
    }
    pub const fn pan_to(&mut self, new_pan: u8) {
        if let Some(movement_channels) =
            self.fixture_type.movement_channels.as_mut()
        {
            movement_channels.set_pan(new_pan);
            movement_channels.set_pan_origin(new_pan);
        }
    }
    pub fn add_to_pan(&mut self, modifier: u8) {
        if let Some(movement_channels) =
            self.fixture_type.movement_channels.as_mut()
        {
            movement_channels.add_to_pan(modifier);
        }
    }
    pub const fn tilt_to(&mut self, new_tilt: u8) {
        if let Some(movement_channels) =
            self.fixture_type.movement_channels.as_mut()
        {
            movement_channels.set_tilt(new_tilt);
            movement_channels.set_tilt_origin(new_tilt);
        }
    }
    pub fn add_to_tilt(&mut self, modifier: u8) {
        if let Some(movement_channels) =
            self.fixture_type.movement_channels.as_mut()
        {
            movement_channels.add_to_tilt(modifier);
        }
    }
    pub const fn set_pan_origin(&mut self, origin: u8) {
        if let Some(movement_channels) =
            self.fixture_type.movement_channels.as_mut()
        {
            movement_channels.set_pan_origin(origin);
        }
    }
    pub const fn set_tilt_origin(&mut self, origin: u8) {
        if let Some(movement_channels) =
            self.fixture_type.movement_channels.as_mut()
        {
            movement_channels.set_tilt_origin(origin);
        }
    }
    #[must_use]
    pub fn color(&self) -> Vec<RgbColor> {
        self.fixture_type
            .color
            .iter()
            .map(|color_channel| color_channel.color)
            .collect()
    }
    pub fn set_color(
        &mut self,
        rgb_color: &RgbColor,
        fade_duration: Option<u8>,
    ) {
        if let Some(ref mut color) = self.fixture_type.color {
            if let Some(fade_duration) = fade_duration {
                color.create_spline_from_current_to_target_color(
                    *rgb_color,
                    fade_duration.into(),
                    self.index,
                );
            } else {
                color.color = *rgb_color;
                color.clear_all_splines();
            }
        }
    }
    pub const fn set_dimmer_value(&mut self, new_dimmer_value: u8) {
        self.dimmer_value = new_dimmer_value;
    }
    pub const fn set_speed_of_blueprints(&mut self, speed: u8) {
        self.speed_of_blueprints = speed;
    }
    #[must_use]
    pub const fn get_stage_coordinates(&self) -> (usize, usize) {
        self.fixture_instance.stage_coordinates
    }
    pub fn set_stage_coordinates(&mut self, _coordinates: (usize, usize)) {
        todo!("set_stage_coordinates")
        // self.fixture_instance.stage_coordinates = coordinates;
    }
    #[must_use]
    pub const fn get_enabled(&self) -> bool {
        self.enabled
    }
    pub const fn set_enabled(&mut self, value: bool) {
        self.enabled = value;
    }
    pub fn set_unimplemented_channel(&mut self, name: &String, value: u8) {
        for channel in &mut self.fixture_type.unimplemented_channels {
            if channel.name() == *name {
                channel.set_value(value);
            }
        }
    }
    pub fn reset_unimplemented_channel(&mut self, name: &String) {
        for channel in &mut self.fixture_type.unimplemented_channels {
            if *channel.name() == *name {
                channel.set_to_default();
            }
        }
    }
    pub fn update_all_blueprint_position_indices(&mut self, index: f32) {
        if let Some(ref mut color) = self.fixture_type.color {
            color.apply_spline_index(index);
        }

        if let Some(movement_channels) =
            self.fixture_type.movement_channels.as_mut()
        {
            movement_channels.apply_spline_index(index);
        }

        for unimplemented_channel in
            &mut self.fixture_type.unimplemented_channels
        {
            unimplemented_channel.apply_spline_index(index);
        }
    }
    // pub fn update_all_timecode_position_indices(
    //     &mut self,
    //     index: f32,
    //     db_handler: &mut DbHandler,
    //     input_parser: &mut InputParser,
    // ) {
    //     // logging::debug(format!("{index:?}"));
    //     let snippet_call_index = self
    //         .snippet_calls_of_current_timecode
    //         .iter()
    //         .rposition(|snippet_call| snippet_call.x < index);
    //     if let Some(snippet_call_index) = snippet_call_index {
    //         for snippet_call in self
    //             .snippet_calls_of_current_timecode
    //             .drain(..=snippet_call_index)
    //         {
    //             if let Some(snippet) =
    //                 db_handler.find_snippet_by_id(snippet_call.snippet_id)
    //             {
    //                 input_parser.push_one_time_instructions(
    //                     &snippet.instructions,
    //                     PRESSED,
    //                 );
    //             }
    //         }
    //     }
    //
    //     if let Some(ref mut color) = self.color {
    //         color.apply_spline_index(index);
    //     }
    //
    //     if let Some(movement_channels) = self.movement_channels.as_mut() {
    //         movement_channels.apply_spline_index(index);
    //     }
    //
    //     for unimplemented_channel in &mut self.unimplemented_channels {
    //         unimplemented_channel.apply_spline_index(index);
    //     }
    // }
    pub fn update_spline_index(
        &mut self,
        position_index: Option<f32>,
        delta: Option<f32>,
    ) {
        if delta.is_none() {
            if let Some(position_index) = position_index {
                self.index = position_index;
            } else {
                logging::log("`delta` and `position_index` cannot be both *None* when updating the index on fixtures".to_owned(), logging::LogLevel::Warning, false);
            }
        } else if position_index.is_none() {
            if let Some(delta) = delta {
                if delta != 0. {
                    let speed_multiplier =
                        f32::from(self.speed_of_blueprints) / 100.0;
                    self.index += delta * speed_multiplier;
                }
            } else {
                logging::log("`delta` and `position_index` cannot be both *None* when updating the index on fixtures".to_owned(), logging::LogLevel::Warning, false);
            }
        } else {
            logging::log("`delta` and `position_index` cannot both be *Some* when updating the index on fixtures".to_owned(), logging::LogLevel::Warning, false);
        }
    }
    pub fn get_dmx_footprint(
        &mut self,
        _db_handler: &mut DbHandler,
        _input_parser: &mut InputParser,
    ) -> Vec<u8> {
        self.update_all_blueprint_position_indices(
            // TODO: investigate. This might be broken, but i dont know
            match self.position_index_offset_mode {
                PositionIndexOffsetMode::Matching => self.index,
                PositionIndexOffsetMode::CustomOffset => {
                    #[allow(clippy::suboptimal_flops)]
                    {
                        self.index
                            - self.blueprint_delay_eights
                                * DMX_RENDERER_BEAT_IS_INDEX_POINTS
                    }
                }
                #[allow(clippy::suboptimal_flops)]
                PositionIndexOffsetMode::Random => {
                    self.index
                        - self.blueprint_random_delay
                            * DMX_RENDERER_BEAT_IS_INDEX_POINTS
                }
                PositionIndexOffsetMode::StageUpToDown
                | PositionIndexOffsetMode::StageDownToUp
                | PositionIndexOffsetMode::StageLeftToRight
                | PositionIndexOffsetMode::StageRightToLeft => 0.,
            },
        );

        // self.update_all_timecode_position_indices(
        //     self.index,
        //     db_handler,
        //     input_parser,
        // );

        let mut footprint = vec![0_u8; self.fixture_type.footprint_size];

        let mut dmx_channel_emitter: Vec<Box<dyn DmxChannelEmitter>> = vec![];

        if let Some(color) = self.fixture_type.color.clone() {
            dmx_channel_emitter.push(Box::new(color));
        }

        if let Some(movement_channels) =
            self.fixture_type.movement_channels.clone()
        {
            dmx_channel_emitter.push(Box::new(movement_channels));
        }
        for unimplemented_channel in
            self.fixture_type.unimplemented_channels.clone()
        {
            dmx_channel_emitter.push(Box::new(unimplemented_channel));
        }
        for dmx_channel_emitter in &mut dmx_channel_emitter {
            let dmx_channels =
                dmx_channel_emitter.compute_dmx_channels(self.dimmer_value);
            for dmx_channel in dmx_channels {
                if let Some(channel) =
                    footprint.get_mut(dmx_channel.channel.saturating_sub(1))
                {
                    *channel = dmx_channel.value;
                }
            }
        }
        footprint
    }
    #[must_use]
    pub const fn get_timecode_dmx_footprint(&self) -> Vec<u8> {
        vec![]
    }
    #[must_use]
    pub const fn dmx_address(&self) -> Option<usize> {
        self.fixture_instance.dmx_address
    }
    #[must_use]
    pub const fn dmx_universe(&self) -> u16 {
        self.fixture_instance.dmx_universe
    }
    #[must_use]
    pub const fn get_footprint_size(&self) -> usize {
        self.fixture_type.footprint_size
    }
    #[must_use]
    pub fn unimplemented_channels(&self) -> Vec<&UnimplementedChannel> {
        self.fixture_type.unimplemented_channels.iter().collect()
    }
    #[must_use]
    pub fn get_function_value(&self, channel: &str) -> Option<u8> {
        match channel {
            "pan" => {
                Some(self.fixture_type.movement_channels.clone()?.pan_tilt().0)
            }
            "tilt" => {
                Some(self.fixture_type.movement_channels.clone()?.pan_tilt().1)
            }
            "red" => Some(
                self.fixture_type
                    .color
                    .clone()
                    .map(|color| color.color.red)
                    .unwrap_or_default(),
            ),
            "green" => Some(
                self.fixture_type
                    .color
                    .clone()
                    .map(|color| color.color.green)
                    .unwrap_or_default(),
            ),
            "blue" => Some(
                self.fixture_type
                    .color
                    .clone()
                    .map(|color| color.color.blue)
                    .unwrap_or_default(),
            ),
            channel => {
                let unimplemented_channel =
                    self.fixture_type.unimplemented_channels.iter().find(
                        |unimplemented_channel| {
                            unimplemented_channel.name() == channel
                        },
                    )?;
                Some(unimplemented_channel.value())
            }
        }
    }
    pub fn clear_all_splines(&mut self) {
        if let Some(ref mut color) = self.fixture_type.color {
            color.clear_all_splines();
        }
        if let Some(ref mut movement_channels) =
            self.fixture_type.movement_channels
        {
            movement_channels.clear_all_splines();
        }
        for unimplemented_channel in
            &mut self.fixture_type.unimplemented_channels
        {
            unimplemented_channel.clear_all_splines();
        }
    }
    pub fn clear_splines_by_blueprint_id(&mut self, blueprint_id: usize) {
        if let Some(ref mut color) = self.fixture_type.color {
            color.clear_splines_by_blueprint_id(blueprint_id);
        }
        if let Some(ref mut movement_channels) =
            self.fixture_type.movement_channels
        {
            movement_channels.clear_splines_by_blueprint_id(blueprint_id);
        }
        for unimplemented_channel in
            &mut self.fixture_type.unimplemented_channels
        {
            unimplemented_channel.clear_splines_by_blueprint_id(blueprint_id);
        }
    }
}
