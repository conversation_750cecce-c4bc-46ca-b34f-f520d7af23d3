use mysql::{prelude::Queryable, PooledConn};
use serde::{Deserialize, Serialize};
use ts_rs::TS;

use crate::{
    dmx_renderer::channel::{
        color_channels::RgbColor,
        unimplemented_channels::UnimplementedChannelSetter,
    },
    input_parser::structs::{
        AddToGroup, BlueprintToInstruction, ColorToInstruction, Comparison,
        FixtureLoop, FunctionGetter, IfStatement, Instruction,
        InstructionValue, LoopStatement, MathOperator, NumberValue,
        RemoveFromGroup, TimecodeToInstruction, VariableSetter,
    },
    logging,
};

use super::{
    directory_tree::{Directory, Identifiable, TreeItem},
    DbHandler,
};
use core::fmt;

#[derive(
    Default, PartialEq, Eq, Debug, Clone, Copy, Serialize, Deserialize, TS,
)]
#[ts(export)]
pub enum SnippetCategory {
    Startup,
    #[default]
    Key,
    Watcher,
    Callable,
}
impl fmt::Display for SnippetCategory {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            Self::Startup => write!(f, "Startup"),
            Self::Key => write!(f, "Key"),
            Self::Watcher => write!(f, "Watcher"),
            Self::Callable => write!(f, "Callable"),
        }
    }
}

#[derive(Debug, Clone, Serialize, TS)]
#[ts(export)]
pub struct LibrarySnippet {
    pub category: SnippetCategory,
    pub name: String,
    pub do_not_use_instructions: bool,
    pub instructions: Vec<Instruction>,
}
#[derive(Default, Debug, Clone, Serialize, Deserialize, PartialEq, TS)]
#[ts(export)]
pub struct Snippet {
    pub id: usize,
    pub category: SnippetCategory,
    pub serial_module_key: Option<u16>,
    pub name: String,
    pub do_not_use_instructions: bool,
    pub instructions: Vec<Instruction>,
    pub requires_user_action_reason: Option<String>,
}

impl Identifiable for Snippet {
    type Identifier = usize;

    fn identify(&self, identifier: Self::Identifier) -> bool {
        self.id == identifier
    }
}

fn saturate_snippet(
    db_connection: &mut PooledConn,
    snippet_id: usize,
) -> Vec<Instruction> {
    let mut instruction_ids: Vec<usize> = vec![];
    db_connection
        .query_map(
            format!(
                "
                    SELECT i.id
                    FROM instructions i
                    JOIN snippets s ON i.snippet_id = s.id
                    WHERE s.id = {snippet_id}
                ",
            ),
            |id| {
                instruction_ids.push(id);
            },
        )
        .unwrap_or_else(|err| {
            logging::log(
                format!("{err:?}\n        (selecting instructions of snippet)"),
                logging::LogLevel::DbError,
                true,
            );
            vec![]
        });
    instruction_ids
        .iter()
        .map(|id| DbHandler::instruction(db_connection, *id))
        .collect()
}
fn saturate_result_dir_content(
    db_connection: &mut PooledConn,
    parent_dir_id: usize,
    directory: &mut Directory<Snippet>,
) {
    db_connection
        .query_map(
            format!(
                "
                    SELECT
                        s.id,
                        s.name,
                        s.category,
                        s.do_not_use_instructions,
                        s.serial_module_key,
                        s.requires_user_action_reason
                    FROM snippets AS s
                    JOIN snippet_dirs AS sd ON s.parent_dir_id = sd.id
                    WHERE s.parent_dir_id = {parent_dir_id}
                "
            ),
            |(
                id,
                name,
                db_category,
                do_not_use_instructions,
                serial_module_key,
                requires_user_action_reason,
            ): (
                usize,
                String,
                Option<String>,
                bool,
                Option<u16>,
                Option<String>,
            )| {
                let category =
                    db_category.map_or(SnippetCategory::Key, |db_category| {
                        match db_category.as_str() {
                            "Startup" => SnippetCategory::Startup,
                            "Watcher" => SnippetCategory::Watcher,
                            "Callable" => SnippetCategory::Callable,
                            _ /* | "Key" */ => SnippetCategory::Key,
                        }
                    });
                directory.content.push(TreeItem::Item(Snippet {
                    id,
                    category,
                    serial_module_key,
                    name,
                    do_not_use_instructions,
                    instructions: vec![],
                    requires_user_action_reason,
                }));
            },
        )
        .unwrap_or_else(|err| {
            logging::log(
                format!(
                    "{err:?}\n        (selecting child snippets of directory)"
                ),
                logging::LogLevel::DbError,
                true,
            );
            vec![]
        });
}

impl DbHandler {
    #[must_use]
    pub fn snippets_dir_for_active_show(
        db_connection: &mut PooledConn,
    ) -> Option<Directory<Snippet>> {
        db_connection
            .query_map(
                "
                    SELECT snippet_dirs.id
                    FROM snippet_dirs
                    JOIN shows on snippet_dirs.show_id = shows.id
                    WHERE shows.active
                ",
                |id: usize| id,
            )
            .unwrap_or_else(|err| {
                logging::log(
                    format!(
                        "{err:?}\n        (select snippet dir for active show)"
                    ),
                    logging::LogLevel::DbError,
                    true,
                );
                vec![]
            })
            .first()
            .map(|id| Self::directory(db_connection, *id))
    }

    fn directory(
        db_connection: &mut PooledConn,
        id: usize,
    ) -> Directory<Snippet> {
        let mut result_dir_name = String::new();
        let mut result_dir = Directory {
            id,
            name: db_connection
                .query_map(
                    format!(
                        "
                            SELECT name
                            FROM snippet_dirs
                            WHERE id = {id}
                        "
                    ),
                    |name| name,
                )
                .unwrap_or_else(|err| {
                    logging::log(
                        format!(
                            "{err:?}\n        (selecting child directories)"
                        ),
                        logging::LogLevel::DbError,
                        true,
                    );
                    vec![]
                })
                .first()
                .cloned()
                .unwrap_or_default(),
            content: db_connection
                .query_map(
                    format!(
                        "
                            SELECT c.id, p.name
                            FROM snippet_dirs AS c
                            JOIN snippet_dirs AS p ON c.parent_dir_id = p.id
                            WHERE p.id = {id}
                        "
                    ),
                    |(child_id, parent_name)| {
                        result_dir_name = parent_name;
                        child_id
                    },
                )
                .unwrap_or_else(|err| {
                    logging::log(
                        format!(
                            "{err:?}\n        (selecting child directories)"
                        ),
                        logging::LogLevel::DbError,
                        true,
                    );
                    vec![]
                })
                .iter()
                .map(|dir_id| {
                    TreeItem::Directory(Self::directory(db_connection, *dir_id))
                })
                .collect(),
        };
        if !result_dir_name.is_empty() {
            result_dir.name = result_dir_name;
        }

        saturate_result_dir_content(db_connection, id, &mut result_dir);

        for tree_item in &mut result_dir.content {
            if let TreeItem::Item(ref mut snippet) = tree_item {
                snippet.instructions =
                    saturate_snippet(db_connection, snippet.id);
            }
        }
        result_dir
    }
    #[allow(clippy::too_many_lines)]
    fn instruction(db_connection: &mut PooledConn, id: usize) -> Instruction {
        let result_instruction = db_connection
            .query_map(
                format!(
                    "
                            SELECT
                                variant,
                                int_val,
                                str_val,
                                bool_val_1,
                                bool_val_2
                            FROM instructions
                            WHERE instructions.id = {id}
                            "
                ),
                |(variant, int_val, str_val, bool_val_1, bool_val_2): (
                    String,
                    usize,
                    String,
                    bool,
                    bool,
                )| {
                    match variant.as_str() {
                        "ColorTo" => Instruction::ColorTo(ColorToInstruction {
                            color: RgbColor::from_hex(&str_val),
                            fade_duration: None,
                        }),
                        "StartRecording" => Instruction::StartRecording,
                        "StopRecording" => Instruction::StopRecording,
                        "ClearRecording" => Instruction::ClearRecording,
                        "ColorToRandom" => Instruction::ColorToRandom(None),
                        "SetSpeedOfBlueprints" => {
                            Instruction::SetSpeedOfBlueprints(
                                InstructionValue::Default,
                            )
                        }
                        "ActivateAllFixtures" => {
                            Instruction::ActivateAllFixtures
                        }
                        "ActivateFixtureById" => {
                            Instruction::ActivateFixtureById(int_val)
                        }
                        "ActivateFixtureGroup" => {
                            Instruction::ActivateFixtureGroup(str_val)
                        }
                        "BlueprintTo" => {
                            Instruction::BlueprintTo(BlueprintToInstruction {
                                id: int_val,
                                oneshot: bool_val_1,
                                delay: bool_val_2,
                            })
                        }
                        "TimecodeTo" => {
                            Instruction::TimecodeTo(TimecodeToInstruction {
                                id: int_val,
                                index: 0,
                                play: true,
                            })
                        }
                        "SetBlueprintPositionIndexOffsetMode" => {
                            Instruction::SetBlueprintPositionIndexOffsetMode(
                                str_val.into(),
                            )
                        }
                        "ToggleQueueMode" => {
                            Instruction::ToggleQueueMode(str_val.into())
                        }
                        "UnimplementedChannelTo" => {
                            Instruction::UnimplementedChannelTo(
                                UnimplementedChannelSetter {
                                    name: str_val,
                                    value: InstructionValue::Default,
                                },
                            )
                        }
                        "ClearBlueprint" => {
                            Instruction::ClearBlueprint(int_val)
                        }
                        "PanTo" => {
                            Instruction::PanTo(InstructionValue::Default)
                        }
                        "ExecuteCallableSnippet" => {
                            Instruction::ExecuteCallableSnippet(int_val)
                        }
                        "AddToPan" => {
                            Instruction::AddToPan(InstructionValue::Default)
                        }
                        "TiltTo" => {
                            Instruction::TiltTo(InstructionValue::Default)
                        }
                        "AddToTilt" => {
                            Instruction::AddToTilt(InstructionValue::Default)
                        }
                        "PositionTo" => Instruction::PositionTo(int_val),
                        "BpmTo" => {
                            Instruction::BpmTo(InstructionValue::Default)
                        }
                        "BpmModifierTo" => Instruction::BpmModifierTo(
                            InstructionValue::Default,
                        ),
                        "FixtureLoop" => {
                            Instruction::FixtureLoop(FixtureLoop {
                                fixtures: vec![],
                                instructions: vec![],
                            })
                        }
                        "CreateVariable" => {
                            Instruction::CreateVariable(str_val)
                        }
                        "SetVariable" => {
                            Instruction::SetVariable(VariableSetter {
                                name: str_val,
                                value: InstructionValue::Default,
                            })
                        }
                        "AddToGroup" => Instruction::AddToGroup(AddToGroup {
                            name: str_val,
                            fixtures: vec![],
                        }),
                        "RemoveFromGroup" => {
                            Instruction::RemoveFromGroup(RemoveFromGroup {
                                name: str_val,
                                fixtures: vec![],
                            })
                        }
                        "DelayBy" => Instruction::DelayBy(
                            InstructionValue::Default,
                        ),
                        "DeselectAllFixtures" => {
                            Instruction::DeselectAllFixtures
                        }
                        "DimmerTo" => {
                            Instruction::DimmerTo(InstructionValue::Default)
                        }
                        "IfStatement" => {
                            Instruction::IfStatement(IfStatement {
                                comparison: Comparison {
                                    left_value: InstructionValue::Default,
                                    right_value: InstructionValue::Default,
                                    comparator: str_val.into(),
                                },
                                truthy_branch: vec![],
                                falsy_branch: vec![],
                            })
                        }
                        "LoopStatement" => {
                            Instruction::LoopStatement(LoopStatement {
                                comparison: Comparison {
                                    left_value: InstructionValue::Default,
                                    right_value: InstructionValue::Default,
                                    comparator: str_val.into(),
                                },
                                content: vec![],
                            })
                        }
                        "IfQueueAllowsContinue" => {
                            Instruction::IfQueueAllowsContinue(vec![])
                        }
                        _ => Instruction::Nop,
                    }
                },
            )
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (selecting instruction from )"),
                    logging::LogLevel::DbError,
                    true,
                );
                vec![]
            });
        result_instruction.first().map_or(Instruction::Nop, |result_instruction| {
            match result_instruction {
                Instruction::DimmerTo(_) => {
                    Instruction::DimmerTo(Self::instruction_value(
                        db_connection,
                        Some(id),
                        None,
                        None,
                    ))
                }
                Instruction::SetSpeedOfBlueprints(_) => {
                    Instruction::SetSpeedOfBlueprints(Self::instruction_value(
                        db_connection,
                        Some(id),
                        None,
                        None,
                    ))
                }
                Instruction::PanTo(_) => {
                    Instruction::PanTo(Self::instruction_value(
                        db_connection,
                        Some(id),
                        None,
                        None,
                    ))
                }
                Instruction::AddToPan(_) => {
                    Instruction::AddToPan(Self::instruction_value(
                        db_connection,
                        Some(id),
                        None,
                        None,
                    ))
                }
                Instruction::TiltTo(_) => {
                    Instruction::TiltTo(Self::instruction_value(
                        db_connection,
                        Some(id),
                        None,
                        None,
                    ))
                }
                Instruction::AddToTilt(_) => {
                    Instruction::AddToTilt(Self::instruction_value(
                        db_connection,
                        Some(id),
                        None,
                        None,
                    ))
                }
                Instruction::BpmTo(_) => {
                    Instruction::BpmTo(Self::instruction_value(
                        db_connection,
                        Some(id),
                        None,
                        None,
                    ))
                }
                Instruction::BpmModifierTo(_) => {
                    Instruction::BpmModifierTo(Self::instruction_value(
                        db_connection,
                        Some(id),
                        None,
                        None,
                    ))
                }
                Instruction::DelayBy(_) => {
                    Instruction::DelayBy(Self::instruction_value(
                        db_connection,
                        Some(id),
                        None,
                        None,
                    ))
                }

                Instruction::FixtureLoop(_) => {
                    Instruction::FixtureLoop(FixtureLoop {
                        fixtures: {
                            let mut g_ids: Vec<usize> = vec![];
                            db_connection
                        .query_map(
                            format!(
                                "
                                    SELECT ivv.id FROM instructions i
                                    JOIN instructions ivv ON i.id = ivv.parent_instruction_id
                                    WHERE i.id = {id} AND ivv.branch = 'fixtures'
                                "
                            ),
                            |id| {
                                g_ids.push(id);
                            },
                        )
                        .unwrap_or_else(|err| {
                            logging::log(
                                format!(
                        "{err:?}\n        (selecting fixtures inside fx_loop)"
                    ),
                                logging::LogLevel::DbError,
                                true,
                            );
                            vec![]
                        });
                            let mut result = vec![];
                            for g_id in &g_ids {
                                result.push(Self::instruction(
                                    db_connection,
                                    *g_id,
                                ));
                            }
                            result
                        },
                        instructions: {
                            let mut g_ids: Vec<usize> = vec![];
                            db_connection
                        .query_map(
                            format!(
                                "
                                    SELECT ivv.id FROM instructions i
                                    JOIN instructions ivv ON i.id = ivv.parent_instruction_id
                                    WHERE i.id = {id} AND ivv.branch = 'instructions'
                                "
                            ),
                            |id| {
                                g_ids.push(id);
                            },
                        )
                        .unwrap_or_else(|err| {
                            logging::log(
                                format!(
                        "{err:?}\n        (selecting instructions inside fx_loop)"
                    ),
                                logging::LogLevel::DbError,
                                true,
                            );
                            vec![]
                        });
                            let mut result = vec![];
                            for g_id in &g_ids {
                                result.push(Self::instruction(
                                    db_connection,
                                    *g_id,
                                ));
                            }
                            result
                        },
                    })
                }
                Instruction::SetVariable(sv) => {
                    Instruction::SetVariable(VariableSetter {
                        value: Self::instruction_value(
                            db_connection,
                            Some(id),
                            None,
                            None,
                        ),
                        name: sv.name.clone(),
                    })
                }
                Instruction::AddToGroup(atg) => {
                    Instruction::AddToGroup(AddToGroup {
                        fixtures: {
                            let mut g_ids: Vec<usize> = vec![];
                            db_connection
                        .query_map(
                            format!(
                                "
                                    SELECT ivv.id FROM instructions i
                                    JOIN instructions ivv ON i.id = ivv.parent_instruction_id
                                    WHERE i.id = {id} AND ivv.branch = 'fixtures'
                                "
                            ),
                            |id| {
                                g_ids.push(id);
                            },
                        )
                        .unwrap_or_else(|err| {
                            logging::log(
                                format!(
                        "{err:?}\n        (selecting fixtures inside AddToGroup)"
                    ),
                                logging::LogLevel::DbError,
                                true,
                            );
                            vec![]
                        });
                            let mut result = vec![];
                            for g_id in &g_ids {
                                result.push(Self::instruction(
                                    db_connection,
                                    *g_id,
                                ));
                            }
                            result
                        },
                        name: atg.name.clone(),
                    })
                }
                Instruction::RemoveFromGroup(rfg) => {
                    Instruction::RemoveFromGroup(RemoveFromGroup {
                        fixtures: {
                            let mut g_ids: Vec<usize> = vec![];
                            db_connection
                        .query_map(
                            format!(
                                "
                                    SELECT ivv.id FROM instructions i
                                    JOIN instructions ivv ON i.id = ivv.parent_instruction_id
                                    WHERE i.id = {id} AND ivv.branch = 'fixtures'
                                "
                            ),
                            |id| {
                                g_ids.push(id);
                            },
                        )
                        .unwrap_or_else(|err| {
                            logging::log(
                                format!(
                        "{err:?}\n        (selecting fixtures inside RemoveFromGroup)"
                    ),
                                logging::LogLevel::DbError,
                                true,
                            );
                            vec![]
                        });
                            let mut result = vec![];
                            for g_id in &g_ids {
                                result.push(Self::instruction(
                                    db_connection,
                                    *g_id,
                                ));
                            }
                            result
                        },
                        name: rfg.name.clone(),
                    })
                }
                Instruction::ColorTo(color_to) => {

                    let iv_count = db_connection
                        .query_map(
                            format!(
                                "
                                    SELECT count(iv.id)
                                    FROM instruction_values iv
                                    JOIN instructions i ON i.id = iv.instruction_id
                                    WHERE i.id = {id}
                                "
                            ),
                            |count: usize| count,
                        )
                        .unwrap_or_else(|err| {
                            logging::log(
                                format!(
                            "{err:?}\n        (Counting instruction_values by id)"
                        ),
                                logging::LogLevel::DbError,
                                true,
                            );
                            vec![]
                        });
                    Instruction::ColorTo(
                        ColorToInstruction {
                            color: color_to.color,
                            fade_duration:
                                if iv_count.is_empty() || iv_count.first().is_some_and(|n| *n == 0) {
                                    None
                                } else {
                                    Some(Self::instruction_value(db_connection, Some(id), None, None))
                                }
                        }
                    )
                }
                Instruction::ColorToRandom(_) => {
                    let iv_count = db_connection
                        .query_map(
                            format!(
                                "
                                    SELECT count(iv.id)
                                    FROM instruction_values iv
                                    JOIN instructions i ON i.id = iv.instruction_id
                                    WHERE i.id = {id}
                                "
                            ),
                            |count: usize| count,
                        )
                        .unwrap_or_else(|err| {
                            logging::log(
                                format!(
                            "{err:?}\n        (Counting instruction_values by id)"
                        ),
                                logging::LogLevel::DbError,
                                true,
                            );
                            vec![]
                        });
                    Instruction::ColorToRandom(
                        if iv_count.is_empty() || iv_count.first().is_some_and(|n| *n == 0) {
                            None
                        }else {
                            Some(Self::instruction_value(db_connection, Some(id), None, None))
                        }
                    )

                }
                Instruction::UnimplementedChannelTo(uct) => {
                    Instruction::UnimplementedChannelTo(
                        UnimplementedChannelSetter {
                            value: Self::instruction_value(
                                db_connection,
                                Some(id),
                                None,
                                None,
                            ),
                            name: uct.name.clone(),
                        },
                    )
                }

                Instruction::StartRecording
                | Instruction::StopRecording
                | Instruction::ClearRecording
                | Instruction::DeselectAllFixtures
                | Instruction::ClearBlueprint(_)
                | Instruction::CreateVariable(_)
                | Instruction::PositionTo(_)
                | Instruction::ActivateAllFixtures
                | Instruction::ActivateFixtureById(_)
                | Instruction::ActivateFixtureGroup(_)
                | Instruction::BlueprintTo(_)
                | Instruction::TimecodeTo(_)
                | Instruction::SetBlueprintPositionIndexOffsetMode(_)
                | Instruction::ToggleQueueMode(_)
                | Instruction::ExecuteCallableSnippet(_)
                | Instruction::Nop => result_instruction.clone(),

                Instruction::IfStatement(is) => {
                    Instruction::IfStatement(IfStatement {
                        truthy_branch: {
                            let mut g_ids: Vec<usize> = vec![];
                            db_connection
                        .query_map(
                            format!(
                                "
                                    SELECT ivv.id FROM instructions i
                                    JOIN instructions ivv ON i.id = ivv.parent_instruction_id
                                    WHERE i.id = {id} AND ivv.branch = 'truthy_branch'
                                "
                            ),
                            |id| {
                                g_ids.push(id);
                            },
                        )
                        .unwrap_or_else(|err| {
                            logging::log(
                                format!(
                        "{err:?}\n        (selecting truthy_branch inside IfStatement)"
                    ),
                                logging::LogLevel::DbError,
                                true,
                            );
                            vec![]
                        });
                            let mut result = vec![];
                            for g_id in &g_ids {
                                result.push(Self::instruction(
                                    db_connection,
                                    *g_id,
                                ));
                            }
                            result
                        },
                        falsy_branch: {
                            let mut g_ids: Vec<usize> = vec![];
                            db_connection
                        .query_map(
                            format!(
                                "
                                    SELECT ivv.id FROM instructions i
                                    JOIN instructions ivv ON i.id = ivv.parent_instruction_id
                                    WHERE i.id = {id} AND ivv.branch = 'falsy_branch'
                                "
                            ),
                            |id| {
                                g_ids.push(id);
                            },
                        )
                        .unwrap_or_else(|err| {
                            logging::log(
                                format!(
                        "{err:?}\n        (selecting falsy_branch inside IfStatement)"
                    ),
                                logging::LogLevel::DbError,
                                true,
                            );
                            vec![]
                        });
                            let mut result = vec![];
                            for g_id in &g_ids {
                                result.push(Self::instruction(
                                    db_connection,
                                    *g_id,
                                ));
                            }
                            result
                        },
                        comparison: Comparison {
                            left_value: Self::instruction_value(
                                db_connection,
                                Some(id),
                                None,
                                Some("left_value".to_owned()),
                            ),
                            right_value: Self::instruction_value(
                                db_connection,
                                Some(id),
                                None,
                                Some("right_value".to_owned()),
                            ),
                            comparator: is.comparison.comparator,
                        },
                    })
                }
                Instruction::LoopStatement(ls) => {
                    Instruction::LoopStatement(LoopStatement {
                        comparison: Comparison {
                            left_value: Self::instruction_value(
                                db_connection,
                                Some(id),
                                None,
                                Some("left_value".to_owned()),
                            ),
                            right_value: Self::instruction_value(
                                db_connection,
                                Some(id),
                                None,
                                Some("right_value".to_owned()),
                            ),
                            comparator: ls.comparison.comparator,
                        },
                        content: {
                            let mut g_ids: Vec<usize> = vec![];
                            db_connection
                        .query_map(
                            format!(
                                "
                                    SELECT ivv.id FROM instructions i
                                    JOIN instructions ivv ON i.id = ivv.parent_instruction_id
                                    WHERE i.id = {id} AND ivv.branch = 'content'
                                "
                            ),
                            |id| {
                                g_ids.push(id);
                            },
                        )
                        .unwrap_or_else(|err| {
                            logging::log(
                                format!(
                        "{err:?}\n        (selecting content inside LoopStatement)"
                    ),
                                logging::LogLevel::DbError,
                                true,
                            );
                            vec![]
                        });
                            let mut result = vec![];
                            for g_id in &g_ids {
                                result.push(Self::instruction(
                                    db_connection,
                                    *g_id,
                                ));
                            }
                            result
                        },
                    })
                }
                Instruction::IfQueueAllowsContinue(_) => {
                    Instruction::IfQueueAllowsContinue({
                        let mut g_ids: Vec<usize> = vec![];
                        db_connection
                        .query_map(
                            format!(
                                "
                                    SELECT ivv.id FROM instructions i
                                    JOIN instructions ivv ON i.id = ivv.parent_instruction_id
                                    WHERE i.id = {id} AND ivv.branch = 'content'
                                "
                            ),
                            |id| {
                                g_ids.push(id);
                            },
                        )
                        .unwrap_or_else(|err| {
                            logging::log(
                                format!(
                        "{err:?}\n        (selecting content inside IfQueueAllowsContinue)"
                    ),
                                logging::LogLevel::DbError,
                                true,
                            );
                            vec![]
                        });
                        let mut result = vec![];
                        for g_id in &g_ids {
                            result
                                .push(Self::instruction(db_connection, *g_id));
                        }
                        result
                    })
                }
            }
        })
    }
    #[allow(clippy::too_many_lines)]
    fn instruction_value(
        db_connection: &mut PooledConn,
        instruction_id: Option<usize>,
        instruction_value_id: Option<usize>,
        branch: Option<String>,
    ) -> InstructionValue {
        let mut result = InstructionValue::Default;
        let mut iv_id = 0;

        db_connection.query_map(
            if let Some(instruction_id) = instruction_id {
                format!(
                    "
                        SELECT
                            iv.id,
                            iv.variant,
                            iv.int_val,
                            iv.str_val,
                            iv.bool_val
                        FROM instruction_values iv
                        JOIN instructions i ON i.id = iv.instruction_id
                        WHERE iv.branch = '{}' AND i.id = {}
                    ",
                    branch.unwrap_or_default(),
                    instruction_id
                )
            } else {
                format!(
                    "
                        SELECT
                            ivv.id,
                            ivv.variant,
                            ivv.int_val,
                            ivv.str_val,
                            ivv.bool_val
                        FROM instruction_values iv
                            JOIN instruction_values ivv ON ivv.instruction_value_id = iv.id
                            WHERE ivv.branch = '{}' AND ivv.instruction_value_id = {}
                    ",
                    branch.unwrap_or_default(),
                    instruction_value_id.unwrap_or_default()
                )
            },
            |(id, variant, int_val, str_val, bool_val): (usize, String, usize, String, bool)| {
                iv_id = id;
                result = match variant.as_str() {
                    "Number" => {
                        #[allow(clippy::cast_possible_truncation, clippy::as_conversions)]
                        InstructionValue::Number (NumberValue{
                            value: int_val.clamp(0, 255) as u8,
                            dmx_precision: bool_val
                        })
                    }
                    "Variable" => InstructionValue::Variable(str_val),
                    "Keypoint" => InstructionValue::Keypoint(int_val),
                    "Function" => InstructionValue::Function(FunctionGetter {
                        fixture_id: int_val,
                        function: str_val,
                    }),
                    "Value" => InstructionValue::Value,
                    "Pressed" => InstructionValue::Pressed,
                    "Released" => InstructionValue::Released,
                    "Default" => InstructionValue::Default,
                    "Bpm" => InstructionValue::Bpm,
                    "BpmModifier" => InstructionValue::BpmModifier,
                    "MathOperator" => InstructionValue::MathOperator(Box::new(MathOperator {
                        left_value: InstructionValue::Default,
                        right_value: InstructionValue::Default,
                        operand: str_val.into(),
                    })),
                    "Random" => InstructionValue::Random((Box::new(InstructionValue::Default), Box::new(InstructionValue::Default))),
                    val => {
                        logging::log(format!("Unknown instruction_value: {val}"), logging::LogLevel::Warning, true);
                        InstructionValue::Default
                    }
                };
            },
        )
        .unwrap_or_else(|err| {
            logging::log(
                format!(
                    "{err:?}\n        (Selecting instructionvalue)"
                ),
                logging::LogLevel::DbError,
                true,
            );
            vec![]
        });
        if let InstructionValue::MathOperator(ref mut math_operator) = result {
            math_operator.left_value = Self::instruction_value(
                db_connection,
                None,
                Some(iv_id),
                Some("left_value".to_owned()),
            );
            math_operator.right_value = Self::instruction_value(
                db_connection,
                None,
                Some(iv_id),
                Some("right_value".to_owned()),
            );
        }
        if let InstructionValue::Random(ref mut random) = result {
            random.0 = Box::new(Self::instruction_value(
                db_connection,
                None,
                Some(iv_id),
                Some("left_value".to_owned()),
            ));
            random.1 = Box::new(Self::instruction_value(
                db_connection,
                None,
                Some(iv_id),
                Some("right_value".to_owned()),
            ));
        }
        result
    }

    pub fn create_default_snippet_directory(
        &mut self,
        parent_dir_id: usize,
    ) -> Option<usize> {
        self.db_connection()
            .query_drop(format!(
                "
                    INSERT INTO snippet_dirs (parent_dir_id, name)
                    VALUES ({parent_dir_id}, 'NEW')
                "
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Inserting new snippet_dir)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        let created_id = self.db_connection()
            .query_map(
                "
                    SELECT MAX(id) FROM snippet_dirs;
                "
                ,
                |id: usize| id,
            )
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Selecting just created snippets_dir)"),
                    logging::LogLevel::DbError,
                    true,
                );
                vec![]
            })
            .first()
            .copied();
        self.active_show.snippets_dir =
            Self::snippets_dir_for_active_show(&mut self.db_connection());
        created_id
    }
    pub fn create_root_snippet_directory(&mut self) -> Option<usize> {
        self.db_connection()
            .query_drop(
                "
                    INSERT INTO snippet_dirs (name, show_id)
                    VALUES ('', (SELECT id FROM shows WHERE active))
                ",
            )
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Inserting root snippet_dir)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        let created_id = self.db_connection()
            .query_map(
                "
                    SELECT MAX(id) FROM snippet_dirs;
                "
                ,
                |id: usize| id,
            )
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Selecting just created snippets_dir)"),
                    logging::LogLevel::DbError,
                    true,
                );
                vec![]
            })
            .first()
            .copied();
        self.active_show.snippets_dir =
            Self::snippets_dir_for_active_show(&mut self.db_connection());
        created_id
    }
    pub fn create_default_snippet(
        &mut self,
        parent_dir_id: usize,
    ) -> Option<usize> {
        self.db_connection()
            .query_drop(format!(
                "
                    INSERT INTO snippets (name, parent_dir_id, do_not_use_instructions)
                    VALUES ('NEW', {parent_dir_id}, 0)
                "
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Inserting new snippet)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        let created_id = self
            .db_connection()
            .query_map(
                "
                    SELECT MAX(id) FROM snippets;
                ",
                |id: usize| id,
            )
            .unwrap_or_else(|err| {
                logging::log(
                    format!(
                        "{err:?}\n        (Selecting just created snippet)"
                    ),
                    logging::LogLevel::DbError,
                    true,
                );
                vec![]
            })
            .first()
            .copied();
        self.active_show.snippets_dir =
            Self::snippets_dir_for_active_show(&mut self.db_connection());
        created_id
    }

    pub fn create_snippet_with_data(
        &mut self,
        snippet: Snippet,
        parent_dir_id: usize,
    ) -> Option<usize> {
        self.db_connection()
            .query_drop(format!(
                "
                    INSERT INTO snippets (name, parent_dir_id, do_not_use_instructions, category, serial_module_key)
                    VALUES ('{}', {}, {}, '{}', {})
                ",
                snippet.name,
                parent_dir_id,
                u8::from(snippet.do_not_use_instructions),
                snippet.category,
                snippet.serial_module_key.map_or("NULL".to_string(), |k| k.to_string())
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Inserting new snippet with data)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });

        let created_id = self
            .db_connection()
            .query_map(
                "
                    SELECT MAX(id) FROM snippets;
                ",
                |id: usize| id,
            )
            .unwrap_or_else(|err| {
                logging::log(
                    format!(
                        "{err:?}\n        (Selecting just created snippet with data)"
                    ),
                    logging::LogLevel::DbError,
                    true,
                );
                vec![]
            })
            .first()
            .copied();

        if let Some(snippet_id) = created_id {
            self.set_snippet_instructions(snippet_id, &snippet.instructions);

            if let Some(reason) = snippet.requires_user_action_reason {
                self.set_snippet_requires_user_action_reason(
                    snippet_id,
                    &Some(reason),
                );
            }
        }

        self.active_show.snippets_dir =
            Self::snippets_dir_for_active_show(&mut self.db_connection());
        created_id
    }

    pub fn delete_snippets_dir(&mut self, id: usize) {
        self.db_connection()
            .query_drop(format!(
                "
                    DELETE FROM snippet_dirs
                    WHERE id = {id}
                "
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Deleting snippets_dir)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        self.active_show.snippets_dir =
            Self::snippets_dir_for_active_show(&mut self.db_connection());
    }
    pub fn delete_snippet(&mut self, id: usize) {
        self.db_connection()
            .query_drop(format!(
                "
                    DELETE FROM snippets
                    WHERE id = {id}
                "
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Deleting snippet)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        self.active_show.snippets_dir =
            Self::snippets_dir_for_active_show(&mut self.db_connection());
    }
    pub fn set_snippets_dir_name(
        &mut self,
        directory_id: usize,
        new_name: &String,
    ) {
        self.db_connection()
            .query_drop(format!(
                "
                    UPDATE snippet_dirs
                    SET name = '{new_name}'
                    WHERE id = {directory_id};
                "
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Updating snippet_dir name)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        self.active_show.snippets_dir =
            Self::snippets_dir_for_active_show(&mut self.db_connection());
    }
    pub fn set_snippet_name(&mut self, snippet_id: usize, new_name: &String) {
        self.db_connection()
            .query_drop(format!(
                "
                    UPDATE snippets
                    SET name = '{new_name}'
                    WHERE id = {snippet_id};
                "
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Updating snippet name)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        self.active_show.snippets_dir =
            Self::snippets_dir_for_active_show(&mut self.db_connection());
    }
    pub fn set_snippet_category(
        &mut self,
        snippet_id: usize,
        new_category: SnippetCategory,
    ) {
        self.db_connection()
            .query_drop(format!(
                "
                    UPDATE snippets
                    SET category = '{new_category}'
                    WHERE id = {snippet_id};
                "
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Updating snippet category)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        self.active_show.snippets_dir =
            Self::snippets_dir_for_active_show(&mut self.db_connection());
    }
    pub fn set_snippet_do_not_use_instructions(
        &mut self,
        snippet_id: usize,
        new_do_not_use_instructions: bool,
    ) {
        self.db_connection()
            .query_drop(format!(
                "
                    UPDATE snippets
                    SET do_not_use_instructions = '{}'
                    WHERE id = {snippet_id};
                ", u8::from(new_do_not_use_instructions)
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Updating snippet new_do_not_use_instructions)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        self.active_show.snippets_dir =
            Self::snippets_dir_for_active_show(&mut self.db_connection());
    }
    pub fn set_snippet_serial_module_key(
        &mut self,
        snippet_id: usize,
        new_serial_module_key: Option<u16>,
    ) {
        self.db_connection()
            .query_drop(format!(
                "
                    UPDATE snippets
                    SET serial_module_key = {}
                    WHERE id = {snippet_id};
                ",
                new_serial_module_key.map_or("NULL".to_string(), |key| key.to_string())
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Updating snippet new_serial_module_key)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        self.active_show.snippets_dir =
            Self::snippets_dir_for_active_show(&mut self.db_connection());
    }
    pub fn set_snippet_requires_user_action_reason(
        &mut self,
        snippet_id: usize,
        new_requires_user_action_reason: &Option<String>,
    ) {
        self.db_connection()
            .query_drop(format!(
                "
                    UPDATE snippets
                    SET requires_user_action_reason = '{}'
                    WHERE id = {snippet_id};
                ",
                new_requires_user_action_reason.clone().unwrap_or_default()
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Updating snippet new_requires_user_action_reason)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        self.active_show.snippets_dir =
            Self::snippets_dir_for_active_show(&mut self.db_connection());
    }
    pub fn set_snippet_instructions(
        &mut self,
        snippet_id: usize,
        content: &[Instruction],
    ) {
        self.db_connection()
            .query_drop(format!(
                "
                    DELETE i FROM instructions i
                    LEFT JOIN snippets s ON s.id = i.snippet_id
                    WHERE s.id = {snippet_id};
                "
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Removing old instructions)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        for instruction in content {
            self.create_instruction(Some(snippet_id), None, instruction, None);
        }
        self.active_show.snippets_dir =
            Self::snippets_dir_for_active_show(&mut self.db_connection());
    }
    pub fn move_snippet_to_directory(
        &mut self,
        snippet_id: usize,
        new_parent_dir_id: usize,
    ) {
        self.db_connection()
            .query_drop(format!(
                "
                    UPDATE snippets
                    SET parent_dir_id = {new_parent_dir_id}
                    WHERE id = {snippet_id};
                "
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Moving snippet to directory)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        self.active_show.snippets_dir =
            Self::snippets_dir_for_active_show(&mut self.db_connection());
    }
    pub fn move_snippet_directory_to_directory(
        &mut self,
        directory_id: usize,
        new_parent_dir_id: usize,
    ) {
        self.db_connection()
            .query_drop(format!(
                "
                    UPDATE snippet_dirs
                    SET parent_dir_id = {new_parent_dir_id}
                    WHERE id = {directory_id};
                "
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Moving snippet directory to directory)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        self.active_show.snippets_dir =
            Self::snippets_dir_for_active_show(&mut self.db_connection());
    }
    #[allow(clippy::too_many_lines)]
    fn create_instruction(
        &mut self,
        snippet_id: Option<usize>,
        parent_instruction_id: Option<usize>,
        instruction: &Instruction,
        branch: Option<String>,
    ) {
        match instruction {
            Instruction::SetSpeedOfBlueprints(speed_of_blueprints) => {
                let inst_id = self.persist_instruction(
                    branch,
                    "SetSpeedOfBlueprints",
                    None,
                    None,
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
                self.persist_instruction_value(
                    speed_of_blueprints,
                    Some(inst_id),
                    None,
                    None,
                );
            }
            Instruction::PanTo(pt) => {
                let inst_id = self.persist_instruction(
                    branch,
                    "PanTo",
                    None,
                    None,
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
                self.persist_instruction_value(pt, Some(inst_id), None, None);
            }
            Instruction::TiltTo(tt) => {
                let inst_id = self.persist_instruction(
                    branch,
                    "TiltTo",
                    None,
                    None,
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
                self.persist_instruction_value(tt, Some(inst_id), None, None);
            }
            Instruction::ColorTo(ct) => {
                let inst_id = self.persist_instruction(
                    branch,
                    "ColorTo",
                    None,
                    Some(&ct.color.as_hex()),
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
                if let Some(fade_duration) = &ct.fade_duration {
                    self.persist_instruction_value(
                        fade_duration,
                        Some(inst_id),
                        None,
                        None,
                    );
                }
            }
            Instruction::BpmTo(bt) => {
                let inst_id = self.persist_instruction(
                    branch,
                    "BpmTo",
                    None,
                    None,
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
                self.persist_instruction_value(bt, Some(inst_id), None, None);
            }
            Instruction::ColorToRandom(fade_duration) => {
                let inst_id = self.persist_instruction(
                    branch,
                    "ColorToRandom",
                    None,
                    None,
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
                if let Some(fade_duration) = fade_duration {
                    self.persist_instruction_value(
                        fade_duration,
                        Some(inst_id),
                        None,
                        None,
                    );
                }
            }
            Instruction::ActivateAllFixtures => {
                self.persist_instruction(
                    branch,
                    "ActivateAllFixtures",
                    None,
                    None,
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
            }
            Instruction::StartRecording => {
                self.persist_instruction(
                    branch,
                    "StartRecording",
                    None,
                    None,
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
            }
            Instruction::StopRecording => {
                self.persist_instruction(
                    branch,
                    "StopRecording",
                    None,
                    None,
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
            }
            Instruction::ClearRecording => {
                self.persist_instruction(
                    branch,
                    "ClearRecording",
                    None,
                    None,
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
            }
            Instruction::ExecuteCallableSnippet(excs) => {
                self.persist_instruction(
                    branch,
                    "ExecuteCallableSnippet",
                    Some(*excs),
                    None,
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
            }
            Instruction::ActivateFixtureById(afn) => {
                self.persist_instruction(
                    branch,
                    "ActivateFixtureById",
                    Some(*afn),
                    None,
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
            }
            Instruction::ActivateFixtureGroup(afg) => {
                self.persist_instruction(
                    branch,
                    "ActivateFixtureGroup",
                    None,
                    Some(afg),
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
            }
            Instruction::BlueprintTo(bpt) => {
                #[allow(clippy::cast_possible_truncation)]
                self.persist_instruction(
                    branch,
                    "BlueprintTo",
                    Some(bpt.id),
                    None,
                    Some(bpt.oneshot),
                    Some(bpt.delay),
                    snippet_id,
                    parent_instruction_id,
                );
            }
            Instruction::ClearBlueprint(blueprint_id) => {
                self.persist_instruction(
                    branch,
                    "ClearBlueprint",
                    Some(*blueprint_id),
                    None,
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
            }
            Instruction::TimecodeTo(tct) => {
                #[allow(clippy::cast_possible_truncation)]
                self.persist_instruction(
                    branch,
                    "TimecodeTo",
                    Some(tct.id),
                    None,
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
            }
            Instruction::SetBlueprintPositionIndexOffsetMode(sbpiom) => {
                self.persist_instruction(
                    branch,
                    "SetBlueprintPositionIndexOffsetMode",
                    None,
                    Some(&sbpiom.to_string()),
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
            }
            Instruction::ToggleQueueMode(queue_mode) => {
                self.persist_instruction(
                    branch,
                    "ToggleQueueMode",
                    None,
                    Some(&(*queue_mode).to_string()),
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
            }
            Instruction::UnimplementedChannelTo(uct) => {
                let inst_id = self.persist_instruction(
                    branch,
                    "UnimplementedChannelTo",
                    None,
                    Some(&uct.name),
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
                self.persist_instruction_value(
                    &uct.value,
                    Some(inst_id),
                    None,
                    None,
                );
            }
            Instruction::AddToPan(atp) => {
                let inst_id = self.persist_instruction(
                    branch,
                    "AddToPan",
                    None,
                    None,
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
                self.persist_instruction_value(atp, Some(inst_id), None, None);
            }
            Instruction::AddToTilt(att) => {
                let inst_id = self.persist_instruction(
                    branch,
                    "AddToTilt",
                    None,
                    None,
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
                self.persist_instruction_value(att, Some(inst_id), None, None);
            }
            Instruction::PositionTo(pos) => {
                #[allow(clippy::cast_possible_truncation)]
                self.persist_instruction(
                    branch,
                    "PositionTo",
                    Some(*pos),
                    None,
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
            }
            Instruction::BpmModifierTo(bmt) => {
                let inst_id = self.persist_instruction(
                    branch,
                    "BpmModifierTo",
                    None,
                    None,
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
                self.persist_instruction_value(bmt, Some(inst_id), None, None);
            }
            Instruction::FixtureLoop(fx_loop) => {
                let inst_id = self.persist_instruction(
                    branch,
                    "FixtureLoop",
                    None,
                    None,
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
                for instruction in &fx_loop.fixtures {
                    self.create_instruction(
                        None,
                        Some(inst_id),
                        instruction,
                        Some("fixtures".to_owned()),
                    );
                }
                for instruction in &fx_loop.instructions {
                    self.create_instruction(
                        None,
                        Some(inst_id),
                        instruction,
                        Some("instructions".to_owned()),
                    );
                }
            }
            Instruction::CreateVariable(cv) => {
                self.persist_instruction(
                    branch,
                    "CreateVariable",
                    None,
                    Some(cv),
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
            }
            Instruction::SetVariable(sv) => {
                let inst_id = self.persist_instruction(
                    branch,
                    "SetVariable",
                    None,
                    Some(&sv.name),
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
                self.persist_instruction_value(
                    &sv.value,
                    Some(inst_id),
                    None,
                    None,
                );
            }
            Instruction::AddToGroup(atg) => {
                let inst_id = self.persist_instruction(
                    branch,
                    "AddToGroup",
                    None,
                    Some(&atg.name),
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
                for instruction in &atg.fixtures {
                    self.create_instruction(
                        None,
                        Some(inst_id),
                        instruction,
                        Some("fixtures".to_owned()),
                    );
                }
            }
            Instruction::RemoveFromGroup(rfg) => {
                let inst_id = self.persist_instruction(
                    branch,
                    "RemoveFromGroup",
                    None,
                    Some(&rfg.name),
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
                for instruction in &rfg.fixtures {
                    self.create_instruction(
                        None,
                        Some(inst_id),
                        instruction,
                        Some("fixtures".to_owned()),
                    );
                }
            }
            Instruction::DelayBy(dbe) => {
                let inst_id = self.persist_instruction(
                    branch,
                    "DelayBy",
                    None,
                    None,
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
                self.persist_instruction_value(dbe, Some(inst_id), None, None);
            }
            Instruction::DeselectAllFixtures => {
                self.persist_instruction(
                    branch,
                    "DeselectAllFixtures",
                    None,
                    None,
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
            }
            Instruction::DimmerTo(dt) => {
                let inst_id = self.persist_instruction(
                    branch,
                    "DimmerTo",
                    None,
                    None,
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
                self.persist_instruction_value(dt, Some(inst_id), None, None);
            }
            Instruction::IfStatement(is) => {
                let inst_id = self.persist_instruction(
                    branch,
                    "IfStatement",
                    None,
                    Some(&is.comparison.comparator.to_string()),
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
                self.persist_instruction_value(
                    &is.comparison.left_value,
                    Some(inst_id),
                    None,
                    Some("left_value"),
                );
                self.persist_instruction_value(
                    &is.comparison.right_value,
                    Some(inst_id),
                    None,
                    Some("right_value"),
                );
                for instruction in &is.truthy_branch {
                    self.create_instruction(
                        None,
                        Some(inst_id),
                        instruction,
                        Some("truthy_branch".to_owned()),
                    );
                }
                for instruction in &is.falsy_branch {
                    self.create_instruction(
                        None,
                        Some(inst_id),
                        instruction,
                        Some("falsy_branch".to_owned()),
                    );
                }
            }
            Instruction::LoopStatement(ls) => {
                let inst_id = self.persist_instruction(
                    branch,
                    "LoopStatement",
                    None,
                    Some(&ls.comparison.comparator.to_string()),
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
                self.persist_instruction_value(
                    &ls.comparison.left_value,
                    Some(inst_id),
                    None,
                    Some("left_value"),
                );
                self.persist_instruction_value(
                    &ls.comparison.right_value,
                    Some(inst_id),
                    None,
                    Some("right_value"),
                );
                for instruction in &ls.content {
                    self.create_instruction(
                        None,
                        Some(inst_id),
                        instruction,
                        Some("content".to_owned()),
                    );
                }
            }
            Instruction::IfQueueAllowsContinue(iqac) => {
                let inst_id = self.persist_instruction(
                    branch,
                    "IfQueueAllowsContinue",
                    None,
                    None,
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
                for instruction in iqac {
                    self.create_instruction(
                        None,
                        Some(inst_id),
                        instruction,
                        Some("content".to_owned()),
                    );
                }
            }
            Instruction::Nop => {
                self.persist_instruction(
                    branch,
                    "Nop",
                    None,
                    None,
                    None,
                    None,
                    snippet_id,
                    parent_instruction_id,
                );
            }
        }
    }
    #[allow(clippy::too_many_arguments)]
    fn persist_instruction(
        &self,
        branch: Option<String>,
        variant: &str,
        int_val: Option<usize>,
        str_val: Option<&String>,
        bool_val_1: Option<bool>,
        bool_val_2: Option<bool>,
        snippet_id: Option<usize>,
        parent_instruction_id: Option<usize>,
    ) -> usize {
        self.db_connection()
            .query_drop(format!(
                "
                        INSERT INTO instructions
                        (
                            branch,
                            variant,
                            int_val,
                            str_val,
                            bool_val_1,
                            bool_val_2,
                            {}
                        )
                        VALUES
                        (
                            '{}',
                            '{variant}',
                            {},
                            '{}',
                            {},
                            {},
                            {}
                        )
                    ",
                if snippet_id.is_some() {
                    "snippet_id"
                } else {
                    "parent_instruction_id"
                },
                branch.unwrap_or_default(),
                int_val.unwrap_or_default(),
                str_val.unwrap_or(&String::new()),
                bool_val_1.unwrap_or_default(),
                bool_val_2.unwrap_or_default(),
                snippet_id.unwrap_or_else(
                    || parent_instruction_id.unwrap_or_default()
                )
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Creating new instruction)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        let mut instruction_id = 0;
        self.db_connection()
                    .query_map(
                        "
                    SELECT MAX(id) FROM instructions

                    ",
                        |id| instruction_id = id,
                    )
                    .unwrap_or_else(|err| {
                        logging::log(
                            format!(
                        "{err:?}\n        (Selecting the just-created instruction)"
                    ),
                            logging::LogLevel::DbError,
                            true,
                        );
                        vec![]
                    });
        instruction_id
    }
    #[allow(clippy::too_many_arguments)]
    fn create_instruction_value(
        &self,
        variant: &str,
        branch: Option<&str>,
        int_val: Option<usize>,
        str_val: Option<&String>,
        bool_val: Option<bool>,
        instruction_id: Option<usize>,
        instruction_value_id: Option<usize>,
    ) -> usize {
        self.db_connection()
            .query_drop(format!(
                "
                INSERT INTO instruction_values
                (
                    variant,
                    branch,
                    int_val,
                    str_val,
                    bool_val,
                    {}
                )
                VALUES
                (
                    '{variant}',
                    '{}',
                    {},
                    '{}',
                    {},
                    {}
                )
            ",
                if instruction_id.is_some() {
                    "instruction_id"
                } else {
                    "instruction_value_id"
                },
                branch.unwrap_or_default(),
                int_val.unwrap_or_default(),
                str_val.unwrap_or(&String::new()),
                bool_val.unwrap_or(false),
                instruction_id
                    .unwrap_or_else(|| instruction_value_id.unwrap_or_default())
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!(
                        "{err:?}\n        (Creating new instruction_value)"
                    ),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        let mut instruction_value_id = 0;
        self.db_connection()
                    .query_map(
                        "
                    SELECT MAX(id) FROM instruction_values

                    ",
                        |id| instruction_value_id = id,
                    )
                    .unwrap_or_else(|err| {
                        logging::log(
                            format!(
                        "{err:?}\n        (Selecting the just-created instruction_value)"
                    ),
                            logging::LogLevel::DbError,
                            true,
                        );
                        vec![]
                    });
        instruction_value_id
    }
    #[allow(clippy::too_many_lines)]
    fn persist_instruction_value(
        &mut self,
        instruction_value: &InstructionValue,
        instruction_id: Option<usize>,
        instruction_value_id: Option<usize>,
        branch: Option<&str>,
    ) {
        match instruction_value {
            InstructionValue::Number(NumberValue {
                value,
                dmx_precision,
            }) => {
                self.create_instruction_value(
                    "Number",
                    branch,
                    Some((*value).into()),
                    None,
                    Some(*dmx_precision),
                    instruction_id,
                    instruction_value_id,
                );
            }
            InstructionValue::Variable(str) => {
                self.create_instruction_value(
                    "Variable",
                    branch,
                    None,
                    Some(str),
                    None,
                    instruction_id,
                    instruction_value_id,
                );
            }
            InstructionValue::Function(fn_get) => {
                self.create_instruction_value(
                    "Function",
                    branch,
                    Some(fn_get.fixture_id),
                    Some(&fn_get.function),
                    None,
                    instruction_id,
                    instruction_value_id,
                );
            }
            InstructionValue::Value => {
                self.create_instruction_value(
                    "Value",
                    branch,
                    None,
                    None,
                    None,
                    instruction_id,
                    instruction_value_id,
                );
            }
            InstructionValue::Pressed => {
                self.create_instruction_value(
                    "Pressed",
                    branch,
                    None,
                    None,
                    None,
                    instruction_id,
                    instruction_value_id,
                );
            }
            InstructionValue::Released => {
                self.create_instruction_value(
                    "Released",
                    branch,
                    None,
                    None,
                    None,
                    instruction_id,
                    instruction_value_id,
                );
            }
            InstructionValue::Default => {
                self.create_instruction_value(
                    "Default",
                    branch,
                    None,
                    None,
                    None,
                    instruction_id,
                    instruction_value_id,
                );
            }
            InstructionValue::Bpm => {
                self.create_instruction_value(
                    "Bpm",
                    branch,
                    None,
                    None,
                    None,
                    instruction_id,
                    instruction_value_id,
                );
            }
            InstructionValue::Keypoint(kp_id) => {
                self.create_instruction_value(
                    "Keypoint",
                    branch,
                    Some(*kp_id),
                    None,
                    None,
                    instruction_id,
                    instruction_value_id,
                );
            }
            InstructionValue::BpmModifier => {
                self.create_instruction_value(
                    "BpmModifier",
                    branch,
                    None,
                    None,
                    None,
                    instruction_id,
                    instruction_value_id,
                );
            }
            InstructionValue::MathOperator(mo) => {
                let instruction_value_id: usize = self
                    .create_instruction_value(
                        "MathOperator",
                        branch,
                        None,
                        Some(&format!("{:?}", mo.operand)),
                        None,
                        instruction_id,
                        instruction_value_id,
                    );
                self.persist_instruction_value(
                    &mo.left_value,
                    None,
                    Some(instruction_value_id),
                    Some("left_value"),
                );
                self.persist_instruction_value(
                    &mo.right_value,
                    None,
                    Some(instruction_value_id),
                    Some("right_value"),
                );
            }
            InstructionValue::Random(rand) => {
                let instruction_value_id: usize = self
                    .create_instruction_value(
                        "Random",
                        branch,
                        None,
                        None,
                        None,
                        instruction_id,
                        instruction_value_id,
                    );
                self.persist_instruction_value(
                    &rand.0,
                    None,
                    Some(instruction_value_id),
                    Some("left_value"),
                );
                self.persist_instruction_value(
                    &rand.1,
                    None,
                    Some(instruction_value_id),
                    Some("right_value"),
                );
            }
        }
    }
}
