#![no_std]
#![feature(abi_avr_interrupt)]
#![feature(cell_update)]

extern crate alloc;

#[global_allocator]
pub static HEAP: Heap = Heap::empty();
pub const HEAP_SIZE: usize = 4096;
pub static mut HEAP_MEM: [MaybeUninit<u8>; HEAP_SIZE] = [MaybeUninit::uninit(); HEAP_SIZE];

use avr_device::interrupt::{self, Mutex};
use core::cell::Cell;
use core::mem::MaybeUninit;
use embedded_alloc::Heap;

pub mod panic_handler;
pub mod sendable_bytes_queue;

pub static IDENTIFIED: Mutex<Cell<bool>> = Mutex::new(Cell::new(false));
pub static ACKNOWLEDGED: Mutex<Cell<bool>> = Mutex::new(Cell::new(true));
/// If this is
/// 0, the incomming byte should be 122,
/// 1, the incomming byte should be 127,
/// 2, the incomming byte should be 115,
/// 3, the incomming byte should be the package type
static RECV_INDEX: Mutex<Cell<u8>> = Mutex::new(Cell::new(0));

#[avr_device::interrupt(atmega2560)]
fn USART0_RX() {
    unsafe {
        let dp = arduino_hal::Peripherals::steal();
        let pins = arduino_hal::pins!(dp);

        let mut serial = arduino_hal::default_serial!(dp, pins, 250_000);
        #[cfg(debug_assertions)]
        let mut debug_serial = arduino_hal::Usart::new(
            dp.USART3,
            pins.d15,
            pins.d14.into_output(),
            arduino_hal::hal::usart::BaudrateArduinoExt::into_baudrate(115_200),
        );

        let byte = serial.read_byte();
        #[cfg(debug_assertions)]
        {
            debug_serial.write_byte(114);
            debug_serial.write_byte(95);
            debug_serial.write_byte(byte.saturating_add(48));
            debug_serial.write_byte(10);
            debug_serial.write_byte(13);
        }
        let mut package_type = None;
        #[allow(clippy::bool_to_int_with_if)]
        interrupt::free(|cs| {
            RECV_INDEX.borrow(cs).update(|recv_index| match recv_index {
                0 => {
                    if byte == 122 {
                        #[cfg(debug_assertions)]
                        {
                            debug_serial.write_byte(98);
                            debug_serial.write_byte(95);
                            debug_serial.write_byte(49);
                            debug_serial.write_byte(10);
                            debug_serial.write_byte(13);
                        }
                        1
                    } else {
                        0
                    }
                }
                1 => {
                    if byte == 127 {
                        #[cfg(debug_assertions)]
                        {
                            debug_serial.write_byte(98);
                            debug_serial.write_byte(95);
                            debug_serial.write_byte(50);
                            debug_serial.write_byte(10);
                            debug_serial.write_byte(13);
                        }
                        2
                    } else if byte == 122 {
                        1
                    } else {
                        0
                    }
                }
                2 => {
                    if byte == 115 {
                        #[cfg(debug_assertions)]
                        {
                            debug_serial.write_byte(98);
                            debug_serial.write_byte(95);
                            debug_serial.write_byte(51);
                            debug_serial.write_byte(10);
                            debug_serial.write_byte(13);
                        }
                        3
                    } else if byte == 122 {
                        1
                    } else {
                        0
                    }
                }
                3 => {
                    if byte == 122 {
                        1
                    } else {
                        #[cfg(debug_assertions)]
                        {
                            debug_serial.write_byte(98);
                            debug_serial.write_byte(95);
                            debug_serial.write_byte(52);
                            debug_serial.write_byte(10);
                            debug_serial.write_byte(13);

                            debug_serial.write_byte(112);
                            debug_serial.write_byte(95);
                            debug_serial.write_byte(byte.saturating_add(48));
                            debug_serial.write_byte(10);
                            debug_serial.write_byte(13);
                        }
                        package_type = Some(byte);
                        0
                    }
                }
                _ => 0,
            });
        });

        #[allow(clippy::collapsible_if)]
        if let Some(package_type) = package_type {
            interrupt::free(|cs| {
                if package_type == 1 {
                    serial.write_byte(122);
                    serial.write_byte(127);
                    serial.write_byte(115);
                    serial.write_byte(0);
                    #[cfg(feature = "tomahawk_1")]
                    serial.write_byte(1);
                    #[cfg(feature = "tomahawk_2")]
                    serial.write_byte(2);
                    IDENTIFIED.borrow(cs).set(true);
                } else if package_type == 5 {
                    #[cfg(debug_assertions)]
                    {
                        debug_serial.write_byte(97);
                        debug_serial.write_byte(99);
                        debug_serial.write_byte(107);
                        debug_serial.write_byte(10);
                        debug_serial.write_byte(13);
                    }
                    ACKNOWLEDGED.borrow(cs).set(true);
                } else if package_type == 6 {
                    if IDENTIFIED.borrow(cs).get() {
                        #[cfg(debug_assertions)]
                        {
                            debug_serial.write_byte(97);
                            debug_serial.write_byte(108);
                            debug_serial.write_byte(105);
                            debug_serial.write_byte(118);
                            debug_serial.write_byte(101);
                            debug_serial.write_byte(10);
                            debug_serial.write_byte(13);
                        }

                        serial.write_byte(122);
                        serial.write_byte(127);
                        serial.write_byte(115);
                        serial.write_byte(7);
                    }
                }
            });
        }
    }
}
