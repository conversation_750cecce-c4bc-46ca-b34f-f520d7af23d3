<script lang="ts" module>
    export function getAllOtherXCoordinates(
        track: TrackType,
        selectedIndex: number,
    ): number[] {
        const allOtherXCoordinates: number[] = [];
        for (let i = 0; i < track.properties.length; i++) {
            if (i !== selectedIndex) {
                const property = track.properties[i];
                if ("ColorPropertyCoordinates" in property) {
                    for (let coordinate of property
                        .ColorPropertyCoordinates[0]) {
                        allOtherXCoordinates.push(
                            coordinate.x + property.ColorPropertyCoordinates[1],
                        );
                    }
                } else if ("UnimplementedChannel" in property) {
                    for (let coordinate of property.UnimplementedChannel[1]) {
                        allOtherXCoordinates.push(
                            coordinate.x + property.UnimplementedChannel[2],
                        );
                    }
                } else if ("CallSnippet" in property) {
                    allOtherXCoordinates.push(property.CallSnippet[1]);
                    allOtherXCoordinates.push(
                        property.CallSnippet[1] + PROPERTY_MIN_WIDTH,
                    );
                }
            }
        }
        allOtherXCoordinates.sort((a, b) => (a < b ? -1 : 1));
        return allOtherXCoordinates;
    }

    export function getAllOtherPropertyRanges(
        track: TrackType,
        selectedIndex: number,
    ): [number, number][] {
        const allOtherPropertyRanges: [number, number][] = [];
        for (let i = 0; i < track.properties.length; i++) {
            if (i !== selectedIndex) {
                const property = track.properties[i];
                if ("ColorPropertyCoordinates" in property) {
                    allOtherPropertyRanges.push([
                        property.ColorPropertyCoordinates[0][0].x +
                            property.ColorPropertyCoordinates[1],
                        property.ColorPropertyCoordinates[0][
                            property.ColorPropertyCoordinates[0].length - 1
                        ].x + property.ColorPropertyCoordinates[1],
                    ]);
                } else if ("UnimplementedChannel" in property) {
                    allOtherPropertyRanges.push([
                        property.UnimplementedChannel[1][0].x +
                            property.UnimplementedChannel[2],
                        property.UnimplementedChannel[1][
                            property.UnimplementedChannel[1].length - 1
                        ].x + property.UnimplementedChannel[2],
                    ]);
                } else if ("CallSnippet" in property) {
                    allOtherPropertyRanges.push([
                        property.CallSnippet[1],
                        property.CallSnippet[1] + PROPERTY_MIN_WIDTH,
                    ]);
                }
            }
        }
        allOtherPropertyRanges.sort((a, b) => (a[0] < b[0] ? -1 : 1));
        return allOtherPropertyRanges;
    }
    export function getSelectedPropertyBounds(
        allOtherXCoordinates: number[],
        property: TimedPropertyFileDescriptor,
    ): {
        lowerOuterBound: number | undefined;
        upperOuterBound: number | undefined;
    } {
        const lowerOuterBound = allOtherXCoordinates.findLast((x) => {
            if ("ColorPropertyCoordinates" in property) {
                return x <= property.ColorPropertyCoordinates[1];
            } else if ("UnimplementedChannel" in property) {
                return x <= property.UnimplementedChannel[2];
            } else if ("CallSnippet" in property) {
                return x <= property.CallSnippet[1];
            }
        });

        const upperOuterBound = allOtherXCoordinates.find((x) => {
            if ("ColorPropertyCoordinates" in property) {
                return (
                    x >=
                    property.ColorPropertyCoordinates[1] +
                        property.ColorPropertyCoordinates[0][
                            property.ColorPropertyCoordinates[0].length - 1
                        ].x
                );
            } else if ("UnimplementedChannel" in property) {
                return (
                    x >=
                    property.UnimplementedChannel[2] +
                        property.UnimplementedChannel[1][
                            property.UnimplementedChannel[1].length - 1
                        ].x
                );
            } else if ("CallSnippet" in property) {
                return x >= property.CallSnippet[1] + PROPERTY_MIN_WIDTH;
            }
        });
        return {
            lowerOuterBound,
            upperOuterBound,
        };
    }

    export function calculateMaxPropertyWidth(
        allOtherXCoordinates: number[],
        property: TimedPropertyFileDescriptor,
    ): number {
        const upperOuterBound = allOtherXCoordinates.find((x) => {
            if ("ColorPropertyCoordinates" in property) {
                return (
                    x >=
                    property.ColorPropertyCoordinates[1] +
                        property.ColorPropertyCoordinates[0][
                            property.ColorPropertyCoordinates[0].length - 1
                        ].x
                );
            } else if ("UnimplementedChannel" in property) {
                return (
                    x >=
                    property.UnimplementedChannel[2] +
                        property.UnimplementedChannel[1][
                            property.UnimplementedChannel[1].length - 1
                        ].x
                );
            } else if ("CallSnippet" in property) {
                return x >= property.CallSnippet[1] + PROPERTY_MIN_WIDTH;
            }
        });

        console.log(upperOuterBound);

        if (upperOuterBound !== undefined) {
            if ("ColorPropertyCoordinates" in property) {
                return upperOuterBound - property.ColorPropertyCoordinates[1];
            } else if ("UnimplementedChannel" in property) {
                return upperOuterBound - property.UnimplementedChannel[2];
            } else if ("CallSnippet" in property) {
                return upperOuterBound - property.CallSnippet[1];
            }
        }
        return 65_535;
    }

    export function applyMousemovementToProperty(
        mouseMovementX: number,
        bounds: {
            lowerOuterBound: number | undefined;
            upperOuterBound: number | undefined;
        },
        property: TimedPropertyFileDescriptor,
    ) {
        if ("ColorPropertyCoordinates" in property) {
            const newPosition = Math.max(
                property.ColorPropertyCoordinates[1] + mouseMovementX,
                bounds.lowerOuterBound ?? 0,
            );

            if (!bounds.upperOuterBound) {
                property.ColorPropertyCoordinates[1] = newPosition;
            } else if (
                newPosition +
                    property.ColorPropertyCoordinates[0][
                        property.ColorPropertyCoordinates[0].length - 1
                    ].x <
                bounds.upperOuterBound
            ) {
                property.ColorPropertyCoordinates[1] = newPosition;
            }
        } else if ("UnimplementedChannel" in property) {
            const newPosition = Math.max(
                property.UnimplementedChannel[2] + mouseMovementX,
                bounds.lowerOuterBound ?? 0,
            );

            if (!bounds.upperOuterBound) {
                property.UnimplementedChannel[2] = newPosition;
            } else if (
                newPosition +
                    property.UnimplementedChannel[1][
                        property.UnimplementedChannel[1].length - 1
                    ].x <
                bounds.upperOuterBound
            ) {
                property.UnimplementedChannel[2] = newPosition;
            }
        } else if ("CallSnippet" in property) {
            const newPosition = Math.max(
                property.CallSnippet[1] + mouseMovementX,
                bounds.lowerOuterBound ?? 0,
            );

            if (!bounds.upperOuterBound) {
                property.CallSnippet[1] = newPosition;
            } else if (
                newPosition + PROPERTY_MIN_WIDTH <
                bounds.upperOuterBound
            ) {
                property.CallSnippet[1] = newPosition;
            }
        }
    }
</script>

<script lang="ts">
    import { networking } from "$lib/stores/networking";
    import AudiofileManager from "./partials/audiofileManager.svelte";
    import type { InstructionsWithValue } from "$lib/types/bindings/InstructionsWithValue";
    import Button from "$lib/atoms/button.svelte";
    import Icon from "$lib/atoms/icon.svelte";
    import { timecodes } from "$lib/stores/timecodes";
    import Track, { PROPERTY_MIN_WIDTH } from "./partials/track.svelte";
    import type { Track as TrackType } from "$lib/types/bindings/Track";
    import { fixtures } from "$lib/stores/fixtures";
    import LabeledTextinput from "$lib/molecules/labeled_textinput.svelte";
    import { TOAST } from "$lib/stores/toast";
    import {
        allLeafesOf,
        appendSnippetEditingReason,
    } from "$lib/stores/snippets";
    import SearchableDropdown from "$lib/molecules/searchable_dropdown.svelte";
    import Textinput from "$lib/atoms/textinput.svelte";
    import TemporaryPropertySelect from "./partials/temporaryPropertySelect.svelte";
    import type { SvelteComponent } from "svelte";
    import Wavesurfer from "$lib/atoms/wavesurfer.svelte";
    import { get } from "svelte/store";
    import type { TimedPropertyFileDescriptor } from "$lib/types/bindings/TimedPropertyFileDescriptor";
    import { createDefaultTimedPropertyBasedOn } from "$lib/utils";
    import LabeledNumberinput from "$lib/molecules/labeled_numberinput.svelte";

    let wavesurfer: SvelteComponent | undefined = $state();
    let patchResult = $state(false);
    $effect(() => {
        if (patchResult) {
            setTimeout(() => (patchResult = false), 1500);
        }
    });
    let trackDimensions: { scrollLeft: number; width: number } = $state({
        scrollLeft: 0,
        width: 0,
    });
    let timelineIndex: number = $state(0);
    let timelineCursor: { start: number; current: number } = $state({
        start: 0,
        current: 0,
    });

    let selectedTimecodeId: number | null = $state(null);
    let selectedTimecode = $derived.by(() => {
        return $timecodes?.find(
            (timecode) => timecode.id === selectedTimecodeId,
        );
    });

    async function playBackendTimecode() {
        console.log(timelineCursor.current);
        if (selectedTimecode !== undefined) {
            let instructions_with_value: InstructionsWithValue = {
                instructions: [
                    {
                        instruction: "FixtureLoop",
                        instructionMod: {
                            fixtures: [
                                {
                                    instruction: "ActivateAllFixtures",
                                },
                            ],
                            instructions: [
                                {
                                    instruction: "TimecodeTo",
                                    instructionMod: {
                                        id: selectedTimecode.id,
                                        index: timelineCursor.current,
                                        play: true,
                                    },
                                },
                            ],
                        },
                    },
                ],
                value: 0,
            };
            fetch(
                `http://${get(networking)}:${networking.port}/one_time_instructions`,
                {
                    method: "PUT",
                    body: JSON.stringify(instructions_with_value),
                    headers: new Headers({
                        "content-type": "application/json",
                    }),
                },
            );
        }
    }

    async function pauseBackendTimecode() {
        if (selectedTimecodeId !== null) {
            let instructions_with_value: InstructionsWithValue = {
                instructions: [
                    {
                        instruction: "FixtureLoop",
                        instructionMod: {
                            fixtures: [
                                {
                                    instruction: "ActivateAllFixtures",
                                },
                            ],
                            instructions: [
                                {
                                    instruction: "TimecodeTo",
                                    instructionMod: {
                                        id: selectedTimecodeId,
                                        index: timelineCursor.current,
                                        play: false,
                                    },
                                },
                            ],
                        },
                    },
                ],
                value: 0,
            };
            fetch(
                `http://${get(networking)}:${networking.port}/one_time_instructions`,
                {
                    method: "PUT",
                    body: JSON.stringify(instructions_with_value),
                    headers: new Headers({
                        "content-type": "application/json",
                    }),
                },
            );
        }
    }

    const possible_fixture_properties = $derived.by(() => {
        let result = [
            ...new Set([
                "ColorTransition",
                ...$fixtures
                    .map((fixture) => fixture.unimplemented_channels)
                    .flat()
                    .map((unim_channel) => unim_channel.name),
            ]),
        ];

        result.sort();
        return result;
    });

    function enoughSpaceForProperty(
        track: TrackType,
        property: TimedPropertyFileDescriptor,
        xOffset: number,
    ): boolean {
        const allOtherXCoordinates = getAllOtherXCoordinates(track, xOffset);
        const allOtherPropertyRanges = getAllOtherPropertyRanges(
            track,
            xOffset,
        );
        if (
            allOtherPropertyRanges.find(
                (range) => xOffset >= range[0] && xOffset <= range[1],
            )
        ) {
            return false;
        }
        const upperOuterBound = allOtherXCoordinates.find(
            (otherX) => otherX >= xOffset,
        );
        if (upperOuterBound) {
            if ("ColorPropertyCoordinates" in property) {
                return (
                    upperOuterBound >
                    property.ColorPropertyCoordinates[0][
                        property.ColorPropertyCoordinates[0].length - 1
                    ].x +
                        property.ColorPropertyCoordinates[1]
                );
            } else if ("UnimplementedChannel" in property) {
                return (
                    upperOuterBound >
                    property.UnimplementedChannel[1][
                        property.UnimplementedChannel[1].length - 1
                    ].x +
                        property.UnimplementedChannel[2]
                );
            } else if ("CallSnippet" in property) {
                return upperOuterBound > PROPERTY_MIN_WIDTH;
            }
        }
        return true;
    }

    async function addPropertyToTrack(
        track: TrackType,
        property: TimedPropertyFileDescriptor,
        updateXOffsetTo?: number,
    ): Promise<any> {
        if (!selectedTimecode) return Promise.reject();

        let copiedProperty = JSON.parse(JSON.stringify(property));

        if (updateXOffsetTo !== undefined) {
            if ("ColorPropertyCoordinates" in property) {
                copiedProperty.ColorPropertyCoordinates[1] = updateXOffsetTo;
            } else if ("CallSnippet" in property) {
                copiedProperty.CallSnippet[1] = updateXOffsetTo;
            } else {
                copiedProperty.UnimplementedChannel[2] = updateXOffsetTo;
            }
        }

        if (
            !enoughSpaceForProperty(track, copiedProperty, updateXOffsetTo ?? 0)
        ) {
            TOAST.warning("Not enough space for property");
            return Promise.reject();
        }

        track.properties.push(copiedProperty);

        return timecodes.patchOne(selectedTimecode).then((result) => {
            patchResult = result;
        });
    }

    function removePropertyFromTrack(track: TrackType, propertyIndex: number) {
        if (!selectedTimecode) return;

        track.properties = track.properties.filter(
            (_e, i, _arr) => i !== propertyIndex,
        );

        timecodes.patchOne(selectedTimecode).then((result) => {
            patchResult = result;
        });
    }

    function playAudio() {
        if (wavesurfer) {
            wavesurfer.play();
        }
    }
    function pauseAudio() {
        if (wavesurfer) {
            wavesurfer.pause();
        }
    }
    function stopAudio() {
        if (wavesurfer) {
            wavesurfer.stop();
        }
    }
</script>

<div class="flex flex-col space-x-4 rounded-lg bg-object p-4">
    <div class="flex w-1/2 space-x-4">
        <div class="flex flex-col">
            <p>Select Timecode</p>
            <SearchableDropdown
                id="select-timecode-dropdown"
                bind:value={selectedTimecodeId}
                items={$timecodes}
            ></SearchableDropdown>
        </div>
        <Button
                    id="save-timecode"
                    onclick={() => {
                if (selectedTimecode) {
                    selectedTimecode.requires_user_action_reason = null;
                    timecodes
                        .patchOne(selectedTimecode)
                        .then((result) => (patchResult = result));
                }
            }}
            disabled={selectedTimecodeId === undefined}
        >
            <div class="text-2xl">
                {#if patchResult}
                    <div class="absolute text-green-500">
                        <Icon icon="material-symbols:save"></Icon>
                    </div>
                    <div
                        class="fixed animate-ping-once text-green-500"
                        class:text-success={patchResult}
                    >
                        <Icon icon="material-symbols:save"></Icon>
                    </div>
                {:else}
                    <Icon icon="material-symbols:save"></Icon>
                {/if}
            </div>
        </Button>
        <Button
                    id="delete-timecode"
                    onclick={() => {
                if (selectedTimecode) {
                    appendSnippetEditingReason(
                        (snippet) =>
                            snippet.instructions
                                .flatMap((instruction) =>
                                    allLeafesOf(instruction),
                                )
                                .some((instruction) => {
                                    if ("instruction" in instruction) {
                                        if (
                                            "TimecodeTo" ===
                                            instruction.instruction
                                        ) {
                                            return (
                                                instruction.instructionMod
                                                    .id === selectedTimecode.id
                                            );
                                        }
                                    }
                                    return false;
                                }),
                        `deleted timecode ${selectedTimecode.name}`,
                    );
                    timecodes.deleteOne(selectedTimecode);
                }
            }}
            disabled={selectedTimecodeId === null}
        >
            <Icon icon="ri:subtract-fill"></Icon>
        </Button>
        <Button
                    id="create-timecode"
                    onclick={async () => {
                await timecodes.createOne();
                selectedTimecodeId = $timecodes.length + 1;
            }}
        >
            <Icon icon="mdi:add"></Icon>
        </Button>
    </div>
    {#if selectedTimecode}
        <div class="flex rounded-lg bg-object p-4 w-full">
            <LabeledTextinput
                label="Rename here"
                bind:value={selectedTimecode.name}
                onchange={async () => {
                    await timecodes.patchOne(selectedTimecode);
                    patchResult = true;
                }}
            ></LabeledTextinput>
            <div class="flex justify-center w-full">
                <div class="w-16">
                    <Button
                    id="play-audio"
                    onclick={() => {
                            if (selectedTimecode) {
                                playAudio();
                            }
                        }}
                    >
                        <Icon icon="mdi:play"></Icon>
                    </Button>
                </div>
                <div class="w-16">
                    <Button
                    id="pause-audio"
                    onclick={() => {
                            if (selectedTimecode) {
                                pauseAudio();
                            }
                        }}
                    >
                        <Icon icon="mdi:pause"></Icon>
                    </Button>
                </div>
                <div class="w-16">
                    <Button
                    id="stop-audio"
                    onclick={() => {
                            if (selectedTimecode) {
                                stopAudio();
                            }
                        }}
                    >
                        <Icon icon="mdi:stop"></Icon>
                    </Button>
                </div>
            </div>
        </div>
    {/if}

    {#if selectedTimecode}
        <div class="w-[250px]">
            <Button
                    id="add-track"
                    onclick={async () => {
                    selectedTimecode.tracks = [
                        ...selectedTimecode.tracks,
                        {
                            id: 0,
                            name: "new track",
                            requires_user_action_reason: "new",
                            properties: [],
                        },
                    ];
                    await timecodes.patchOne(selectedTimecode);
                    patchResult = true;
                }}>Add track</Button
            >
            <Button
                    id="remove-track"
                    onclick={async () => {
                    // Confirm action
                    if (
                        selectedTimecode.tracks[
                            selectedTimecode.tracks.length - 1
                        ].properties.length > 0
                    ) {
                        if (!confirm(
                            "do you realy want to remove the last track? It still has properties.",
                        )) {
                            return; // Don't proceed if user cancels
                        }
                    }
                    selectedTimecode.tracks = [
                        ...selectedTimecode.tracks.slice(
                            0,
                            selectedTimecode.tracks.length - 2,
                        ),
                    ];
                    timecodes.patchOne(selectedTimecode);
                    patchResult = true;
                }}>remove last track</Button
            >
        </div>
    {/if}
    {#if selectedTimecode && selectedTimecode.audiofile_id !== null && trackDimensions.width !== 0}
        {#each selectedTimecode.tracks as track, i}
            <div class="flex my-1">
                {#if track}
                    <div class="min-w-[250px]">
                        <Textinput
                            bind:value={track.name}
                            onchange={async () => {
                                await timecodes.patchOne(selectedTimecode);
                                patchResult = true;
                            }}
                        ></Textinput>
                        <TemporaryPropertySelect
                            possibleProperties={possible_fixture_properties}
                            addPropertyToTrack={(propertyName) =>
                                addPropertyToTrack(
                                    track,
                                    createDefaultTimedPropertyBasedOn(
                                        propertyName,
                                        trackDimensions.scrollLeft,
                                    ),
                                    trackDimensions.scrollLeft,
                                )}
                        ></TemporaryPropertySelect>
                    </div>
                {/if}
                <div class="overflow-x-scroll no-scrollbar trackWrapper">
                    <div style={`min-width: ${trackDimensions.width - 5}px;`}>
                        <Track
                            {track}
                            removePropertyFromTrack={(index) =>
                                removePropertyFromTrack(track, index)}
                            addPropertyToTrack={(property, xOffset) =>
                                addPropertyToTrack(track, property, xOffset)}

                            bpm={selectedTimecode.bpm ?? undefined}
                            width={trackDimensions.width}
                            onchange={() => {
                                return new Promise((resolve, _) => {
                                    if (selectedTimecode) {
                                        selectedTimecode.requires_user_action_reason =
                                            null;
                                        resolve(
                                            timecodes
                                                .patchOne(selectedTimecode)
                                                .then((result) => {
                                                    patchResult = result;
                                                }),
                                        );
                                    }
                                });
                            }}
                            oninput={(item) => {
                                if (selectedTimecode) {
                                    if ("track" in item) {
                                        let timecodeIndex =
                                            $timecodes.findIndex(
                                                (timecode) =>
                                                    timecode.id ===
                                                    selectedTimecodeId,
                                            );
                                        $timecodes[timecodeIndex].tracks[i] =
                                            item.track;
                                        $timecodes = $timecodes;
                                    } else {
                                        let timecodeIndex =
                                            $timecodes.findIndex(
                                                (timecode) =>
                                                    timecode.id ===
                                                    selectedTimecodeId,
                                            );

                                        $timecodes[timecodeIndex].tracks[
                                            i
                                        ].properties[item.propertyIndex] =
                                            item.property;
                                        $timecodes = $timecodes;
                                    }
                                }
                            }}
                        />
                    </div>
                </div>
            </div>
        {/each}
    {/if}

    {#if selectedTimecode}
        {#key selectedTimecode.audiofile_id}
            {#if selectedTimecode.audiofile_id}
                <Wavesurfer
                    bind:editorWidth={trackDimensions.width}
                    bind:timelineIndex={timelineCursor.current}
                    bind:this={wavesurfer}
                    setTimecodeToIndexPaused={() => {
                        pauseBackendTimecode();
                    }}
                    setTimecodeToIndexPlaying={() => {
                        playBackendTimecode();
                    }}
                    trackUrl={`http://${get(networking)}:${networking.port}/audiofile/${selectedTimecode.audiofile_id}`}
                    updateScroll={(newScroll) => {
                        let trackWrappers =
                            document.getElementsByClassName("trackWrapper");
                        for (let i = 0; i < trackWrappers.length; i++) {
                            trackWrappers[i].scrollLeft = newScroll;
                            trackDimensions.scrollLeft = newScroll;
                        }
                    }}
                />
            {/if}
        {/key}
    {/if}

    {#if selectedTimecodeId !== null && selectedTimecode}
        <div class="mt-10">
            {#if selectedTimecode.bpm}
                <LabeledNumberinput
                    label="BPM"
                    bind:value={selectedTimecode.bpm}
                    onchange={async () => {
                        await timecodes.patchOne(selectedTimecode);
                        patchResult = true;
                    }}
                ></LabeledNumberinput>
            {/if}
            <div class="flex items-center justify-center bg-object">
                <AudiofileManager
                    bind:selected_audiofile={selectedTimecode.audiofile_id}
                />
            </div>
        </div>
    {/if}
</div>
