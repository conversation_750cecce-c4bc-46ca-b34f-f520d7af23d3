<script lang="ts">
    import Button from "$lib/atoms/button.svelte";
    import LabeledTextinput from "$lib/molecules/labeled_textinput.svelte";
    import { shows } from "$lib/stores/shows";
    import type { Show } from "$lib/types/bindings/Show";
    import Icon from "$lib/atoms/icon.svelte";
    import { TOAST } from "$lib/stores/toast";

    let newShowName: string = $state("");
    let showToUpload: Show | null = $state(null);

    async function createNewShow() {
        if (newShowName && !$shows.find((show) => show[1] == newShowName)) {
            shows.create(newShowName);
        } else {
            TOAST.warning("Showname cant be empty!");
        }
        newShowName = "";
    }

    async function uploadShow() {
        if (newShowName.length === 0) {
            TOAST.warning("Showname cant be empty!");
            return;
        }
        if ($shows.find((show) => show[1] == newShowName)) {
            TOAST.warning("Showname already exists!");
            return;
        }
        if (showToUpload === null) {
            TOAST.warning("No file to upload!");
            return;
        }
        showToUpload.name = newShowName;
        shows.upload(showToUpload);
        showToUpload = null;
        newShowName = "";
    }

    async function deleteShow(id: number, name: string) {
        if (
            prompt("Type the full name of show " + name + " to delete.") ===
            name
        ) {
            shows.delete(id);
        }
    }

    async function downloadShow(id: number) {
        let show = await shows.getShow(id);
        var dataStr =
            "data:text/json;charset=utf-8," +
            encodeURIComponent(JSON.stringify(show));
        var dlAnchorElem = document.createElement("a");
        dlAnchorElem.setAttribute("href", dataStr);
        dlAnchorElem.setAttribute("download", `${show.name}.json`);
        dlAnchorElem.click();
    }

    async function renameShow(id: number, old_name: string) {
        if (old_name.length > 0) {
            let new_name = prompt(`${old_name} should be renamed to`);
            if (new_name && new_name.length > 0) {
                shows.rename(id, new_name);
            }
        }
    }

    async function prepareFile(e: Event) {
        // @ts-ignore
        let systemShowPath = e.target.files[0].name;
        newShowName = systemShowPath.slice(
            systemShowPath.lastIndexOf("\\") + 1,
            systemShowPath.lastIndexOf("."),
        );
        // @ts-ignore
        let file = e.target.files[0];
        const reader = new FileReader();
        reader.onload = function (e) {
            try {
                // @ts-ignore
                const jsonContent = JSON.parse(e.target.result);
                showToUpload = jsonContent;
            } catch (error) {
                console.error("Error parsing JSON:", error);
            }
        };
        reader.readAsText(file);
    }
</script>

<div class="flex justify-center">
    <div class="flex flex-col space-y-10 rounded-lg bg-object p-8">
        <h1 class="text-center text-4xl font-bold">All available shows</h1>
        <div class="flex flex-col space-y-2">
            {#if $shows.length}
                {#each $shows as available_show}
                    <div class="flex">
                        <Button
                            id={`clickShow-${available_show[0]}`}
                            grow
                            onclick={() => shows.switch(available_show[0])}
                        >
                            <p class={available_show[2] ? "font-bold" : ""}>
                                {available_show[1]}
                            </p>
                        </Button>
                        <Button
                            id={`downloadShow-${available_show[0]}`}
                            onclick={() => downloadShow(available_show[0])}
                        >
                            <Icon icon="mdi:download"></Icon>
                        </Button>
                        <Button
                            id={`renameShow-${available_show[0]}`}
                            onclick={() =>
                                renameShow(
                                    available_show[0],
                                    available_show[1],
                                )}
                        >
                            <Icon icon="mdi:rename"></Icon>
                        </Button>
                        <Button
                            id={`deleteShow-${available_show[0]}`}
                            onclick={() =>
                                deleteShow(
                                    available_show[0],
                                    available_show[1],
                                )}
                        >
                            <Icon icon="mdi:garbage-can-empty"></Icon>
                        </Button>
                    </div>
                {/each}
            {/if}
        </div>
        <div class="flex justify-evenly space-x-2">
            <div class="flex space-x-2">
                <LabeledTextinput label="New name" bind:value={newShowName} />
                {#if showToUpload === null}
                    <Button id="createShow" onclick={() => createNewShow()}>
                        <div class="flex">
                            <Icon icon="mdi:add"></Icon>
                        </div>
                    </Button>
                {:else}
                    <Button id="upload-show-button" onclick={() => uploadShow()}>
                        <div class="flex">
                            <Icon icon="mdi:upload"></Icon>
                        </div>
                    </Button>
                {/if}
            </div>

            <div class="flex items-center justify-center">
                <label
                    for="dropzone-file"
                    class="flex cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-input p-2"
                >
                    <div
                        class="flex flex-col items-center justify-center pb-6 pt-5"
                    >
                        <Icon icon="mdi:upload"></Icon>
                        <p class="font-semibold">Click to upload</p>
                        <p>or drag and drop</p>
                        <p class="text-xs">(show.json)</p>
                    </div>
                    <input
                        oninput={(e) => prepareFile(e)}
                        id="dropzone-file"
                        type="file"
                        class="hidden"
                        accept="application/json"
                    />
                </label>
            </div>
        </div>
    </div>
</div>
