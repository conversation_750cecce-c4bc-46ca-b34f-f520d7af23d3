#include "rw_utils.h"

Button::Button(int p_col_pin, int p_row_pin, int p_led_id,
               const int (&p_led_color)[3]) {
  col_pin = p_col_pin;
  row_pin = p_row_pin;
  led_id = p_led_id;
  led_state = LED_ON;
  for (int i = 0; i < 3; i++) {
    led_color[i] = p_led_color[i];
  }
};
void Button::tick(void (*on_press_callback)(int),
                  void (*on_release_callback)(int), Adafruit_NeoPixel *p_pixels,
                  bool p_led_blinker_state, int button_id) {
  digitalWrite(col_pin, LOW);

  bool currently_pressed = !digitalRead(row_pin);
  if (currently_pressed && !pressed) {
    pressed = true;
    on_press_callback(button_id);
  } else if (!currently_pressed && pressed) {
    pressed = false;
    on_release_callback(button_id);
  }

  if ((led_state == LED_ON) ||
      (led_state == LED_BLINK && p_led_blinker_state)) {
    p_pixels->setPixelColor(
        led_id, p_pixels->Color(led_color[0], led_color[1], led_color[2]));
  } else {
    p_pixels->setPixelColor(led_id, p_pixels->Color(0, 0, 0));
  }

  digitalWrite(col_pin, HIGH);
}
void Button::update_led_state(int (*state_update)(int)) {
  led_state = state_update(led_state);
}
void Button::set_led_color(const int (&p_led_color)[3]) {
  for (int i = 0; i < 3; i++) {
    led_color[i] = p_led_color[i];
  }
}
