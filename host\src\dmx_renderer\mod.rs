use crate::database_handler::pan_tilt_positions::{
    ComposedPanTiltPosition, FixturePosition,
};
use crate::database_handler::<PERSON>b<PERSON><PERSON><PERSON>;
use crate::dmx_renderer::channel::color_channels::RgbColor;
use crate::dmx_renderer::fixture::DmxFixture;
use crate::input_parser::structs::{
    ActivatedFixture, FunctionGetter, InputParser,
};
use crate::logging;
use crate::logging::Dashboard;
use serde::{Deserialize, Serialize};
use splines::{Interpolation, Spline};

pub mod channel;
pub mod dynamics;
pub mod fixture;
pub mod output;

use channel::unimplemented_channels::Keypoint;
use core::fmt::Display;
use core::time::Duration;
use dynamics::timecode::TimecodeFileDescriptor;
use map_to_range::MapRange;
use output::ArtNet;
use std::time::Instant;
use ts_rs::TS;

use self::dynamics::blueprint::BlueprintFileDescriptor;
use self::fixture::PositionIndexOffsetMode;

const UNIVERSE_SIZE: usize = 512;
pub const DMX_FRAME_RATE_IN_MS: u64 = 40; // ca. 25 FPS

const DMX_RENDERER_BEAT_IS_INDEX_POINTS: f32 = 160.;

pub const DEFAULT_BPM: u16 = 125;

pub struct DmxRenderer<'a> {
    position_timestamp: Instant,
    render_timestamp: Instant,
    /// This can modify the duration of an actual beat
    /// = 1.0 -> neutral
    /// > 1.0 -> beat is slower
    /// < 1.0 -> beat is faster
    pub bpm_modifier: f64,
    bpm: u16,
    fixtures: Vec<DmxFixture<'a>>,
    artnet_output: Option<ArtNet>,
}

impl<'a> DmxRenderer<'a> {
    #[must_use]
    pub fn new(db_handler: &mut DbHandler) -> Self {
        let artnet_output = ArtNet::new();

        let fixture_types = DbHandler::fixtures_for_active_show(
            &mut db_handler.db_connection(),
        )
        .iter()
        .map(|dmx_fixture_file_descriptor| {
            DmxFixture::new(&dmx_fixture_file_descriptor, _)
        })
        .collect();

        Self {
            position_timestamp: Instant::now(),
            render_timestamp: Instant::now(),
            fixtures,
            bpm: DEFAULT_BPM,
            bpm_modifier: 1.,
            artnet_output,
        }
    }
    pub fn render(
        &mut self,
        db_handler: &mut DbHandler,
        input_parser: &mut InputParser,
        eight_delta: f32,
    ) {
        self.render_timestamp = Instant::now();

        let mut universes: Vec<(u16, Vec<u8>)> = vec![];
        let mut existing_universes: Vec<u16> =
            self.fixtures.iter().map(DmxFixture::dmx_universe).collect();
        existing_universes.sort_unstable();
        existing_universes.dedup();
        for universe in &existing_universes {
            universes.push((
                *universe,
                self.build_dmx_universe(
                    *universe,
                    db_handler,
                    input_parser,
                    eight_delta,
                ),
            ));
        }
        if let Some(artnet_output) = self.artnet_output.as_ref() {
            artnet_output.send(&universes);
        }
    }
    pub fn build_dmx_universe(
        &mut self,
        universe: u16,
        db_handler: &mut DbHandler,
        input_parser: &mut InputParser,
        beat_delta: f32,
    ) -> Vec<u8> {
        let mut dmx_values: Vec<u8> = vec![0; UNIVERSE_SIZE];
        let fixtures_of_universe = self
            .fixtures
            .iter_mut()
            .filter(|fixture| fixture.dmx_universe() == universe);
        for fixture in fixtures_of_universe {
            let Some(dmx_address) = fixture.dmx_address() else {
                continue;
            };
            fixture.update_spline_index(
                None,
                Some(beat_delta * DMX_RENDERER_BEAT_IS_INDEX_POINTS),
            );
            for (i, channel) in fixture
                .get_dmx_footprint(db_handler, input_parser)
                .iter()
                .enumerate()
            {
                if let Some(t) = dmx_values
                    .get_mut(i.saturating_add(dmx_address.saturating_sub(1)))
                {
                    *t = *channel;
                }
            }
        }
        // logging::debug(format!("{:?}", dmx_values.get(..8)));
        dmx_values
    }
    pub fn apply_blueprint(
        &mut self,
        fixtures: &[ActivatedFixture],
        oneshot: bool,
        blueprint: &BlueprintFileDescriptor,
        all_positions: &[ComposedPanTiltPosition],
    ) {
        self.position_timestamp = Instant::now();
        self.for_all_fixtures_in(fixtures, |fixture| {
            fixture.apply_blueprint(blueprint, oneshot, all_positions);
        });
    }
    #[allow(clippy::cast_precision_loss, clippy::as_conversions)]
    pub fn apply_timecode(
        &mut self,
        fixtures: &[ActivatedFixture],
        timecode: &TimecodeFileDescriptor,
        _index: usize,
        _timecode_advancing: bool,
        db_handler: &mut DbHandler,
    ) {
        self.for_all_fixtures_in(fixtures, |fixture| {
            fixture.apply_timecode(timecode, db_handler);
        });
    }
    #[allow(
        clippy::cast_possible_truncation,
        clippy::cast_sign_loss,
        clippy::cast_precision_loss,
        clippy::as_conversions
    )]
    pub fn compute_beat_delta(&mut self) -> f32 {
        let beat_duration = (Self::beat_duration(self.bpm, self.bpm_modifier))
            .as_millis() as f32;

        let millis_to_compute =
            self.position_timestamp.elapsed().as_millis() as f32;

        self.position_timestamp = Instant::now();

        millis_to_compute / beat_duration
    }
    pub fn fill_dashboard(&self, dashboard: &mut Dashboard) -> bool {
        if dashboard.bpm != self.bpm() {
            dashboard.bpm = self.bpm();
            true
        } else if dashboard.bpm_modifier != self.bpm_modifier() {
            dashboard.bpm_modifier = self.bpm_modifier();
            true
        } else {
            false
        }
    }
    #[must_use]
    pub const fn is_artnet_active(&self) -> bool {
        self.artnet_output.is_some()
    }
    pub fn merge_new_fixtures_with_state(
        &mut self,
        fixtures: Vec<DmxFixtureFileDescriptor>,
    ) {
        self.fixtures.retain(|dmx_fixture| {
            fixtures
                .iter()
                .find(|fixture_fd| fixture_fd.id == Some(dmx_fixture.id()))
                .is_some()
        });
        for dmx_fixture_file_descriptor in fixtures {
            let Some(dmx_fixture_file_descriptor_id) =
                dmx_fixture_file_descriptor.id
            else {
                logging::log(
                    "Failing to merge DmxFixtureFD without an id with state"
                        .to_owned(),
                    logging::LogLevel::Warning,
                    true,
                );
                return;
            };
            let Some(fixture) =
                self.get_fixture_mut(dmx_fixture_file_descriptor_id)
            else {
                self.fixtures
                    .push(DmxFixture::new(dmx_fixture_file_descriptor.clone()));
                continue;
            };
            fixture
                .merge_file_descriptor_with_state(&dmx_fixture_file_descriptor);
        }
    }
    pub const fn get_fixtures(&mut self) -> &Vec<DmxFixture> {
        &self.fixtures
    }
    fn get_fixture_mut(&mut self, id: usize) -> Option<&mut DmxFixture> {
        self.fixtures.iter_mut().find(|fixture| fixture.id() == id)
    }
    pub fn set_color(
        &mut self,
        fixtures: &[ActivatedFixture],
        color: RgbColor,
        fade_duration: Option<u8>,
    ) {
        self.for_all_fixtures_in(fixtures, |fixture| {
            fixture.set_color(&color, fade_duration);
        });
    }
    pub fn set_dimmer_value(
        &mut self,
        new_dimmer_value: u8,
        fixtures: &[ActivatedFixture],
    ) {
        self.for_all_fixtures_in(fixtures, |fixture| {
            fixture.set_dimmer_value(new_dimmer_value);
        });
    }
    pub fn pan_to(&mut self, fixtures: &[ActivatedFixture], new_pan: u8) {
        self.for_all_fixtures_in(fixtures, |fixture| {
            fixture.pan_to(new_pan);
        });
    }
    pub fn add_to_pan(&mut self, fixtures: &[ActivatedFixture], modifier: u8) {
        self.for_all_fixtures_in(fixtures, |fixture| {
            fixture.add_to_pan(modifier);
        });
    }
    pub fn tilt_to(&mut self, fixtures: &[ActivatedFixture], new_tilt: u8) {
        self.for_all_fixtures_in(fixtures, |fixture| {
            fixture.tilt_to(new_tilt);
        });
    }
    pub fn add_to_tilt(&mut self, fixtures: &[ActivatedFixture], modifier: u8) {
        self.for_all_fixtures_in(fixtures, |fixture| {
            fixture.add_to_tilt(modifier);
        });
    }
    pub const fn bpm_to(&mut self, new_bpm: u16) {
        self.bpm = new_bpm;
    }
    #[must_use]
    pub const fn bpm(&self) -> u16 {
        self.bpm
    }
    #[allow(clippy::cast_possible_truncation, clippy::as_conversions)]
    #[must_use]
    pub fn bpm_truncated(&self) -> u8 {
        self.bpm.clamp(0, u8::MAX.into()) as u8
    }
    pub fn bpm_modifier_to(&mut self, new_modifier: u8) {
        if new_modifier == 128 {
            self.bpm_modifier = 1.;
        } else {
            let new_modifier: f64 = f64::from(new_modifier)
                .map_range((0., 255.), (0., 2.))
                .unwrap_or(1.);
            self.bpm_modifier = new_modifier;
        }
    }
    #[allow(
        clippy::cast_possible_truncation,
        clippy::cast_sign_loss,
        clippy::float_cmp,
        clippy::as_conversions
    )]
    #[must_use]
    pub fn bpm_modifier(&self) -> u8 {
        if self.bpm_modifier == 1. {
            128
        } else {
            self.bpm_modifier
                .map_range((0., 2.), (0., u8::MAX.into()))
                .unwrap_or(128.)
                .clamp(u8::MIN.into(), u8::MAX.into()) as u8
        }
    }
    pub fn set_pan_tilt_position(
        &mut self,
        fixtures: &[ActivatedFixture],
        position: &ComposedPanTiltPosition,
    ) {
        self.for_all_fixtures_in(fixtures, |fixture| {
            if let Some(pan_tilt_position) =
                position.fixture_positions.iter().find(|iter_position| {
                    iter_position.fixture_id() == fixture.id()
                })
            {
                fixture.set_pan_tilt_position(pan_tilt_position);
            }
        });
    }
    pub fn set_enabled(
        &mut self,
        fixtures: &[ActivatedFixture],
        setter: fn(bool) -> bool,
    ) {
        self.for_all_fixtures_in(fixtures, |fixture| {
            fixture.set_enabled(setter(fixture.get_enabled()));
        });
    }
    pub fn set_position_index_offset_mode(
        &mut self,
        fixtures: &[ActivatedFixture],
        position_index_offset_mode: PositionIndexOffsetMode,
    ) {
        self.for_all_fixtures_in(fixtures, |fixture| {
            fixture.set_position_index_offset_mode(position_index_offset_mode);
        });
    }
    pub fn set_speed_of_blueprints(
        &mut self,
        fixtures: &[ActivatedFixture],
        speed: u8,
    ) {
        self.for_all_fixtures_in(fixtures, |fixture| {
            fixture.set_speed_of_blueprints(speed);
        });
    }
    pub fn update_enabled(
        &mut self,
        fixtures: &[ActivatedFixture],
        update_fn: fn(bool) -> bool,
    ) {
        self.for_all_fixtures_in(fixtures, |fixture| {
            fixture.set_enabled(update_fn(fixture.get_enabled()));
        });
    }
    fn for_all_fixtures_in<F>(
        &mut self,
        active_fixtures: &[ActivatedFixture],
        mut callback: F,
    ) where
        F: FnMut(&mut DmxFixture),
    {
        for active_fixture in active_fixtures {
            self.fixtures.iter_mut().for_each(|fixture| {
                if active_fixture.name == fixture.get_name()
                    && active_fixture.fixturetype == *fixture.get_type()
                {
                    callback(fixture);
                }
            });
        }
    }
    pub const fn set_beat_fraction_duration(&mut self, new_bpm: u16) {
        self.bpm = new_bpm;
    }
    pub const fn set_manual_beat_duration_modifier(
        &mut self,
        new_modifier: f64,
    ) {
        self.bpm_modifier = new_modifier;
    }
    pub fn reset_to_defaults(&mut self) {
        self.position_timestamp = Instant::now();
        self.bpm = DEFAULT_BPM;
        self.bpm_modifier = 1.;
    }
    pub fn unimplemented_channel_to(
        &mut self,
        fixtures: &[ActivatedFixture],
        name: &String,
        value: Option<u8>,
    ) {
        self.for_all_fixtures_in(fixtures, |fixture| {
            if let Some(value) = value {
                fixture.set_unimplemented_channel(name, value);
            } else {
                fixture.reset_unimplemented_channel(name);
            }
        });
    }
    pub fn reset_unimplemented_channel(
        &mut self,
        fixtures: &[ActivatedFixture],
        channel_value: &String,
    ) {
        self.for_all_fixtures_in(fixtures, |fixture| {
            fixture.reset_unimplemented_channel(channel_value);
        });
    }
    pub fn clear_all_splines(&mut self, fixtures: &[ActivatedFixture]) {
        self.for_all_fixtures_in(fixtures, |fixture| {
            fixture.clear_all_splines();
        });
    }
    pub fn clear_splines_by_blueprint_id(
        &mut self,
        fixtures: &[ActivatedFixture],
        blueprint_id: usize,
    ) {
        self.for_all_fixtures_in(fixtures, |fixture| {
            fixture.clear_splines_by_blueprint_id(blueprint_id);
        });
    }
    pub fn capture_pan_tilt_positions(&mut self) -> Vec<FixturePosition> {
        let mut pan_tilt_positions = vec![];

        for live_fixture in self.get_fixtures() {
            if let Some(movement_channels) = live_fixture.movement_channels() {
                let (pan, tilt) = movement_channels.pan_tilt();
                pan_tilt_positions.push(FixturePosition::new(
                    live_fixture.id(),
                    pan,
                    tilt,
                ));
            }
        }
        pan_tilt_positions
    }
    #[must_use]
    pub fn all_keypoints(&self) -> Vec<&Keypoint> {
        let mut keypoints = vec![];
        for fixture in &self.fixtures {
            for channel in fixture.unimplemented_channels() {
                keypoints.push(channel.keypoints());
            }
        }
        keypoints.iter().flatten().copied().collect()
    }
    #[must_use]
    pub fn get_function_value_of_fixture(
        &self,
        function_getter: &FunctionGetter,
    ) -> Option<u8> {
        let fixture = self
            .fixtures
            .iter()
            .find(|fixture| fixture.id() == function_getter.fixture_id)?;

        fixture.get_function_value(&function_getter.function)
    }
    /// Length of a quater note
    /// Calculate `* <tact>` to get the duration of a full bar
    /// Calculate `/ 2` to get the duration of an eighth
    #[allow(
        clippy::as_conversions,
        clippy::cast_precision_loss,
        clippy::cast_possible_truncation,
        clippy::cast_sign_loss
    )]
    #[must_use]
    pub fn beat_duration(bpm: u16, bpm_modifier: f64) -> Duration {
        Duration::from_millis(
            (60000. / (f64::from(bpm) * bpm_modifier))
                .round()
                .clamp(0., u64::MAX as f64) as u64,
        )
    }
    #[allow(clippy::arithmetic_side_effects)]
    #[must_use]
    pub fn sixteenth_duration(bpm: u16, bpm_modifier: f64) -> Duration {
        Self::beat_duration(bpm, bpm_modifier) / 4
    }
}

#[must_use]
#[allow(clippy::modulo_arithmetic)]
pub fn clamp_index_to_spline_length(
    value: f32,
    spline: &Spline<f32, f32>,
) -> f32 {
    if value < 0. {
        return 0.;
    }
    let max_key = spline.keys().last().map_or(0., |key| key.t);
    if max_key > 0. {
        value % (max_key - 1.)
    } else {
        0.
    }
}

#[derive(Copy, Clone, Serialize, Deserialize, Debug, PartialEq, Eq, TS)]
#[ts(export)]
pub enum InterpolationMethod {
    Linear,
    Cosine,
}

impl<T, V> From<InterpolationMethod> for Interpolation<T, V> {
    fn from(value: InterpolationMethod) -> Self {
        match value {
            InterpolationMethod::Linear => Self::Linear,
            InterpolationMethod::Cosine => Self::Cosine,
        }
    }
}

impl From<String> for InterpolationMethod {
    fn from(value: String) -> Self {
        match value.as_str() {
            "Cosine" => Self::Cosine,
            _ => Self::Linear,
        }
    }
}

impl Display for InterpolationMethod {
    fn fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result {
        match self {
            Self::Linear => write!(f, "Linear"),
            Self::Cosine => write!(f, "Cosine"),
        }
    }
}
