<script lang="ts">
    import WaveSurfer from "wavesurfer.js";
    import TimelinePlugin from "wavesurfer.js/dist/plugins/timeline.esm.js";
    import Hover from "wavesurfer.js/dist/plugins/hover.esm.js";
    import { theme } from "$lib/stores/theme";
    import Button from "$lib/atoms/button.svelte";
    import { BEAT_INDEX_POINTS } from "../../routes/blueprintBuilder/partials/constants";

    // TODO: Make this dynamic
    const BPM = 125;

    let {
        trackUrl,
        editorWidth = $bindable(),
        timelineIndex = $bindable(),
        setTimecodeToIndexPaused = $bindable(),
        setTimecodeToIndexPlaying = $bindable(),
        updateScroll,
    }: {
        trackUrl: string;
        editorWidth: number;
        timelineIndex: number;
        setTimecodeToIndexPaused: (index: number) => any;
        setTimecodeToIndexPlaying: (index: number) => any;
        updateScroll: (newScroll: number) => any;
    } = $props();

    let wavesurfer: WaveSurfer;

    let loading = $state(false);

    let startCursorPosition: number = $state(0);

    $effect(() => {
        if (!wavesurfer) {
            loading = true;
            wavesurfer = WaveSurfer.create({
                container: "#waveform",
                plugins: [TimelinePlugin.create(), Hover.create({})],
            });
            wavesurfer.load(trackUrl);

            wavesurfer.on("ready", () => {
                loading = false;
                editorWidth = roundedBeatIndexPointsFrom(
                    wavesurfer.getDuration(),
                    BPM,
                );
            });

            wavesurfer.on("click", (e) => {
                startCursorPosition = wavesurfer.getDuration() * e;
                setTimecodeToIndexPaused(
                    roundedBeatIndexPointsFrom(startCursorPosition, BPM),
                );
            });

            wavesurfer.on("pause", () => {
                setTimecodeToIndexPaused(
                    roundedBeatIndexPointsFrom(
                        wavesurfer.getCurrentTime(),
                        BPM,
                    ),
                );
            });

            wavesurfer.on("play", () => {
                setTimecodeToIndexPlaying(
                    roundedBeatIndexPointsFrom(
                        wavesurfer.getCurrentTime(),
                        BPM,
                    ),
                );
            });

            wavesurfer.on("error", (data) => {
                console.error(data);
            });

            wavesurfer.on("timeupdate", () => {
                timelineIndex = roundedBeatIndexPointsFrom(
                    wavesurfer.getCurrentTime(),
                    BPM,
                );
            });
        }

        // TOOD: Add option to make this non-interactable. Klicking on this and then pressing SPACE, the song starts/stops immediately
        wavesurfer.setOptions({
            waveColor:
                $theme === "dark"
                    ? "#BB86FA"
                    : $theme === "light"
                      ? "#0051FB"
                      : $theme === "solar"
                        ? "#B58900"
                        : $theme === "catpuccin"
                          ? "#A68164"
                          : "rgb(0, 0, 0)",
            progressColor:
                $theme === "dark"
                    ? "#7A3E9D"
                    : $theme === "light"
                      ? "#003DAA"
                      : $theme === "solar"
                        ? "#8B6B00"
                        : $theme === "catpuccin"
                          ? "#7A5B4D"
                          : "rgb(0, 0, 0)",
        });
    });

    function handleKeyEvent(event: KeyboardEvent) {
        if (event.key === " ") {
            if (wavesurfer.isPlaying()) {
                stop();
            } else {
                play();
            }
        }
    }

    function roundedBeatIndexPointsFrom(
        timestampInSeconds: number,
        bpm: number,
    ): number {
        const SECONDS_IN_MINUTE = 60;
        return Math.round(
            (timestampInSeconds / SECONDS_IN_MINUTE) * bpm * BEAT_INDEX_POINTS,
        );
    }

    export function play() {
        wavesurfer.play();
    }

    export function pause() {
        wavesurfer.pause();
    }

    export function stop() {
        wavesurfer.pause();
        setTimecodeToIndexPaused(
            roundedBeatIndexPointsFrom(startCursorPosition, BPM),
        );
        wavesurfer.setTime(startCursorPosition);
    }
</script>

<svelte:window on:keydown={handleKeyEvent} />

<div class="flex">
    <div class="min-w-[250px] w-[250px]">
        <Button
            id="zoom-plus-button"
            onclick={() => {
                console.error("not implemented");
                // zoom += 10;
                // wavesurfer.zoom(zoom);
            }}>Plus 10 Zoom</Button
        >
        <Button
            id="zoom-minus-button"
            onclick={() => {
                console.error("not implemented");
                // zoom -= 10;
                // wavesurfer.zoom(zoom);
            }}>Minus 10 Zoom</Button
        >
    </div>

    <div
        class="overflow-x-scroll"
        onscroll={(e) => {
            // @ts-ignore: ts does not understand e
            updateScroll(e.target.scrollLeft);
        }}
    >
        <div
            class="border"
            style={`width: ${editorWidth}px;`}
            id="waveform"
        ></div>
    </div>
</div>
{#if loading}
    loading...
{/if}
