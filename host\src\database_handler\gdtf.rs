// TODO: repair me
// use crate::database_handler::db_handler::library_gdtfs_dir;
// use crate::logging;
use gdtf_parser::Gdtf;
// use std::{
//     fs::{create_dir_all, read_dir},
//     path::Path,
// };

#[must_use]
pub const fn gdtfs_from_filesystem() -> Vec<Gdtf> {
    let result: Vec<Gdtf> = vec![];
    // if let Ok(possible_gdtfs) = read_dir(library_gdtfs_dir()) {
    //     for gdtf_dir_entry_result in possible_gdtfs.flatten() {
    //         if let Ok(path_name) =
    //             gdtf_dir_entry_result.path().into_os_string().into_string()
    //         {
    //             let path_name_str = path_name.as_str();
    //
    //             let path = Path::new(path_name_str);
    //             if let Ok(gdtf) = Gdtf::try_from(path) {
    //                 result.push(gdtf);
    //             }
    //         }
    //     }
    // } else {
    //     logging::log(
    //         "GDTF library folder not found creating new".to_string(),
    //         logging::LogLevel::Info,
    //         false,
    //     );
    //     let _trash_bin = create_dir_all(library_gdtfs_dir());
    // }
    result
}
