CREATE TABLE fixture_instance (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name <PERSON><PERSON><PERSON><PERSON>(255),
    dmx_address INT,
    dmx_universe INT NOT NULL,
    stage_coordinate_x INT,
    stage_coordinate_y INT,
    fixture_type_id INT,
    FOREIGN KEY (fixture_type_id) REFERENCES fixtures(id) ON DELETE SET NULL
);

INSERT INTO fixture_instance(name, dmx_address, dmx_universe, stage_coordinate_x, stage_coordinate_y, fixture_type_id)
SELECT name, dmx_address, dmx_universe, stage_coordinate_x, stage_coordinate_y, id FROM fixtures;

ALTER TABLE fixtures
DROP COLUMN name;
ALTER TABLE fixtures
DROP COLUMN dmx_address;
ALTER TABLE fixtures
DROP COLUMN dmx_universe;
ALTER TABLE fixtures
DROP COLUMN stage_coordinate_x;
ALTER TABLE fixtures
DROP COLUMN stage_coordinate_y;

RENAME TABLE fixtures TO fixture_types;
