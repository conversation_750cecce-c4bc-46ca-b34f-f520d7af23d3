<script lang="ts">
    import Select from "$lib/atoms/select.svelte";
    import { allCallableSnippets } from "$lib/stores/snippets";

    const HEIGHT = 72;
    const WIDTH = 160;

    let {
        snippetId = $bindable(),
    }: {
        snippetId: number;
    } = $props();
</script>

<Select bind:value={snippetId}>
    {#each allCallableSnippets() as snippet}
        <option value={snippet.id}>{snippet.name}</option>
    {/each}
</Select>
