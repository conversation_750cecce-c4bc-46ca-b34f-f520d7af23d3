// This file was generated by [ts-rs](https://github.com/Aleph-Alpha/ts-rs). Do not edit this file manually.
import type { Instruction } from "./Instruction";
import type { SnippetCategory } from "./SnippetCategory";

export interface Snippet { id: number, category: SnippetCategory, serial_module_key: number | null, name: string, do_not_use_instructions: boolean, instructions: Array<Instruction>, requires_user_action_reason: string | null, }