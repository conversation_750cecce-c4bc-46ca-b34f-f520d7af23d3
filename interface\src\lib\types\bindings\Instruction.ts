// This file was generated by [ts-rs](https://github.com/Aleph-Alpha/ts-rs). Do not edit this file manually.
import type { AddToGroup } from "./AddToGroup";
import type { BlueprintToInstruction } from "./BlueprintToInstruction";
import type { ColorToInstruction } from "./ColorToInstruction";
import type { FixtureLoop } from "./FixtureLoop";
import type { IfStatement } from "./IfStatement";
import type { InstructionValue } from "./InstructionValue";
import type { LoopStatement } from "./LoopStatement";
import type { PositionIndexOffsetMode } from "./PositionIndexOffsetMode";
import type { QueueMode } from "./QueueMode";
import type { RemoveFromGroup } from "./RemoveFromGroup";
import type { TimecodeToInstruction } from "./TimecodeToInstruction";
import type { UnimplementedChannelSetter } from "./UnimplementedChannelSetter";
import type { VariableSetter } from "./VariableSetter";

export type Instruction = { "instruction": "ExecuteCallableSnippet", "instructionMod": number } | { "instruction": "ColorTo", "instructionMod": ColorToInstruction } | { "instruction": "ColorToRandom", "instructionMod": InstructionValue | null } | { "instruction": "ActivateAllFixtures" } | { "instruction": "ActivateFixtureById", "instructionMod": number } | { "instruction": "ActivateFixtureGroup", "instructionMod": string } | { "instruction": "BlueprintTo", "instructionMod": BlueprintToInstruction } | { "instruction": "TimecodeTo", "instructionMod": TimecodeToInstruction } | { "instruction": "SetBlueprintPositionIndexOffsetMode", "instructionMod": PositionIndexOffsetMode } | { "instruction": "SetSpeedOfBlueprints", "instructionMod": InstructionValue } | { "instruction": "ToggleQueueMode", "instructionMod": QueueMode } | { "instruction": "UnimplementedChannelTo", "instructionMod": UnimplementedChannelSetter } | { "instruction": "PanTo", "instructionMod": InstructionValue } | { "instruction": "AddToPan", "instructionMod": InstructionValue } | { "instruction": "TiltTo", "instructionMod": InstructionValue } | { "instruction": "AddToTilt", "instructionMod": InstructionValue } | { "instruction": "PositionTo", "instructionMod": number } | { "instruction": "BpmTo", "instructionMod": InstructionValue } | { "instruction": "BpmModifierTo", "instructionMod": InstructionValue } | { "instruction": "FixtureLoop", "instructionMod": FixtureLoop } | { "instruction": "CreateVariable", "instructionMod": string } | { "instruction": "SetVariable", "instructionMod": VariableSetter } | { "instruction": "AddToGroup", "instructionMod": AddToGroup } | { "instruction": "RemoveFromGroup", "instructionMod": RemoveFromGroup } | { "instruction": "DelayBy", "instructionMod": InstructionValue } | { "instruction": "DeselectAllFixtures" } | { "instruction": "DimmerTo", "instructionMod": InstructionValue } | { "instruction": "ClearBlueprint", "instructionMod": number } | { "instruction": "StartRecording" } | { "instruction": "StopRecording" } | { "instruction": "ClearRecording" } | { "instruction": "IfStatement", "instructionMod": IfStatement } | { "instruction": "LoopStatement", "instructionMod": LoopStatement } | { "instruction": "IfQueueAllowsContinue", "instructionMod": Array<Instruction> } | { "instruction": "Nop" };