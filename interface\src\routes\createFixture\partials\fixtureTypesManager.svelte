<script lang="ts">
    import Button from "$lib/atoms/button.svelte";
    import Icon from "$lib/atoms/icon.svelte";
    import LabeledTextinput from "$lib/molecules/labeled_textinput.svelte";
    import LabeledNumberinput from "$lib/molecules/labeled_numberinput.svelte";
    import LabeledDropdown from "$lib/molecules/labeled_dropdown.svelte";
    import type { FixtureType } from "$lib/types/bindings/FixtureType";
    import type { MovementChannels } from "$lib/types/bindings/MovementChannels";
    import type { UnimplementedChannel } from "$lib/types/bindings/UnimplementedChannel";
    import { fixtureTypes } from "$lib/stores/fixtureTypes";
    import { onMount } from "svelte";

    let editingType: FixtureType | null = $state(null);
    let isCreating = $state(false);

    let colorChannels:
        | ""
        | "RgbChannels"
        | "ColorWheel"
        | "CmyChannels"
        | "HsvChannels"
        | "XyzChannels" = $state("");

    let unimplemented_channels: UnimplementedChannel[] = $state([]);
    let hasMovementChannels = $state(false);
    let hasColorChannel = $derived(!!colorChannels);
    
    function emptyMovementChannels(): MovementChannels {
        return { pan: { channel: -1 }, tilt: { channel: -1 } };
    }
    let newMovementChannels = $state(emptyMovementChannels());

    function emptyFixtureType(): FixtureType {
        return {
            id: undefined,
            name: "",
            fixturetype: "",
            footprint_size: 0,
            movement_channels: undefined,
            color: undefined,
            unimplemented_channels: [],
        };
    }

    let newFixtureType = $state(emptyFixtureType());

    // Color channel bindings
    let r: number | null = $state(null);
    let g: number | null = $state(null);
    let b: number | null = $state(null);
    let c: number | null = $state(null);
    let m: number | null = $state(null);
    let y: number | null = $state(null);
    let h: number | null = $state(null);
    let s: number | null = $state(null);
    let v: number | null = $state(null);
    let x: number | null = $state(null);
    let yy: number | null = $state(null);
    let z: number | null = $state(null);

    // Color wheel bindings
    let channel = $state(-1);
    let red = $state(-1);
    let green = $state(-1);
    let blue = $state(-1);
    let light_blue = $state(-1);
    let purple = $state(-1);
    let yellow = $state(-1);
    let pink = $state(-1);
    let orange = $state(-1);
    let white = $state(-1);

    onMount(async () => {
        await fixtureTypes.refresh();
    });

    function clearForm() {
        newFixtureType = emptyFixtureType();
        hasMovementChannels = false;
        newMovementChannels = emptyMovementChannels();
        colorChannels = "";
        resetColorBindings();
        unimplemented_channels = [];
        isCreating = false;
        editingType = null;
    }

    function resetColorBindings() {
        r = null; g = null; b = null;
        c = null; m = null; y = null;
        h = null; s = null; v = null;
        x = null; yy = null; z = null;
        channel = -1; red = -1; green = -1; blue = -1;
        light_blue = -1; purple = -1; yellow = -1;
        pink = -1; orange = -1; white = -1;
    }

    function startCreating() {
        clearForm();
        isCreating = true;
    }

    function editFixtureType(fixtureType: FixtureType) {
        clearForm();
        editingType = fixtureType;
        isCreating = true;
        
        // Populate form
        newFixtureType = { ...fixtureType };
        unimplemented_channels = [...fixtureType.unimplemented_channels];
        
        if (fixtureType.movement_channels) {
            hasMovementChannels = true;
            newMovementChannels = { ...fixtureType.movement_channels };
        }

        // Populate color channels
        if (fixtureType.color) {
            const channels = fixtureType.color.channels;
            if ("RgbChannels" in channels) {
                colorChannels = "RgbChannels";
                r = channels.RgbChannels.r ?? null;
                g = channels.RgbChannels.g ?? null;
                b = channels.RgbChannels.b ?? null;
            }
            // Add other color channel types as needed...
        }
    }

    function add_unimplemented_channel() {
        unimplemented_channels = [
            ...unimplemented_channels,
            {
                id: null,
                name: "",
                channel: 0,
                default: 0,
                dimmable: false,
                inverted: false,
                keypoints: [],
            },
        ];
    }

    function applyColorChannelsToFixtureType(fixtureType: FixtureType) {
        if (colorChannels === "RgbChannels") {
            fixtureType.color = {
                color: { red: 0, green: 0, blue: 0 },
                channels: {
                    RgbChannels: { r, g, b },
                },
            };
        }
        // Add other color channel types as needed...
    }

    async function saveFixtureType() {
        if (hasMovementChannels) {
            newFixtureType.movement_channels = newMovementChannels;
        }
        
        applyColorChannelsToFixtureType(newFixtureType);
        newFixtureType.unimplemented_channels = unimplemented_channels;

        if (editingType) {
            await fixtureTypes.update(newFixtureType);
        } else {
            await fixtureTypes.create(newFixtureType);
        }
        clearForm();
    }

    async function deleteFixtureType(id: number | undefined) {
        if (id) {
            await fixtureTypes.delete(id);
        }
    }
</script>

<div class="fixture-types-manager">
    <div class="header">
        <h2>Fixture Types</h2>
        <Button id="new-fixture-type" onclick={startCreating}>
            <Icon icon="mdi:add" />
            New Fixture Type
        </Button>
    </div>

    {#if !isCreating}
        <div class="fixture-types-list">
            {#each $fixtureTypes as fixtureType}
                <div class="fixture-type-card">
                    <div class="card-header">
                        <h3>{fixtureType.name}</h3>
                        <div class="actions">
                            <Button id="edit-type-{fixtureType.id}" onclick={() => editFixtureType(fixtureType)}>
                                <Icon icon="solar:pen-bold" />
                            </Button>
                            <Button id="delete-type-{fixtureType.id}" onclick={() => deleteFixtureType(fixtureType.id)}>
                                <Icon icon="mdi:delete" />
                            </Button>
                        </div>
                    </div>
                    <div class="card-content">
                        <p><strong>Type:</strong> {fixtureType.fixturetype}</p>
                        <p><strong>Footprint:</strong> {fixtureType.footprint_size} channels</p>
                        <p><strong>Has Movement:</strong> {fixtureType.movement_channels ? 'Yes' : 'No'}</p>
                        <p><strong>Has Color:</strong> {fixtureType.color ? 'Yes' : 'No'}</p>
                        <p><strong>Other Channels:</strong> {fixtureType.unimplemented_channels.length}</p>
                    </div>
                </div>
            {/each}
        </div>
    {:else}
        <div class="fixture-type-editor rounded-lg bg-object p-8">
            <h3>{editingType ? 'Edit' : 'Create'} Fixture Type</h3>
            
            <div class="form-grid">
                <div class="form-section">
                    <LabeledTextinput label="Name" bind:value={newFixtureType.name} />
                    <LabeledTextinput label="Fixture Type" bind:value={newFixtureType.fixturetype} />
                    <LabeledNumberinput label="Footprint Size" bind:value={newFixtureType.footprint_size} />
                </div>

                <div class="form-section">
                    <LabeledDropdown label="Color Channel Type" bind:value={colorChannels}>
                        <option value="">-</option>
                        <option value="RgbChannels">RGB Channels</option>
                        <option value="ColorWheel">Color Wheel</option>
                        <option value="CmyChannels">CMY Channels</option>
                        <option value="HsvChannels">HSV Channels</option>
                        <option value="XyzChannels">XYZ (CIE) Channels</option>
                    </LabeledDropdown>

                    {#if colorChannels === "RgbChannels"}
                        <div class="color-channels">
                            <div class="flex items-center space-x-2">
                                <Button onclick={() => r = r === null ? 0 : null}>
                                    <Icon icon={r !== null ? "mdi:check-circle" : "mdi:close"} />
                                </Button>
                                {#if r !== null}
                                    <LabeledNumberinput label="Red Channel" bind:value={r} min={1} max={512} />
                                {:else}
                                    <span class="text-gray-400">Red channel - disabled</span>
                                {/if}
                            </div>
                            <!-- Similar for green and blue channels -->
                        </div>
                    {/if}
                </div>

                <div class="form-section">
                    {#if !hasMovementChannels}
                        <Button onclick={() => hasMovementChannels = true}>
                            <Icon icon="mdi:add" />
                            Add Pan/Tilt Channels
                        </Button>
                    {:else}
                        <div class="movement-channels">
                            <h4>Movement Channels</h4>
                            {#if newMovementChannels.pan && newMovementChannels.tilt}
                                <LabeledNumberinput label="Pan" bind:value={newMovementChannels.pan.channel} />
                                <LabeledNumberinput label="Tilt" bind:value={newMovementChannels.tilt.channel} />
                            {/if}
                            <Button onclick={() => hasMovementChannels = false}>
                                <Icon icon="mdi:delete" />
                            </Button>
                        </div>
                    {/if}

                    <div class="other-channels">
                        <Button onclick={add_unimplemented_channel}>
                            <Icon icon="mdi:add" />
                            Add Other Channel
                        </Button>
                        
                        {#each unimplemented_channels as channel, index}
                            <div class="channel-editor">
                                <LabeledTextinput label="Name" bind:value={channel.name} />
                                <LabeledNumberinput label="Channel" bind:value={channel.channel} />
                                <LabeledNumberinput label="Default" bind:value={channel.default} />
                                <label>
                                    <input type="checkbox" bind:checked={channel.dimmable} />
                                    Dimmable
                                </label>
                                <label>
                                    <input type="checkbox" bind:checked={channel.inverted} />
                                    Inverted
                                </label>
                                <Button onclick={() => {
                                    unimplemented_channels.splice(index, 1);
                                    unimplemented_channels = unimplemented_channels;
                                }}>
                                    <Icon icon="mdi:delete" />
                                </Button>
                            </div>
                        {/each}
                    </div>
                </div>
            </div>

            <div class="form-actions">
                <Button onclick={clearForm}>
                    <Icon icon="mdi:close" />
                    Cancel
                </Button>
                <Button onclick={saveFixtureType}>
                    <Icon icon="mdi:check" />
                    {editingType ? 'Update' : 'Create'} Fixture Type
                </Button>
            </div>
        </div>
    {/if}
</div>

<style>
    .fixture-types-manager {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .header {
        display: flex;
        justify-content: between;
        align-items: center;
    }

    .fixture-types-list {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 1rem;
    }

    .fixture-type-card {
        @apply rounded-lg bg-object p-4;
    }

    .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }

    .actions {
        display: flex;
        gap: 0.5rem;
    }

    .form-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin: 2rem 0;
    }

    .form-section {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .form-actions {
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
    }

    .color-channels,
    .movement-channels,
    .other-channels {
        @apply rounded-lg border border-input bg-primary p-4;
    }

    .channel-editor {
        @apply rounded-lg border border-input bg-object p-2;
        display: flex;
        gap: 1rem;
        align-items: end;
        flex-wrap: wrap;
    }
</style>