import { get, writable } from 'svelte/store';
import { networking } from './networking';
import type { Show } from '$lib/types/bindings/Show'
import { TOAST } from './toast';

// TODO: This type should autogenerate from the Backend
type ShowWithActiveHint = [number, string, boolean];

function createShowStore() {
    const { subscribe, set } = writable<ShowWithActiveHint[]>([]);

    fetchShows().then(data => set(data))

    return {
        subscribe,
        switch: (id: number) => switchShow(id).then(data => set(data)),
        create: (show: string) => createShow(show).then(data => set(data)),
        upload: (show: Show) => uploadShow(show).then(data => set(data)),
        delete: (id: number) => deleteShow(id).then(data => set(data)),
        getShow: (id: number) => getShow(id),
        rename: (id: number, new_name: string) => renameShow(id, new_name).then(data => set(data))
    };
}

export const shows = createShowStore();

async function getShow(id: number): Promise<Show> {
    const response = await fetch(`http://${get(networking)}:${networking.port}/show/${id}`);
    return JSON.parse(await response.json());
}

async function fetchShows(): Promise<ShowWithActiveHint[]> {
    return new Promise(async (resolve, _) => {
        const ip = get(networking);
        if (ip) {
            const response = await fetch(`http://${ip}:${networking.port}/shows`);
            resolve(response.json());
        } else {
            setTimeout(async () => resolve(await fetchShows()), 1000)
        }
    })
}

async function switchShow(id: number): Promise<ShowWithActiveHint[]> {
    await fetch(`http://${get(networking)}:${networking.port}/show/${id}/set_active`, {
        method: 'PATCH',
    });
    return fetchShows();
}
async function renameShow(id: number, new_name: string) {
    await fetch(`http://${get(networking)}:${networking.port}/show/${id}/rename?new_name=${new_name}`, {
        method: 'PATCH'
    });
    return fetchShows();
}
async function uploadShow(show: Show) {
    let result = await fetch(`http://${get(networking)}:${networking.port}/show`, {
        method: 'PUT',
        body: JSON.stringify(show),
        headers: new Headers({ 'content-type': 'application/json' }),
    });
    if (result.status === 409) {
        TOAST.error('Unable to upload show, because there can not be two with the same name')
    }
    return fetchShows();
}
async function createShow(showName: string): Promise<ShowWithActiveHint[]> {
    let result = await fetch(`http://${get(networking)}:${networking.port}/show`, {
        method: 'POST',
        body: JSON.stringify(showName),
        headers: new Headers({ 'content-type': 'application/json' }),
    });
    if (result.status === 409) {
        TOAST.error('Unable to create show, because there can not be two with the same name')
    }
    return fetchShows();
}
async function deleteShow(id: number) {
    await fetch(`http://${get(networking)}:${networking.port}/show/${id}`, {
        method: 'DELETE',
    });
    return fetchShows();
}
