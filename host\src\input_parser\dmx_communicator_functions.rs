use super::structs::{
    ActivatedFixture, ColorToInstruction, Comparator,
    DelayableInstructionBatch, InputParser, Instruction, LoopStatement,
    LoopWithTailInstructions, QueueMode, QueuedInstruction,
};
use crate::database_handler::<PERSON>b<PERSON><PERSON><PERSON>;
use crate::dmx_renderer::channel::color_channels::RgbColor;
use crate::dmx_renderer::dynamics::blueprint::BlueprintFileDescriptor;
use crate::dmx_renderer::fixture::PositionIndexOffsetMode;
use crate::dmx_renderer::DmxRenderer;
use crate::input_parser::structs::InstructionValue;
use crate::logging;

pub const PRESSED: u8 = 1;
pub const RELEASED: u8 = 0;
pub const DEFAULT_BPM: u8 = 120;
pub const DEFAULT_BPM_MODIFIER: u8 = 128;

pub fn evaluate_instructions(
    input_parser: &mut InputParser,
    dmx_renderer: &mut DmxRenderer,
    value: u8,
    instructions: &Vec<Instruction>,
    db_handler: &mut DbHandler,
    // TODO: This is sort of broken and needs to be fixed. Github issue #86
    ignore_delays: bool,
) {
    for instruction in instructions {
        evaluate_instruction(
            input_parser,
            dmx_renderer,
            value,
            instruction,
            db_handler,
            ignore_delays,
        );
    }
}

#[allow(clippy::too_many_lines)]
pub fn evaluate_instruction(
    input_parser: &mut InputParser,
    dmx_renderer: &mut DmxRenderer,
    value: u8,
    instruction: &Instruction,
    db_handler: &mut DbHandler,
    ignore_delays: bool,
) {
    if input_parser.delay_upcomming_instructions && !ignore_delays {
        if let Some(delayed_instruction_batch) =
            input_parser.delayed_instructions.last_mut()
        {
            delayed_instruction_batch
                .instructions
                .push(instruction.clone());
        } else {
            logging::log("Unable to find DelayableInstructionBatch to attach delayed instructions to".to_owned(), logging::LogLevel::Warning, false);
        }
    } else if input_parser.append_upcomming_instructions_to_loop {
        if let Some(loop_with_tail_instructions) =
            input_parser.loop_with_tail_instructions.last_mut()
        {
            loop_with_tail_instructions
                .tail_instructions
                .push(instruction.clone());
        } else {
            logging::log("Unable to find LoopWithTailInstructions to attach tail-instructions to".to_owned(), logging::LogLevel::Warning, false);
        }
    } else {
        match &instruction {
            Instruction::ExecuteCallableSnippet(snippet_id) => {
                let mut instructions = vec![];
                if let Some(snippet) =
                    &db_handler.find_snippet_by_id(*snippet_id)
                {
                    instructions = snippet.instructions.clone();
                }
                evaluate_instructions(
                    input_parser,
                    dmx_renderer,
                    value,
                    &instructions,
                    db_handler,
                    false,
                );
            }
            Instruction::FixtureLoop(fixture_loop) => {
                evaluate_instructions(
                    input_parser,
                    dmx_renderer,
                    value,
                    &fixture_loop.fixtures,
                    db_handler,
                    false,
                );
                evaluate_instructions(
                    input_parser,
                    dmx_renderer,
                    value,
                    &fixture_loop.instructions,
                    db_handler,
                    false,
                );
                evaluate_instruction(
                    input_parser,
                    dmx_renderer,
                    value,
                    &Instruction::DeselectAllFixtures,
                    db_handler,
                    false,
                );
            }
            Instruction::DeselectAllFixtures => {
                input_parser.selected_fixtures = vec![];
            }
            Instruction::AddToGroup(add_to_group) => {
                evaluate_instructions(
                    input_parser,
                    dmx_renderer,
                    value,
                    &add_to_group.fixtures,
                    db_handler,
                    false,
                );

                let Some(group) = db_handler
                    .active_show
                    .groups
                    .iter_mut()
                    .find(|group| group.name == add_to_group.name)
                else {
                    input_parser.selected_fixtures = vec![];
                    logging::log(
                        format!(
                            "Unable to find fixture group {}",
                            add_to_group.name
                        ),
                        logging::LogLevel::Warning,
                        false,
                    );
                    return;
                };

                if group.id.is_some() {
                    for selected_fixture in &input_parser.selected_fixtures {
                        group.fixture_ids.retain(|group_fixture_id| {
                            *group_fixture_id != selected_fixture.id
                        });
                    }
                    for selected_fixture in &input_parser.selected_fixtures {
                        group.fixture_ids.push(selected_fixture.id);
                    }
                } else {
                    logging::log(
                        format!(
                            "Attempt to mutate generated group {}",
                            add_to_group.name
                        ),
                        logging::LogLevel::Warning,
                        false,
                    );
                }
            }
            Instruction::RemoveFromGroup(remove_from_group) => {
                evaluate_instructions(
                    input_parser,
                    dmx_renderer,
                    value,
                    &remove_from_group.fixtures,
                    db_handler,
                    false,
                );

                let Some(group) = db_handler
                    .active_show
                    .groups
                    .iter_mut()
                    .find(|group| group.name == remove_from_group.name)
                else {
                    input_parser.selected_fixtures = vec![];
                    logging::log(
                        format!(
                            "Unable to find fixture group {}",
                            remove_from_group.name
                        ),
                        logging::LogLevel::Warning,
                        false,
                    );
                    return;
                };

                if group.id.is_some() {
                    for selected_fixture in &input_parser.selected_fixtures {
                        group.fixture_ids.retain(|group_fixture_id| {
                            *group_fixture_id != selected_fixture.id
                        });
                    }
                } else {
                    logging::log(
                        format!(
                            "Attempt to mutate generated group {}",
                            remove_from_group.name
                        ),
                        logging::LogLevel::Warning,
                        false,
                    );
                }

                input_parser.selected_fixtures = vec![];
            }
            Instruction::ToggleQueueMode(queue_mode) => {
                input_parser.queue_mode = *queue_mode;
            }
            Instruction::ColorTo(color_to_instruction) => {
                color_to(
                    value,
                    input_parser,
                    dmx_renderer,
                    color_to_instruction,
                );
            }
            Instruction::ColorToRandom(fade_duration) => {
                color_to_random(
                    value,
                    input_parser,
                    dmx_renderer,
                    fade_duration.clone(),
                );
            }
            Instruction::ActivateAllFixtures => {
                activate_all_fixtures(input_parser, db_handler);
            }
            Instruction::ActivateFixtureById(fixture_id) => {
                activate_fixture_by_id(input_parser, db_handler, *fixture_id);
            }
            Instruction::ActivateFixtureGroup(group_name) => {
                activate_fixture_group(input_parser, db_handler, group_name);
            }
            Instruction::SetBlueprintPositionIndexOffsetMode(mode) => {
                set_blueprint_position_index_offset_mode(
                    input_parser,
                    *mode,
                    dmx_renderer,
                );
            }
            Instruction::SetSpeedOfBlueprints(speed_value) => {
                set_speed_of_blueprints(
                    value,
                    input_parser,
                    dmx_renderer,
                    speed_value,
                );
            }
            Instruction::BlueprintTo(blueprint_to_instruction) => {
                blueprint_to(
                    input_parser,
                    blueprint_to_instruction.id,
                    blueprint_to_instruction.oneshot,
                    dmx_renderer,
                    db_handler,
                );
                if !input_parser.delay_upcomming_instructions
                    && blueprint_to_instruction.delay
                    && !ignore_delays
                {
                    input_parser.delay_upcomming_instructions = true;
                    input_parser.delayed_instructions.push(
                        DelayableInstructionBatch::new(
                            value,
                            BlueprintFileDescriptor::max_len_in_eights(
                                db_handler,
                                blueprint_to_instruction.id,
                            ),
                            input_parser.selected_fixtures.clone(),
                        ),
                    );
                }
            }
            Instruction::TimecodeTo(timecode) => {
                timecode_to(
                    input_parser,
                    timecode.id,
                    timecode.index,
                    timecode.play,
                    dmx_renderer,
                    db_handler,
                );
            }
            Instruction::UnimplementedChannelTo(
                unimplemented_channel_setter,
            ) => {
                let unimplemented_channel_setter_value =
                    unimplemented_channel_setter.value.clone();
                dmx_renderer.unimplemented_channel_to(
                    &input_parser.selected_fixtures,
                    &unimplemented_channel_setter.name,
                    unimplemented_channel_setter_value.to_num(
                        value,
                        dmx_renderer,
                        input_parser,
                    ),
                );
            }
            Instruction::AddToPan(modifier) => {
                dmx_renderer.add_to_pan(
                    &input_parser.selected_fixtures,
                    modifier
                        .to_num(value, dmx_renderer, input_parser)
                        .unwrap_or(0),
                );
            }
            Instruction::PanTo(pan) => {
                dmx_renderer.pan_to(
                    &input_parser.selected_fixtures,
                    pan.to_num(value, dmx_renderer, input_parser).unwrap_or(0),
                );
            }
            Instruction::AddToTilt(modifier) => {
                dmx_renderer.add_to_tilt(
                    &input_parser.selected_fixtures,
                    modifier
                        .to_num(value, dmx_renderer, input_parser)
                        .unwrap_or(0),
                );
            }
            Instruction::TiltTo(tilt) => {
                dmx_renderer.tilt_to(
                    &input_parser.selected_fixtures,
                    tilt.to_num(value, dmx_renderer, input_parser).unwrap_or(0),
                );
            }
            Instruction::DimmerTo(master) => {
                dmx_renderer.set_dimmer_value(
                    master
                        .to_num(value, dmx_renderer, input_parser)
                        .unwrap_or(0),
                    &input_parser.selected_fixtures,
                );
            }
            Instruction::PositionTo(position_id) => {
                position_to(
                    *position_id,
                    db_handler,
                    dmx_renderer,
                    input_parser,
                );
            }
            Instruction::BpmTo(bpm) => {
                dmx_renderer.bpm_to(
                    bpm.to_num(value, dmx_renderer, input_parser)
                        .unwrap_or(DEFAULT_BPM)
                        .into(),
                );
            }
            Instruction::BpmModifierTo(bpm_modifier) => {
                dmx_renderer.bpm_modifier_to(
                    bpm_modifier
                        .to_num(value, dmx_renderer, input_parser)
                        .unwrap_or(DEFAULT_BPM_MODIFIER),
                );
            }
            Instruction::CreateVariable(name) => {
                input_parser.create_variable(name.clone());
            }
            Instruction::SetVariable(variable_setter) => {
                let other_value = &variable_setter
                    .value
                    .to_num(value, dmx_renderer, input_parser)
                    .unwrap_or(0);
                if let Some(variable) =
                    input_parser.get_variable_mut(&variable_setter.name)
                {
                    variable.value = *other_value;
                }
            }
            Instruction::DelayBy(eights) => {
                if !ignore_delays {
                    input_parser.delay_upcomming_instructions = true;
                    input_parser.delayed_instructions.push(
                        DelayableInstructionBatch::new(
                            value,
                            eights
                                .to_num(value, dmx_renderer, input_parser)
                                .unwrap_or(0),
                            input_parser.selected_fixtures.clone(),
                        ),
                    );
                }
            }
            Instruction::IfStatement(e) => {
                let left_value = &e
                    .comparison
                    .left_value
                    .to_num(value, dmx_renderer, input_parser)
                    .unwrap_or(0);

                let right_value = &e
                    .comparison
                    .right_value
                    .to_num(value, dmx_renderer, input_parser)
                    .unwrap_or(0);

                if match e.comparison.comparator {
                    Comparator::Less => left_value < right_value,
                    Comparator::LessOrEqual => left_value <= right_value,
                    Comparator::Greater => left_value > right_value,
                    Comparator::GreaterOrEqual => left_value >= right_value,
                    Comparator::Equal => left_value == right_value,
                    Comparator::NotEqual => left_value != right_value,
                } {
                    evaluate_instructions(
                        input_parser,
                        dmx_renderer,
                        value,
                        &e.truthy_branch,
                        db_handler,
                        false,
                    );
                } else {
                    evaluate_instructions(
                        input_parser,
                        dmx_renderer,
                        value,
                        &e.falsy_branch,
                        db_handler,
                        false,
                    );
                }
            }
            Instruction::LoopStatement(e) => {
                let left_value = &e
                    .comparison
                    .left_value
                    .to_num(value, dmx_renderer, input_parser)
                    .unwrap_or(0);

                let right_value = &e
                    .comparison
                    .right_value
                    .to_num(value, dmx_renderer, input_parser)
                    .unwrap_or(0);

                if match e.comparison.comparator {
                    Comparator::Less => left_value < right_value,
                    Comparator::LessOrEqual => left_value <= right_value,
                    Comparator::Greater => left_value > right_value,
                    Comparator::GreaterOrEqual => left_value >= right_value,
                    Comparator::Equal => left_value == right_value,
                    Comparator::NotEqual => left_value != right_value,
                } {
                    evaluate_instructions(
                        input_parser,
                        dmx_renderer,
                        value,
                        &e.content,
                        db_handler,
                        true,
                    );
                    input_parser.append_upcomming_instructions_to_loop = true;
                    input_parser.loop_with_tail_instructions.push(
                        LoopWithTailInstructions {
                            loop_instruction: Instruction::LoopStatement(
                                LoopStatement {
                                    comparison: e.comparison.clone(),
                                    content: e.content.clone(),
                                },
                            ),
                            selected_fixtures: input_parser
                                .selected_fixtures
                                .clone(),
                            tail_instructions: vec![],
                            value,
                        },
                    );
                }
            }
            Instruction::IfQueueAllowsContinue(childs) => {
                match input_parser.queue_mode {
                    QueueMode::Queue => {
                        for child in childs {
                            input_parser.instruction_queue.push(
                                QueuedInstruction {
                                    instruction: child.clone(),
                                    value,
                                },
                            );
                        }
                    }
                    QueueMode::Flush => evaluate_instructions(
                        input_parser,
                        dmx_renderer,
                        value,
                        childs,
                        db_handler,
                        false,
                    ),
                }
            }
            Instruction::ClearBlueprint(blueprint_id) => {
                dmx_renderer.clear_splines_by_blueprint_id(&input_parser.selected_fixtures, *blueprint_id);
            }
            Instruction::StartRecording => {
                input_parser.loopstation.start_recording();
            }
            Instruction::StopRecording => {
                input_parser.loopstation.stop_recording();
            }
            Instruction::ClearRecording => {
                input_parser.loopstation.clear_recording();
            }
            Instruction::Nop => (),
        }
    }
}
fn position_to(
    position_id: usize,
    db_handler: &DbHandler,
    dmx_renderer: &mut DmxRenderer,
    input_parser: &InputParser,
) {
    if let Some(position) = db_handler
        .active_show
        .positions
        .iter()
        .find(|iter_position| iter_position.id == position_id)
    {
        dmx_renderer
            .set_pan_tilt_position(&input_parser.selected_fixtures, position);
    }
}
fn color_to(
    value: u8,
    input_parser: &InputParser,
    dmx_renderer: &mut DmxRenderer,
    color_to_instruction: &ColorToInstruction,
) {
    dmx_renderer.set_color(
        &input_parser.selected_fixtures,
        color_to_instruction.color,
        color_to_instruction.fade_duration.clone().and_then(
            |fade_duration_instruction| {
                fade_duration_instruction.to_num(
                    value,
                    dmx_renderer,
                    input_parser,
                )
            },
        ),
    );
}

fn color_to_random(
    value: u8,
    input_parser: &InputParser,
    dmx_renderer: &mut DmxRenderer,
    fade_duration: Option<InstructionValue>,
) {
    dmx_renderer.set_color(
        &input_parser.selected_fixtures,
        RgbColor::new_random(),
        fade_duration.map(|fade_duration_instruction| {
            fade_duration_instruction
                .to_num(value, dmx_renderer, input_parser)
                .unwrap_or_default()
        }),
    );
}
fn blueprint_to(
    input_parser: &InputParser,
    id: usize,
    oneshot: bool,
    dmx_renderer: &mut DmxRenderer,
    db_handler: &DbHandler,
) {
    for blueprint in &db_handler.active_show.blueprints {
        if blueprint.id == id {
            dmx_renderer.apply_blueprint(
                &input_parser.selected_fixtures,
                oneshot,
                blueprint,
                &db_handler.active_show.positions,
            );
        }
    }
}
fn timecode_to(
    input_parser: &InputParser,
    id: usize,
    index: usize,
    timecode_advancing: bool,
    dmx_renderer: &mut DmxRenderer,
    db_handler: &mut DbHandler,
) {
    let timecode_to_apply = &db_handler
        .active_show
        .timecodes
        .iter()
        .find(|timecode| timecode.id == id)
        .cloned();

    if let Some(timecode) = timecode_to_apply {
        dmx_renderer.apply_timecode(
            &input_parser.selected_fixtures,
            timecode,
            index,
            timecode_advancing,
            db_handler,
        );
    }
}
fn activate_all_fixtures(
    input_parser: &mut InputParser,
    db_handler: &DbHandler,
) {
    for dmx_fixture in &db_handler.active_show.fixtures {
        if let Some(id) = dmx_fixture.id {
            input_parser.selected_fixtures.push(ActivatedFixture {
                id,
                name: dmx_fixture.name.clone(),
                fixturetype: dmx_fixture.fixturetype.clone(),
            });
        }
    }
}
fn activate_fixture_by_id(
    input_parser: &mut InputParser,
    db_handler: &DbHandler,
    fx_id: usize,
) {
    for dmx_fixture in &db_handler.active_show.fixtures {
        if let Some(dmx_fixture_id) = dmx_fixture.id {
            if dmx_fixture_id == fx_id {
                if let Some(id) = dmx_fixture.id {
                    input_parser.selected_fixtures.push(ActivatedFixture {
                        id,
                        name: dmx_fixture.name.clone(),
                        fixturetype: dmx_fixture.fixturetype.clone(),
                    });
                }
            }
        }
    }
}
fn activate_fixture_group(
    input_parser: &mut InputParser,
    db_handler: &DbHandler,
    needle_group_name: &str,
) {
    let Some(group) = db_handler
        .active_show
        .enriched_groups()
        .into_iter()
        .find(|group| group.name.as_str() == needle_group_name)
    else {
        logging::log(
            format!("No group `{needle_group_name}` found"),
            logging::LogLevel::Warning,
            false,
        );
        return;
    };
    let appendable_fixtures: Vec<usize> = group
        .fixture_ids
        .iter()
        .filter(|group_fixture_id| {
            db_handler
                .active_show
                .fixtures
                .iter()
                .map(|show_fixture| show_fixture.id)
                .any(|show_fixture_id| {
                    show_fixture_id == Some(**group_fixture_id)
                })
        })
        .copied()
        .collect();

    let mut mapped_appendable_fixtures = appendable_fixtures
        .iter()
        .map(|fixture_id| {
            db_handler
                .active_show
                .fixtures
                .iter()
                .find(|show_fixture_id| show_fixture_id.id == Some(*fixture_id))
        })
        .filter(Option::is_some)
        .map(|show_fixture| {
            show_fixture.map_or_else(
                || {
                    logging::log(
                        format!("show-fixture was faulty while activating from group"),
                        logging::LogLevel::Warning,
                        false,
                    );
                    ActivatedFixture {
                        id: 0,
                        name: String::from("-"),
                        fixturetype: String::from("-"),
                    }
                },
                |show_fixture| {
                    show_fixture.id.map_or_else(|| {
                            logging::log(
                                format!("show-fixture was faulty while activating from group"),
                                logging::LogLevel::Warning,
                                false,
                            );
                            ActivatedFixture {
                                id: 0,
                                name: String::from("-"),
                                fixturetype: String::from("-"),
                            }
                        },
                        |id| {
                            ActivatedFixture {
                                id,
                                name: show_fixture.name.clone(),
                                fixturetype: show_fixture.fixturetype.clone(),
                            }
                        }
                    )}
            )
        })
        .collect();

    input_parser
        .selected_fixtures
        .append(&mut mapped_appendable_fixtures);
}
fn set_blueprint_position_index_offset_mode(
    input_parser: &InputParser,
    position_index_offset_mode: PositionIndexOffsetMode,
    dmx_renderer: &mut DmxRenderer,
) {
    dmx_renderer.set_position_index_offset_mode(
        &input_parser.selected_fixtures,
        position_index_offset_mode,
    );
}

fn set_speed_of_blueprints(
    value: u8,
    input_parser: &InputParser,
    dmx_renderer: &mut DmxRenderer,
    speed_value: &InstructionValue,
) {
    let speed = speed_value
        .to_num(value, dmx_renderer, input_parser)
        .unwrap_or(100);

    dmx_renderer
        .set_speed_of_blueprints(&input_parser.selected_fixtures, speed);
}
