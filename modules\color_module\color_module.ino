#include <Adafruit_NeoPixel.h>
#include "rw_utils.h"

#define BUTTON_COUNT 11

Adafruit_NeoPixel pixels = Adafruit_NeoPixel(BUTTON_COUNT, A2, NEO_GRB + NEO_KHZ800);

Button buttons[11] = {
  <PERSON><PERSON>(10, 7, 0, {150, 150, 0}),
  <PERSON><PERSON>(10, 8, 1, {0, 150, 0}),
  <PERSON><PERSON>(11, 6, 2, {200, 100, 0}),
  <PERSON><PERSON>(11, 7, 3, {50, 50, 50}),//Todo: rainbow
  <PERSON><PERSON>(11, 8, 4, {100, 100, 100}),
  <PERSON><PERSON>(11, 9, 9, {0, 120, 100}),
  <PERSON><PERSON>(12, 6, 10, {180, 0, 0}),
  <PERSON><PERSON>(12, 7, 7, {100, 30, 80}),
  <PERSON><PERSON>(12, 8, 8, {90, 30, 90}),
  <PERSON><PERSON>(12, 9, 5, {0, 0, 150}),
  <PERSON><PERSON>(4, 2, 6, {20, 20, 20}),
};

Fader fader = Fader(A1, 3, 5);

void setup() {
  Serial.begin(250000);

  pinMode(10, OUTPUT);
  pinMode(11, OUTPUT);
  pinMode(12, OUTPUT);
  digitalWrite(10, HIGH);
  digitalWrite(11, HIGH);
  digitalWrite(12, HIGH);

  pinMode(4, OUTPUT);
  digitalWrite(4, LOW);
  pinMode(2, INPUT_PULLUP);

  pinMode(6, INPUT_PULLUP);
  pinMode(7, INPUT_PULLUP);
  pinMode(8, INPUT_PULLUP);
  pinMode(9, INPUT_PULLUP);

  pinMode(A1, INPUT);

  pinMode(3, OUTPUT);
  pinMode(5, OUTPUT);

  pixels.begin();
}

void loop() {
  Serial.write(2);

  bool blink = false;
  if (millis() % 500 < 250) {
    blink = true;
  } else {
    blink = false;
  }

  // yellow
  buttons[0].tick([]() {
    Serial.write(25);
    Serial.write(1);
    reset_color_button_leds();
    buttons[0].update_led_state([](int old_state) {
      return LED_BLINK;
    });
  }, []() {}, &pixels, blink);

  // green
  buttons[1].tick([]() {
    Serial.write(24);
    Serial.write(1);
    reset_color_button_leds();
    buttons[1].update_led_state([](int old_state) {
      return LED_BLINK;
    });
  }, []() {}, &pixels, blink);

  // orange
  buttons[2].tick([]() {
    Serial.write(21);
    Serial.write(1);
    reset_color_button_leds();
    buttons[2].update_led_state([](int old_state) {
      return LED_BLINK;
    });
  }, []() {}, &pixels, blink);

  // white
  buttons[4].tick([]() {
    Serial.write(26);
    Serial.write(1);
    reset_color_button_leds();
    buttons[4].update_led_state([](int old_state) {
      return LED_BLINK;
    });
  }, []() {}, &pixels, blink);

  // light blue
  buttons[5].tick([]() {
    Serial.write(27);
    Serial.write(1);
    reset_color_button_leds();
    buttons[5].update_led_state([](int old_state) {
      return LED_BLINK;
    });
  }, []() {}, &pixels, blink);

  // red
  buttons[6].tick([]() {
    Serial.write(20);
    Serial.write(1);
    reset_color_button_leds();
    buttons[6].update_led_state([](int old_state) {
      return LED_BLINK;
    });
  }, []() {}, &pixels, blink);

  // pink
  buttons[7].tick([]() {
    Serial.write(28);
    Serial.write(1);
    reset_color_button_leds();
    buttons[7].update_led_state([](int old_state) {
      return LED_BLINK;
    });
  }, []() {}, &pixels, blink);

  // violet
  buttons[8].tick([]() {
    Serial.write(22);
    Serial.write(1);
    reset_color_button_leds();
    buttons[8].update_led_state([](int old_state) {
      return LED_BLINK;
    });
  }, []() {}, &pixels, blink);

  // blue
  buttons[9].tick([]() {
    Serial.write(23);
    Serial.write(1);
    reset_color_button_leds();
    buttons[9].update_led_state([](int old_state) {
      return LED_BLINK;
    });
  }, []() {}, &pixels, blink);

  // rainbow
  buttons[3].tick([]() {
    Serial.write(29);
    Serial.write(1);
    reset_color_button_leds();
    buttons[3].update_led_state([](int old_state) {
      return LED_BLINK;
    });
  }, []() {}, &pixels, blink);

  // color transition mode
  buttons[10].tick([]() {
    Serial.write(30);
    Serial.write(1);
    buttons[10].update_led_state([](int old_state) {
      fader.set_new_dest(random(256));
      return LED_BLINK;
    });
  }, []() {
    buttons[10].update_led_state([](int old_state) {
      return LED_ON;
    });
  }, &pixels, blink);

  fader.tick([](int p_position) {
    Serial.write(31);
    Serial.write(p_position);
  });

  pixels.show();
}

void reset_color_button_leds() {
  for ( int i = 0; i < BUTTON_COUNT - 1; i++ ) {
    buttons[i].update_led_state([](int old_state) {
      return LED_ON;
    });
  }
}
