<script lang="ts">
	import Numberinput from '$lib/atoms/numberinput.svelte';

	let {
		value = $bindable(),
		label,
		placeholder,
		min,
		max,
		onchange,
	}: {
		value: number;
		label: string;
		placeholder?: string;
		min?: number;
		max?: number;
		onchange?: (value: number) => void;
	} = $props();
</script>

<div class="text-sub flex flex-col">
	<label for="input">{label}</label>
	<Numberinput
		bind:value
		{placeholder}
		{min}
		{max}
		onchange={() => (onchange ? onchange(value) : null)}
	/>
</div>
