/** @type {import('tailwindcss').Config} */
export default {
    content: ['./src/**/*.{html,js,svelte,ts}'],
    theme: {
        extend: {
            colors: {
                success: '#28a745',
                error: '#dc3545',
                warning: '#ffc107',
                info: '#17a2b8',
            },
            height: {
                'navbar': '4rem'
            },
            spacing: {
                'navbar': '4rem'
            },
            animation: {
                'ping-once': 'ping 1s',
                'shake-once': 'shake 0.7s',
                'bounce-left': 'bounceLeft 0.8s infinite',
                'bounce-right': 'bounceRight 0.8s infinite',
                'in': 'fadeIn 0.2s ease-out',
                'slide-in-from-top-2': 'slideInFromTop 0.2s ease-out',
                'slide-in-from-bottom-2': 'slideInFromBottom 0.2s ease-out',
                'slide-in-from-left-2': 'slideInFromLeft 0.2s ease-out',
                'slide-in-from-right-2': 'slideInFromRight 0.2s ease-out'
            },
            keyframes: {
                shake: {
                    '0%': { transform: 'rotate(0.0deg)' },
                    '10%': { transform: 'rotate(14deg)' },
                    '20%': { transform: 'rotate(-8deg)' },
                    '30%': { transform: 'rotate(14deg)' },
                    '40%': { transform: 'rotate(-4deg)' },
                    '50%': { transform: 'rotate(10.0deg)' },
                    '60%': { transform: 'rotate(0.0deg)' },
                    '100%': { transform: 'rotate(0.0deg)' },
                },
                fadeIn: {
                    '0%': { opacity: '0', transform: 'scale(0.95)' },
                    '100%': { opacity: '1', transform: 'scale(1)' }
                },
                slideInFromTop: {
                    '0%': { opacity: '0', transform: 'translateY(-8px) scale(0.95)' },
                    '100%': { opacity: '1', transform: 'translateY(0) scale(1)' }
                },
                slideInFromBottom: {
                    '0%': { opacity: '0', transform: 'translateY(8px) scale(0.95)' },
                    '100%': { opacity: '1', transform: 'translateY(0) scale(1)' }
                },
                slideInFromLeft: {
                    '0%': { opacity: '0', transform: 'translateX(-8px) scale(0.95)' },
                    '100%': { opacity: '1', transform: 'translateX(0) scale(1)' }
                },
                slideInFromRight: {
                    '0%': { opacity: '0', transform: 'translateX(8px) scale(0.95)' },
                    '100%': { opacity: '1', transform: 'translateX(0) scale(1)' }
                },
                bounceLeft: {
                    '0%': {
                        transform: 'translateX(-50%)',
                        'animation-timing-function': 'animation-timing-function: cubic-bezier(0, 0, 0.2, 1)'
                    },
                    '50%': {
                        transform: 'translateX(0)',
                        'animation-timing-function': 'cubic-bezier(0.8, 0, 1, 1)'
                    },
                    '100%': {
                        transform: 'translateX(-50%)',
                        'animation-timing-function': 'animation-timing-function: cubic-bezier(0, 0, 0.2, 1)'
                    },
                },
                bounceRight: {
                    '0%': {
                        transform: 'translateX(+50%)',
                        'animation-timing-function': 'animation-timing-function: cubic-bezier(0, 0, 0.2, 1)'
                    },
                    '50%': {
                        transform: 'translateX(0)',
                        'animation-timing-function': 'cubic-bezier(0.8, 0, 1, 1)'
                    },
                    '100%': {
                        transform: 'translateX(+50%)',
                        'animation-timing-function': 'animation-timing-function: cubic-bezier(0, 0, 0.2, 1)'
                    },
                }
            }
        }
    },
    darkMode: 'selector',
    plugins: [
        require('tailwindcss-themer')({
            themes: [
                {
                    name: 'dark',
                    extend: {
                        borderColor: {
                            input: '#4B4B4B',
                            selected: '#BB86FA'
                        },
                        textColor: {
                            primary: '#FFFFFF',
                            secondary: '#F3F3F3',
                            complementary: '#000000',
                            disabled: '#2A2C2F',
                        },
                        colors: {
                            primary: '#000000',
                            surface: '#121212',
                            object: '#2A2C2F',
                            accent: '#BB86FA',
                            input: '#3B3B3B'
                        }
                    }
                },
                {
                    name: 'light',
                    extend: {
                        borderColor: {
                            input: '#D1D5DB',
                            selected: '#0051FB',
                        },
                        textColor: {
                            primary: '#000000',
                            secondary: '#202020',
                            complementary: '#FFFFFF',
                            disabled: '#A6AAB2',
                        },
                        colors: {
                            primary: '#F3F4F6',
                            surface: '#FFFFFF',
                            object: '#FFFFFF',
                            accent: '#0051FB',
                            input: '#FFFFFF'
                        }
                    }
                },
                {
                    name: 'solar',
                    extend: {
                        borderColor: {
                            input: '#D1D5DB',
                            selected: '#268BD2',
                        },
                        textColor: {
                            primary: '#002B36',
                            secondary: '#EEE8D5',
                            complementary: '#002B36',
                            disabled: '#839496',
                        },
                        colors: {
                            primary: '#002B36',
                            surface: '#EEE8D5',
                            object: '#EEE8D5',
                            accent: '#B58900',
                            input: '#FDF6E3'
                        }
                    }
                },
                {
                    name: 'catpuccin',
                    extend: {
                        borderColor: {
                            input: '#505050',
                            selected: '#A68164',
                        },
                        textColor: {
                            primary: '#FFFFFF',
                            secondary: '#C3C3C3',
                            complementary: '#303446',
                            disabled: '#505050',
                        },
                        colors: {
                            primary: '#4A342A',
                            surface: '#331E11',
                            object: '#45475A',
                            accent: '#A68164',
                            input: '#313244'
                        }
                    }
                },
            ]
        })
    ]
};
