<script lang="ts">
    import Button from "$lib/atoms/button.svelte";
    import Numberinput from "$lib/atoms/numberinput.svelte";
    import { fixtures } from "$lib/stores/fixtures";
    import { fixtureTypes } from "$lib/stores/fixtureTypes";
    import {
        allLeafesOf,
        appendSnippetEditingReason,
    } from "$lib/stores/snippets";
    import type { DmxFixtureFileDescriptor } from "$lib/types/bindings/DmxFixtureFileDescriptor";
    import Icon from "$lib/atoms/icon.svelte";
    import { TOAST } from "$lib/stores/toast";

    let {
        editFixture,
    }: {
        editFixture: (fixture: DmxFixtureFileDescriptor) => void;
    } = $props();

    let needsSaving = $state(false);
    let showingMakeTypeModal = $state(false);
    let selectedFixtureForType: DmxFixtureFileDescriptor | null = $state(null);
    let newFixtureTypeName = $state("");

    function deleteFixture(fixture: DmxFixtureFileDescriptor) {
        fixtures.delete(fixture);
    }

    function showMakeTypeModal(fixture: DmxFixtureFileDescriptor) {
        selectedFixtureForType = fixture;
        newFixtureTypeName = `${fixture.fixturetype} Type`;
        showingMakeTypeModal = true;
    }

    function closeMakeTypeModal() {
        showingMakeTypeModal = false;
        selectedFixtureForType = null;
        newFixtureTypeName = "";
    }

    async function createTypeFromFixture() {
        if (!selectedFixtureForType || !newFixtureTypeName.trim()) {
            TOAST.error("Please provide a name for the new fixture type");
            return;
        }

        if (selectedFixtureForType.id) {
            await fixtureTypes.createFromFixture(selectedFixtureForType.id, newFixtureTypeName.trim());
            closeMakeTypeModal();
        }
    }
</script>

<h2 class="flex justify-center">existing fixtures:</h2>
<div class="w-fit">
    <Button
        id="save-existing-fixtures"
        onclick={async () => {
            fixtures.updateRemote();
            needsSaving = false;
        }}
    >
        <div class:text-warning={needsSaving} class:text-success={!needsSaving}>
            <Icon icon="material-symbols:save"></Icon>
        </div>
    </Button>
</div>
<div class="flex justify-center">
    <div class="flex flex-col">
        {#if $fixtures && $fixtures.length}
            <table class="w-3/4">
                <thead class="border-b">
                    <tr>
                        <th scope="col" class="px-6 py-4 text-left text-sm">
                            ID
                        </th>
                        <th scope="col" class="px-6 py-4 text-left text-sm">
                            Type
                        </th>
                        <th scope="col" class="px-6 py-4 text-left text-sm">
                            Name
                        </th>
                        <th scope="col" class="px-6 py-4 text-left text-sm">
                            Universe
                        </th>
                        <th scope="col" class="px-6 py-4 text-left text-sm">
                            Address
                        </th>
                        <th scope="col" class="px-6 py-4 text-left text-sm">
                            Edit
                        </th>
                        <th scope="col" class="px-6 py-4 text-left text-sm">
                            Make Type
                        </th>
                        <th scope="col" class="px-6 py-4 text-left text-sm">
                            Delete
                        </th>
                    </tr>
                </thead>
                <tbody>
                    {#each $fixtures as fixture, index}
                        <tr class="cursor-pointer border-b transition">
                            <td class="whitespace-nowrap px-6 py-4 text-sm"
                                >{fixture.id}</td
                            >
                            <td class="whitespace-nowrap px-6 py-4 text-sm"
                                >{fixture.fixturetype}</td
                            >
                            <td class="whitespace-nowrap px-6 py-4 text-sm">
                                {fixture.name}
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm">
                                <Numberinput
                                    bind:value={fixture.dmx_universe}
                                    min={1}
                                    onchange={() => (needsSaving = true)}
                                    max={65535}
                                ></Numberinput>
                            </td>
                            <td class="flex px-6 py-4 text-sm">
                                <div class="grow">
                                    <Numberinput
                                        bind:value={fixture.dmx_address}
                                        min={1}
                                        onchange={() => (needsSaving = true)}
                                        max={512}
                                    ></Numberinput>
                                </div>

                                <Button
                                    id="fill-dmx-address"
                                    onclick={() => {
                                        if (index > 0) {
                                            fixture.dmx_address =
                                                ($fixtures[index - 1]
                                                    ?.dmx_address ?? 0) +
                                                $fixtures[index - 1]
                                                    ?.footprint_size;
                                        } else {
                                            fixture.dmx_address = 1;
                                        }
                                        needsSaving = true;
                                    }}
                                >
                                    <Icon icon="mdi:wand"></Icon>
                                </Button>
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm">
                                <Button
                                    id="load-fixture-into-form"
                                    onclick={() => {
                                        editFixture(fixture);
                                    }}
                                >
                                    <Icon icon="solar:pen-bold"></Icon>
                                </Button>
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm">
                                <Button
                                    id="make-fixture-type-{fixture.id}"
                                    onclick={() => showMakeTypeModal(fixture)}
                                >
                                    <Icon icon="mdi:content-copy"></Icon>
                                </Button>
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm">
                                <Button
                                    id="delete-fixture"
                                    onclick={() => {
                                        appendSnippetEditingReason(
                                            (snippet) =>
                                                snippet.instructions
                                                    .flatMap((instruction) =>
                                                        allLeafesOf(
                                                            instruction,
                                                        ),
                                                    )
                                                    .some((instruction) => {
                                                        if (
                                                            "instruction" in
                                                            instruction
                                                        ) {
                                                            let result = false;
                                                            if (
                                                                "ActivateFixtureById" ===
                                                                instruction.instruction
                                                            ) {
                                                                result =
                                                                    instruction.instructionMod ===
                                                                    fixture.id;
                                                            } else if (
                                                                "UnimplementedChannelTo" ===
                                                                instruction.instruction
                                                            ) {
                                                                result =
                                                                    fixture.unimplemented_channels.some(
                                                                        (
                                                                            newChannel,
                                                                        ) =>
                                                                            instruction
                                                                                .instructionMod
                                                                                .name ===
                                                                            newChannel.name,
                                                                    );
                                                            }
                                                            return result;
                                                        }
                                                        return false;
                                                    }),
                                            `deleted fixture ${fixture.name}`,
                                        );

                                        deleteFixture(fixture);
                                    }}
                                >
                                    <Icon icon="mdi:garbage-can-empty"></Icon>
                                </Button>
                            </td>
                        </tr>
                    {/each}
                </tbody>
            </table>
        {/if}
    </div>
</div>

{#if showingMakeTypeModal && selectedFixtureForType}
    <div class="modal-overlay" onclick={closeMakeTypeModal}>
        <div class="modal-content" onclick={(e) => e.stopPropagation()}>
            <div class="modal-header">
                <h3>Create Fixture Type from "{selectedFixtureForType.name}"</h3>
                <Button onclick={closeMakeTypeModal}>
                    <Icon icon="mdi:close" />
                </Button>
            </div>
            
            <div class="modal-body">
                <p>This will create a new fixture type based on the configuration of "{selectedFixtureForType.name}".</p>
                <p>You can then use this type to create multiple fixtures with the same DMX footprint.</p>
                
                <div class="form-group">
                    <label for="new-type-name">Fixture Type Name:</label>
                    <input 
                        id="new-type-name"
                        type="text" 
                        bind:value={newFixtureTypeName}
                        class="form-input"
                        placeholder="Enter fixture type name"
                    />
                </div>
            </div>
            
            <div class="modal-footer">
                <Button onclick={closeMakeTypeModal}>Cancel</Button>
                <Button onclick={createTypeFromFixture} disabled={!newFixtureTypeName.trim()}>
                    <Icon icon="mdi:content-copy" />
                    Create Fixture Type
                </Button>
            </div>
        </div>
    </div>
{/if}

<style>
    .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1000;
    }
    
    .modal-content {
        background: var(--color-bg-object);
        border-radius: 8px;
        max-width: 500px;
        width: 90%;
        max-height: 80vh;
        overflow-y: auto;
    }
    
    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1.5rem;
        border-bottom: 1px solid var(--color-border);
    }
    
    .modal-body {
        padding: 1.5rem;
    }
    
    .modal-footer {
        display: flex;
        justify-content: flex-end;
        gap: 1rem;
        padding: 1.5rem;
        border-top: 1px solid var(--color-border);
    }
    
    .form-group {
        margin-top: 1rem;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
    }
    
    .form-input {
        width: 100%;
        padding: 0.5rem;
        border: 1px solid var(--color-border);
        border-radius: 4px;
        background: var(--color-bg-primary);
        color: var(--color-text);
    }
</style>
