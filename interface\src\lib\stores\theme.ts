import { writable, get } from 'svelte/store';

function createThemeStore() {
    const { subscribe, set } = writable<String>('dark');

    set(loadThemeFromLocalStorage())

    return {
        subscribe,
        cycle: () => set(cycleThemes())
    };
}

export const theme = createThemeStore();

function loadThemeFromLocalStorage() {
    if ('theme' in localStorage) {
        if (localStorage.theme === 'dark') {
            document.documentElement.classList.add('dark');
            return 'dark';
        } else if (localStorage.theme === 'light') {
            document.documentElement.classList.add('light');
            return 'light';
        } else if (localStorage.theme === 'solar') {
            document.documentElement.classList.add('solar');
            return 'solar';
        } else if (localStorage.theme === 'catpuccin') {
            document.documentElement.classList.add('catpuccin');
            return 'catpuccin';
        }
    } else if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
        document.documentElement.classList.add('solar');
        localStorage.theme = 'solar';
        return 'solar';
    } else {
        document.documentElement.classList.add('light');
        localStorage.theme = 'light';
        return 'light';
    }
    return 'dark'
}

function cycleThemes() {
    const currentTheme = get(theme)
    if (currentTheme === 'dark') {
        document.documentElement.classList.remove('dark');
        document.documentElement.classList.add('light');
        localStorage.theme = 'light';
        return 'light';
    } else if (currentTheme === 'light') {
        document.documentElement.classList.remove('light');
        document.documentElement.classList.add('solar');
        localStorage.theme = 'solar';
        return 'solar';
    } else if (currentTheme === 'solar') {
        document.documentElement.classList.remove('solar');
        document.documentElement.classList.add('catpuccin');
        localStorage.theme = 'catpuccin';
        return 'catpuccin';
    } else if (currentTheme === 'catpuccin') {
        document.documentElement.classList.remove('catpuccin');
        document.documentElement.classList.add('dark');
        localStorage.theme = 'dark';
        return 'dark';
    }
    return 'dark'
}
