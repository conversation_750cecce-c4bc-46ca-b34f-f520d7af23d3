CREATE TABLE shows (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VA<PERSON><PERSON><PERSON>(255),
    active BOOL
);

CREATE TABLE audiofiles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255),
    bytes LONGBLOB,
    show_id INT,
    FOREIGN KEY (show_id) REFERENCES shows(id) ON DELETE SET NULL
);

CREATE TABLE fixtures (
    id INT PRIMARY KEY AUTO_INCREMENT,
    dmx_address INT,
    dmx_universe INT,
    name VARCHAR(255),
    fixturetype VARCHAR(255),
    stage_coordinate_x INT,
    stage_coordinate_y INT,
    footprint_size INT,
    show_id INT,
    FOREIGN KEY (show_id) REFERENCES shows(id) ON DELETE SET NULL
);

CREATE TABLE dmx_channels (
    id INT PRIMARY KEY AUTO_INCREMENT,
    dmx_offset INT,
    name VARCHAR(255),
    dimmable BOOL,
    default_value INT,
    fixture_id INT,
    FOREIG<PERSON> KEY (fixture_id) REFERENCES fixtures(id) ON DELETE CASCADE
);

CREATE TABLE rgb_color_channels (
    id INT PRIMARY KEY AUTO_INCREMENT,
    red_channel_offset INT,
    green_channel_offset INT,
    blue_channel_offset INT,
    fixture_id INT,
    FOREIGN KEY (fixture_id) REFERENCES fixtures(id) ON DELETE CASCADE
);

CREATE TABLE cmy_color_channels (
    id INT PRIMARY KEY AUTO_INCREMENT,
    cyan_channel_offset INT,
    magenta_channel_offset INT,
    yellow_channel_offset INT,
    fixture_id INT,
    FOREIGN KEY (fixture_id) REFERENCES fixtures(id) ON DELETE CASCADE
);

CREATE TABLE hsv_color_channels (
    id INT PRIMARY KEY AUTO_INCREMENT,
    hue_channel_offset INT,
    saturation_channel_offset INT,
    value_channel_offset INT,
    fixture_id INT,
    FOREIGN KEY (fixture_id) REFERENCES fixtures(id) ON DELETE CASCADE
);

CREATE TABLE xyz_color_channels (
    id INT PRIMARY KEY AUTO_INCREMENT,
    x_channel_offset INT,
    y_channel_offset INT,
    z_channel_offset INT,
    fixture_id INT,
    FOREIGN KEY (fixture_id) REFERENCES fixtures(id) ON DELETE CASCADE
);

CREATE TABLE color_wheel_channel (
    id INT PRIMARY KEY AUTO_INCREMENT,
    channel_offset INT,
    fixture_id INT,
    FOREIGN KEY (fixture_id) REFERENCES fixtures(id) ON DELETE CASCADE
);

CREATE TABLE color_wheel_slot (
    id INT PRIMARY KEY AUTO_INCREMENT,
    channel_value INT,
    name VARCHAR(255),
    color_wheel_id INT,
    FOREIGN KEY (color_wheel_id) REFERENCES color_wheel_channel(id) ON DELETE CASCADE
);

CREATE TABLE timecodes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255),
    requires_user_action_reason VARCHAR(255),
    bpm INT,
    show_id INT,
    audiofile_id INT,
    FOREIGN KEY (show_id) REFERENCES shows(id) ON DELETE CASCADE,
    FOREIGN KEY (audiofile_id) REFERENCES audiofiles(id) ON DELETE CASCADE
);

CREATE TABLE tracks (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255),
    requires_user_action_reason VARCHAR(255),
    timecode_id INT,
    FOREIGN KEY (timecode_id) REFERENCES timecodes(id) ON DELETE CASCADE
);

CREATE TABLE blueprints (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255),
    requires_user_action_reason VARCHAR(255),
    show_id INT,
    FOREIGN KEY (show_id) REFERENCES shows(id) ON DELETE CASCADE
);

CREATE TABLE registered_fixture_delays (
    id INT PRIMARY KEY AUTO_INCREMENT,
    delay_by_eights INT,
    blueprint_id INT,
    fixture_id INT,
    FOREIGN KEY (fixture_id) REFERENCES fixtures(id) ON DELETE CASCADE,
    FOREIGN KEY (blueprint_id) REFERENCES blueprints(id) ON DELETE CASCADE
);

CREATE TABLE properties (
    id INT PRIMARY KEY AUTO_INCREMENT,
    variant VARCHAR(255),
    x_offset INT,
    blueprint_id INT,
    track_id INT,
    FOREIGN KEY (blueprint_id) REFERENCES blueprints(id) ON DELETE CASCADE,
    FOREIGN KEY (track_id) REFERENCES tracks(id) ON DELETE CASCADE
);

CREATE TABLE property_fixtures (
	id INT PRIMARY KEY AUTO_INCREMENT,
	property_id INT,
    fixture_id INT,
    FOREIGN KEY (property_id) REFERENCES properties(id) ON DELETE CASCADE,
    FOREIGN KEY (fixture_id) REFERENCES fixtures(id) ON DELETE CASCADE
);

CREATE TABLE pan_tilt_positions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255),
    show_id INT,
    FOREIGN KEY (show_id) REFERENCES shows(id) ON DELETE CASCADE
);

CREATE TABLE fixture_positions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    pan INT,
    tilt INT,
    fixture_id INT,
    pan_tilt_position_id INT,
    FOREIGN KEY (fixture_id) REFERENCES fixtures(id) ON DELETE CASCADE,
    FOREIGN KEY (pan_tilt_position_id) REFERENCES pan_tilt_positions(id) ON DELETE CASCADE
);

CREATE TABLE pan_tilt_position_property (
    id INT PRIMARY KEY AUTO_INCREMENT,
    x INT,
    position_id INT,
    property_id INT,
    FOREIGN KEY (position_id) REFERENCES pan_tilt_positions(id) ON DELETE CASCADE,
    FOREIGN KEY (property_id) REFERENCES properties(id) ON DELETE CASCADE
);

CREATE TABLE property_coordinates (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255),
    x INT,
    y INT,
    property_id INT,
    FOREIGN KEY (property_id) REFERENCES properties(id) ON DELETE CASCADE
);

CREATE TABLE color_property_coordinates (
    id INT PRIMARY KEY AUTO_INCREMENT,
    x INT,
    red INT,
    green INT,
    blue INT,
    property_id INT,
    FOREIGN KEY (property_id) REFERENCES properties(id) ON DELETE CASCADE
);

CREATE TABLE snippet_dirs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255),
    parent_dir_id INT,
    show_id INT,
    create_time TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3),
    FOREIGN KEY (parent_dir_id) REFERENCES snippet_dirs(id) ON DELETE CASCADE,
    FOREIGN KEY (show_id) REFERENCES shows(id) ON DELETE CASCADE
);

CREATE TABLE snippets (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255),
    category VARCHAR(255),
    do_not_use_instructions BOOL,
    serial_module_key INT,
    requires_user_action_reason VARCHAR(255),
    parent_dir_id INT,
    FOREIGN KEY (parent_dir_id) REFERENCES snippet_dirs(id) ON DELETE CASCADE
);

CREATE TABLE instructions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    branch VARCHAR(255),
    variant VARCHAR(255),
    int_val INT,
    str_val VARCHAR(255),
    bool_val_1 BOOL,
    bool_val_2 BOOL,
    snippet_id INT,
    parent_instruction_id INT,
    FOREIGN KEY (snippet_id) REFERENCES snippets(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_instruction_id) REFERENCES instructions(id) ON DELETE CASCADE
);

CREATE TABLE instruction_values (
    id INT PRIMARY KEY AUTO_INCREMENT,
    variant VARCHAR(255),
    branch VARCHAR(255),
    int_val INT,
    str_val VARCHAR(255),
    instruction_id INT,
    instruction_value_id INT,
    FOREIGN KEY (instruction_id) REFERENCES instructions(id) ON DELETE CASCADE,
    FOREIGN KEY (instruction_value_id) REFERENCES instruction_values(id) ON DELETE CASCADE
);
