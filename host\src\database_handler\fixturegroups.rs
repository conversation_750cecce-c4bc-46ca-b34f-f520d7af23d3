use super::<PERSON><PERSON><PERSON><PERSON><PERSON>;
use crate::logging;
use mysql::{prelude::Queryable, PooledConn};
use serde::{Deserialize, Serialize};
use ts_rs::TS;

#[derive(Serialize, Deserialize, TS, Clone, Debug)]
#[ts(export)]
pub struct FixtureGroup {
    pub id: Option<usize>,
    pub name: String,
    pub fixture_ids: Vec<usize>,
    pub immutable: bool,
}

impl DbHandler {
    pub fn fixturegroups_for_active_show(
        db_connection: &mut PooledConn,
    ) -> Vec<FixtureGroup> {
        let fixturegroups: Vec<(usize, String, bool)> = db_connection
            .query_map(
                "
                SELECT fg.id, fg.name, fg.immutable
                FROM fixturegroups fg
                JOIN shows s ON fg.show_id = s.id
                WHERE s.active = TRUE
                ",
                |result: (usize, String, bool)| result,
            )
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (selecting fixturegroups for active show)"),
                    logging::LogLevel::DbError,
                    true,
                );
                vec![]
            });

        fixturegroups
            .into_iter()
            .map(|(id, name, immutable)| {
                let fixture_ids: Vec<usize> = db_connection
                    .query_map(
                        format!(
                            "
                            SELECT fixture_id
                            FROM fixturegroup_fixtures
                            WHERE fixturegroup_id = {id}
                            ",
                        ),
                        |fixture_id: usize| fixture_id,
                    )
                    .unwrap_or_else(|err| {
                        logging::log(
                            format!("{err:?}\n        (selecting fixture IDs for fixturegroup {id})"),
                            logging::LogLevel::DbError,
                            true,
                        );
                        vec![]
                    });

                FixtureGroup {
                    id: Some(id),
                    name,
                    fixture_ids,
                    immutable,
                }
            })
            .collect()
    }

    pub fn create_fixturegroup(&mut self, fixturegroup: &FixtureGroup) {
        let active_show_id: Option<usize> = self
            .db_connection()
            .query_map(
                "SELECT id FROM shows WHERE active = TRUE",
                |id: usize| id,
            )
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (selecting active show ID)"),
                    logging::LogLevel::DbError,
                    true,
                );
                vec![]
            })
            .first()
            .copied();

        let Some(show_id) = active_show_id else {
            logging::log(
                "No active show found when creating fixturegroup".to_string(),
                logging::LogLevel::Warning,
                true,
            );
            return;
        };

        self.db_connection()
            .query_drop(format!(
                "
                INSERT INTO fixturegroups (name, show_id, immutable)
                VALUES ('{}', {}, {})
                ",
                fixturegroup.name, show_id, fixturegroup.immutable
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (inserting fixturegroup)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });

        let fixturegroup_id: Option<usize> = self
            .db_connection()
            .query_map(" SELECT MAX(id) FROM fixturegroups; ", |id: usize| id)
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (getting last inserted ID of fixturegroup)"),
                    logging::LogLevel::DbError,
                    true,
                );
                vec![]
            })
            .first()
            .copied();

        if let Some(id) = fixturegroup_id {
            for fixture_id in &fixturegroup.fixture_ids {
                self.db_connection()
                    .query_drop(format!(
                        "
                        INSERT INTO fixturegroup_fixtures (fixturegroup_id, fixture_id)
                        VALUES ({id}, {fixture_id})
                        ",
                    ))
                    .unwrap_or_else(|err| {
                        logging::log(
                            format!("{err:?}\n        (inserting fixturegroup-fixture relationship)"),
                            logging::LogLevel::DbError,
                            true,
                        );
                    });
            }
        }
        self.publish_changes();
        self.active_show.groups =
            Self::fixturegroups_for_active_show(&mut self.db_connection());
    }

    pub fn update_fixturegroup(&mut self, fixturegroup: &FixtureGroup) {
        let Some(id) = fixturegroup.id else {
            logging::log(
                format!("Cannot update generated group {}", fixturegroup.name),
                logging::LogLevel::Warning,
                true,
            );
            return;
        };

        self.db_connection()
            .query_drop(format!(
                "
                UPDATE fixturegroups
                SET name = '{}', immutable = {}
                WHERE id = {}
                ",
                fixturegroup.name, fixturegroup.immutable, id
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (updating fixturegroup)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });

        self.db_connection()
            .query_drop(format!(
                "
                DELETE FROM fixturegroup_fixtures
                WHERE fixturegroup_id = {id}
                ",
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (deleting fixturegroup-fixture relationships)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });

        for fixture_id in &fixturegroup.fixture_ids {
            self.db_connection()
                .query_drop(format!(
                    "
                    INSERT INTO fixturegroup_fixtures (fixturegroup_id, fixture_id)
                    VALUES ({id}, {fixture_id})
                    ",
                ))
                .unwrap_or_else(|err| {
                    logging::log(
                        format!("{err:?}\n        (inserting fixturegroup-fixture relationship)"),
                        logging::LogLevel::DbError,
                        true,
                    );
                });
        }

        self.publish_changes();
        self.active_show.groups =
            Self::fixturegroups_for_active_show(&mut self.db_connection());
    }

    pub fn delete_fixturegroup(&mut self, id: usize) {
        self.db_connection()
            .query_drop(format!(
                "
                DELETE FROM fixturegroups
                WHERE id = {id}
                ",
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (deleting fixturegroup)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });

        self.publish_changes();
        self.active_show.groups =
            Self::fixturegroups_for_active_show(&mut self.db_connection());
    }
}
