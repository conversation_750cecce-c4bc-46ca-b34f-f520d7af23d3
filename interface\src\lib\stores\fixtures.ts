import { writable } from 'svelte/store';
import type { DmxFixtureFileDescriptor } from '../types/bindings/DmxFixtureFileDescriptor';
import { get } from 'svelte/store';
import { networking } from './networking';

function createFixtureStore() {
    const { subscribe, set, update } = writable<DmxFixtureFileDescriptor[]>([]);

    return {
        subscribe,
        set,
        updateLocal: update,
        updateRemote: async () => await updateRemote(),
        create: (fixtures: DmxFixtureFileDescriptor[]) =>
            createFixtures(fixtures),
        delete: (fixture: DmxFixtureFileDescriptor) => deleteFixture(fixture),
    };
}

export const fixtures = createFixtureStore();

function updateRemote(): Promise<null> {
    return new Promise((resolve, _) => {
        const fxs = get(fixtures);
        fxs.forEach(async fixture => {
            await fetch(`http://${get(networking)}:${networking.port}/fixture`, {
                method: 'PATCH',
                body: JSON.stringify(fixture),
                headers: new Headers({ 'content-type': 'application/json' }),
            });
        });
        resolve(null)
    })
}

function createFixtures(fixtures: DmxFixtureFileDescriptor[]) {
    fixtures.forEach(async fixture => {
        fetch(`http://${get(networking)}:${networking.port}/fixture`, {
            method: 'POST',
            body: JSON.stringify(fixture),
            headers: new Headers({ 'content-type': 'application/json' }),
        });
    });
}

async function deleteFixture(fixture: DmxFixtureFileDescriptor) {
    fetch(`http://${get(networking)}:${networking.port}/fixture/${fixture.id}`, {
        method: 'DELETE',
    });
}
