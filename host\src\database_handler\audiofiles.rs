use std::fs::{create_dir_all, read_dir, OpenOptions};
use std::io::Write;
use std::path::Path;

use crate::logging;

use super::<PERSON><PERSON><PERSON><PERSON><PERSON>;
use mysql::{prelude::*, PooledConn};

const AUDIO_FILES_PATH: &str = "audiofiles/";

impl DbHandler {
    pub fn save_audiofile(&mut self, filename: &String, file_content: &[u8]) {
        self.db_connection()
            .query_drop(format!(
                "
                INSERT INTO
                audiofiles (name, show_id)
                VALUES (
                    '{filename}',
                    (SELECT id FROM shows WHERE active)
                )
            "
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (inserting into audiofiles)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        let _ = create_dir_all(AUDIO_FILES_PATH);
        if let Ok(mut file) = OpenOptions::new()
            .write(true)
            .create_new(true)
            .open(format!("{AUDIO_FILES_PATH}{filename}"))
        {
            if file.write_all(file_content).is_err() {
                logging::log(
                    "failed to write to audiofile.".to_string(),
                    logging::LogLevel::Warning,
                    false,
                );
            }
        } else {
            logging::log(
                "failed to create audiofile. Does it already exist?"
                    .to_string(),
                logging::LogLevel::Warning,
                false,
            );
        }
    }
    #[must_use]
    pub fn available_audiofile_names(&mut self) -> Vec<(usize, String)> {
        let mut result = vec![];
        self.db_connection()
            .query_map(
                "
                SELECT id, name FROM audiofiles
            ",
                |(id, name)| result.push((id, name)),
            )
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (enumerating available audiofile names)"),
                    logging::LogLevel::DbError,
                    true,
                );
                vec![]
            });
        result
    }
    pub fn remove_audiofile(&mut self, id: usize) {
        self.db_connection()
            .query_map(
                format!(
                    "
                SELECT name FROM audiofiles WHERE id = {id}
            "
                ),
                |name: String| {
                    std::fs::remove_file(format!("{AUDIO_FILES_PATH}{name}"))
                },
            )
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (deleting audiofile)"),
                    logging::LogLevel::DbError,
                    true,
                );
                vec![]
            });
        self.db_connection()
            .query_drop(format!(
                "
                DELETE FROM audiofiles WHERE id = {id}
            "
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (deleting audiofile)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
    }
    pub fn get_audiofile(&mut self, id: usize) -> (String, Vec<u8>) {
        self.db_connection()
            .query_map(
                format!(
                    "
                    SELECT name FROM audiofiles WHERE id = {id}
                "
                ),
                |filename: String| filename,
            )
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (selecting audiofile)"),
                    logging::LogLevel::DbError,
                    true,
                );
                vec![]
            })
            .first()
            .map_or_else(
                || {
                    logging::log(
                        format!("Multiple files with the id '{id}' found"),
                        logging::LogLevel::Warning,
                        false,
                    );
                    ("not found".to_owned(), vec![])
                },
                |filename| {
                    std::fs::read(format!("{AUDIO_FILES_PATH}{filename}"))
                        .map_or_else(
                            |_| {
                                logging::log(
                                    format!(
                                        "Failed to read from file '{filename}'"
                                    ),
                                    logging::LogLevel::Warning,
                                    false,
                                );
                                ("not found".to_owned(), vec![])
                            },
                            |content| (filename.clone(), content),
                        )
                },
            )
    }
}
pub fn sync_audiofiles_with_filesystem(
    db_connection: &mut PooledConn,
    available_audiofile_names: &[(usize, String)],
) {
    logging::log(
        "Syncing audiofile database with filesystem".to_string(),
        logging::LogLevel::Info,
        false,
    );

    let _ = create_dir_all(AUDIO_FILES_PATH);

    let filesystem_files = get_filesystem_audiofiles();

    let db_files: Vec<String> = available_audiofile_names
        .iter()
        .map(|(_, name)| name)
        .cloned()
        .collect();

    for fs_file in &filesystem_files {
        if !db_files.contains(fs_file) {
            logging::log(
                format!("Adding missing audiofile to database: {fs_file}"),
                logging::LogLevel::Info,
                false,
            );

            db_connection
                .query_drop(format!(
                    "
                    INSERT INTO audiofiles (name, show_id)
                    VALUES (
                        '{fs_file}',
                        (SELECT id FROM shows WHERE active)
                    )"
                ))
                .unwrap_or_else(|err| {
                    logging::log(
                        format!("{err:?}\n        (adding audiofile {fs_file} during sync)"),
                        logging::LogLevel::DbError,
                        true,
                    );
                });
        }
    }

    for db_file in &db_files {
        if !filesystem_files.contains(db_file) {
            logging::log(
                format!("Removing stale audiofile from database: {db_file}"),
                logging::LogLevel::Info,
                false,
            );

            db_connection
                .query_drop(format!(
                    "DELETE FROM audiofiles WHERE name = '{db_file}' AND show_id = (SELECT id FROM shows WHERE active)"
                ))
                .unwrap_or_else(|err| {
                    logging::log(
                        format!("{err:?}\n        (removing stale audiofile {db_file} during sync)"),
                        logging::LogLevel::DbError,
                        true,
                    );
                });
        }
    }
}

fn get_filesystem_audiofiles() -> Vec<String> {
    let mut files = vec![];

    if let Ok(entries) = read_dir(AUDIO_FILES_PATH) {
        for entry in entries.flatten() {
            if let Ok(file_type) = entry.file_type() {
                if file_type.is_file() {
                    if let Some(filename) = entry.file_name().to_str() {
                        if Path::new(filename)
                            .extension()
                            .is_some_and(|ext| ext.eq_ignore_ascii_case("mp3"))
                        {
                            files.push(filename.to_string());
                        }
                    }
                }
            }
        }
    } else {
        logging::log(
            format!("Could not read audiofiles directory: {AUDIO_FILES_PATH}"),
            logging::LogLevel::Warning,
            false,
        );
    }

    files
}
