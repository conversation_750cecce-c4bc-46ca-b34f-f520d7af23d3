<script lang="ts" module>
    declare const __APP_VERSION__: string;
</script>

<script lang="ts">
    import Button from "$lib/atoms/button.svelte";
    import Icon from "$lib/atoms/icon.svelte";
    import Loadingspinner from "$lib/atoms/Loadingspinner.svelte";

    import { networking, allRwBackendsOnNetwork } from "$lib/stores/networking";
    import type { ReachableBackend } from "$lib/stores/networking";
    import { onDestroy, onMount } from "svelte";
    import { TOAST } from "$lib/stores/toast";

    let reachableBackends: ReachableBackend[] = $state([]);

    let backendSearchInterval: NodeJS.Timeout | undefined = $state();

    let currentlyFetching = $state(true);

    onMount(async () => {
        reachableBackends = await allRwBackendsOnNetwork();
        currentlyFetching = false;
        backendSearchInterval = setInterval(async () => {
            if (!currentlyFetching) {
                currentlyFetching = true;
                reachableBackends = await allRwBackendsOnNetwork();
                currentlyFetching = false;
            }
        }, 10000);
    });

    onDestroy(() => {
        if (backendSearchInterval !== undefined) {
            clearInterval(backendSearchInterval);
        }
    });

    function formatUptime(seconds: number): string {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;

        if (hours > 0) {
            return `${hours}h ${minutes}m`;
        } else if (minutes > 0) {
            return `${minutes}m ${secs}s`;
        } else {
            return `${secs}s`;
        }
    }
</script>

<div class="rounded-lg bg-object p-4">
    <table class="min-w-full bg-surface shadow-md rounded-lg overflow-hidden">
        <thead>
            <tr class="bg-primary text-secondary">
                <th class="py-2 px-4">IP</th>
                <th class="py-2 px-4">VERSION</th>
                <th class="py-2 px-4">SENDING ARTNET</th>
                <th class="py-2 px-4">UPTIME</th>
                <th class="py-2 px-4"></th>
            </tr>
        </thead>
        <tbody class="text-center">
            {#each reachableBackends as backend}
                <tr
                    class="hover:bg-primary hover:bg-opacity-20"
                    class:bg-red-200={__APP_VERSION__ !== backend[1].version}
                    class:bg-green-200={$networking === backend[0]}
                >
                    <td
                        class="py-2 px-4 transition cursor-pointer"
                        onclick={() => {
                            if (__APP_VERSION__ === backend[1].version) {
                                $networking = backend[0];
                                TOAST.success(`Connected to ${backend[0]}`);
                            } else {
                                TOAST.error("Incompatible versions!");
                            }
                        }}
                    >
                        {backend[0]}
                    </td>
                    <td
                        class="py-2 px-4 transition cursor-pointer"
                        onclick={() => {
                            if (__APP_VERSION__ === backend[1].version) {
                                $networking = backend[0];
                                TOAST.success(`Connected to ${backend[0]}`);
                            } else {
                                TOAST.error("Incompatible versions!");
                            }
                        }}>{backend[1].version}</td
                    >
                    <td
                        class="py-2 px-4 transition cursor-pointer"
                        onclick={() => {
                            if (__APP_VERSION__ === backend[1].version) {
                                $networking = backend[0];
                                TOAST.success(`Connected to ${backend[0]}`);
                            } else {
                                TOAST.error("Incompatible versions!");
                            }
                        }}
                    >
                        <span
                            class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                            class:bg-green-100={backend[1].artnet_sending}
                            class:text-green-800={backend[1].artnet_sending}
                            class:bg-gray-100={!backend[1].artnet_sending}
                            class:text-gray-800={!backend[1].artnet_sending}
                        >
                            {backend[1].artnet_sending ? "Active" : "Inactive"}
                        </span>
                    </td>
                    <td
                        class="py-2 px-4 transition cursor-pointer"
                        onclick={() => {
                            if (__APP_VERSION__ === backend[1].version) {
                                $networking = backend[0];
                                TOAST.success(`Connected to ${backend[0]}`);
                            } else {
                                TOAST.error("Incompatible versions!");
                            }
                        }}
                    >
                        {formatUptime(Number(backend[1].uptime_seconds))}
                    </td>
                    <td class="py-2 px-4 flex w-full justify-end">
                        <div class="w-fit text-lg">
                            <Button
                                id={`${backend[0]}-shutdown`}
                                onclick={() => {
                                    const decision = confirm(
                                        `Do you want to shutdown the controller at ${backend[0]}`,
                                    );
                                    if (decision) {
                                        fetch(
                                            `http://${backend[0]}:${networking.port}/shutdown`,
                                        )
                                            .then(() => {
                                                TOAST.warning(
                                                    "Please wait ~25 seconds before unplugging power from the controller to avoid damage.",
                                                );
                                            })
                                            .catch(() =>
                                                TOAST.error(
                                                    `We could not shutdown the controller at ${backend[0]}`,
                                                ),
                                            );
                                    }
                                }}
                            >
                                <Icon icon="mdi:shutdown"></Icon>
                            </Button>
                        </div>
                    </td>
                </tr>
            {/each}
            <tr
                onclick={async () => {
                    if (!currentlyFetching) {
                        currentlyFetching = true;
                        reachableBackends = await allRwBackendsOnNetwork();
                        currentlyFetching = false;
                    }
                }}
                class="transition"
                class:cursor-pointer={!currentlyFetching}
                class:hover:bg-primary={!currentlyFetching}
                class:hover:bg-opacity-20={!currentlyFetching}
            >
                <td class="py-2 px-4 w-1/2">
                    {#if currentlyFetching}
                        <p class="italic">Searching for controllers</p>
                    {:else}
                        <p>Search for controllers</p>
                    {/if}
                </td>
                <td class="py-2 px-4">
                    {#if currentlyFetching}
                        <Loadingspinner></Loadingspinner>
                    {/if}
                </td>
            </tr>
        </tbody>
    </table>
</div>
