describe('Create default show', () => {
    it('creates a test-show if not exists', () => {
        cy.visit('/networking')
        cy.contains('127.0.0.1').click()
        cy.contains('Shows').click()
        cy.get('body').then(($body) => {
            if ($body.text().includes('cy:test')) {
                cy.contains('cy:test').click()
            }
            else {
                cy.get('label').contains('New name').closest('div').find('input').type('cy:test')
                cy.get('button[id="createShow"]').click()
            }
        })
    })
})
