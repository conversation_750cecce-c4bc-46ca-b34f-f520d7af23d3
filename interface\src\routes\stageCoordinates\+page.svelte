<script lang="ts" module>
    export const WIDTH = 1024;
    export const HEIGHT = 1024;
</script>

<script lang="ts">
    import FixtureGroupTable from "./partials/fixtureGroupTable.svelte";
    import Select from "$lib/atoms/select.svelte";
    import { fixtures } from "$lib/stores/fixtures";
    import { fixtureGroups, fixturesForGroup } from "$lib/stores/fixtureGroups";
    import StageCoordinateCanvas from "./partials/stageCoordinateCanvas.svelte";
    import Icon from "$lib/atoms/icon.svelte";
    import SearchableDropdown from "$lib/molecules/searchable_dropdown.svelte";
    import LabeledTextinput from "$lib/molecules/labeled_textinput.svelte";
    import Button from "$lib/atoms/button.svelte";
    import { type FixtureGroup } from "$lib/types/bindings/FixtureGroup";

    let saved = $state(true);
    let movingFixtureId: number | null = $state(null);
    let selectedGroupName: string | null = $state(null);
    let selectedGroup = $derived.by(() => {
        return $fixtureGroups.find((group) => group.name === selectedGroupName);
    });

    let toCreateFixturegroup: FixtureGroup = {
        id: null,
        name: "NEW",
        fixture_ids: [],
        immutable: false,
    };

    let newGroupName = $state("");
    $effect(() => {
        newGroupName = selectedGroupName ?? "";
    });
</script>

<svelte:window
    onmouseup={(e) => {
        if (!saved && e.target instanceof HTMLCanvasElement) {
            fixtures.updateRemote();
            movingFixtureId = null;
            saved = true;
        }
    }}
/>

<div class="flex justify-center">
    <div class="w-3/4 rounded-lg bg-object p-4">
        <h2 class="mb-2 text-center text-3xl">Stage Coordinate View</h2>
        <div class="flex justify-center">
            <div
                class="relative border-2"
                style="width: {WIDTH}px;height: {HEIGHT}px"
            >
                <StageCoordinateCanvas
                    {selectedGroup}
                    onchange={() => {
                        if (selectedGroup) {
                            fixtureGroups.patchOne(selectedGroup);
                        }
                    }}
                />
            </div>
        </div>
        <!-- TODO: Make the border of the canvas the "saved" indicator -->
        <div class="flex justify-center">
            {#if saved}
                <p class="mt-4 rounded-lg bg-green-800 px-2 py-1">all saved</p>
            {:else}
                <p class="mt-4 rounded-lg bg-red-800 px-2 py-1">unsaved</p>
            {/if}
        </div>

        <h2 class="text-lg font-semibold text-primary mb-4 flex items-center">
            <div class="text-accent mr-2">
                <Icon icon="material-symbols:text-snippet-outline"></Icon>
            </div>
            Fixturegroup Management
        </h2>

        <div class="grid grid-cols-1 xl:grid-cols-12 gap-6">
            <div class="lg:col-span-6">
                <div class="space-y-3">
                    <div class="flex flex-col space-y-2">
                        <SearchableDropdown
                            id="blueprintSelect"
                            bind:value={selectedGroupName}
                            items={$fixtureGroups.map((group) => {
                                return {
                                    id: group.name,
                                    name: group.name,
                                    readonly: group.id === null,
                                };
                            })}
                        ></SearchableDropdown>
                    </div>
                    {#if selectedGroupName && selectedGroup && selectedGroup.id !== null}
                        <div class="flex flex-col space-y-2">
                            <LabeledTextinput
                                label="Fixturegroup Name"
                                bind:value={newGroupName}
                                placeholder="Enter fixturegroup name..."
                                onchange={() => (saved = false)}
                            ></LabeledTextinput>
                            <div class="flex items-center space-x-2">
                                <input
                                    type="checkbox"
                                    id="immutableToggle"
                                    bind:checked={selectedGroup.immutable}
                                    onchange={() => (saved = false)}
                                    class="rounded border-gray-300 text-primary focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50"
                                />
                                <label for="immutableToggle" class="text-sm">
                                    Immutable (hidden from blockly add/remove
                                    operations)
                                </label>
                            </div>
                        </div>
                    {/if}
                </div>
            </div>

            <div class="flex flex-col space-y-2">
                <Button
                    id="blueprintSave"
                    onclick={() => {
                        if (selectedGroup && !saved) {
                            const toSaveGroup = selectedGroup;
                            toSaveGroup.name = newGroupName;
                            if (toSaveGroup) {
                                fixtureGroups
                                    .patchOne(toSaveGroup)
                                    .then((_) => {
                                        saved = true;
                                    });
                            }
                        }
                    }}
                >
                    {#if !saved}
                        <div
                            class="flex justify-center w-full rounded-lg p-4 text-3xl text-info"
                        >
                            <div class="animate-shake-once">
                                <Icon icon="material-symbols:save"></Icon>
                            </div>
                        </div>
                    {:else}
                        <div
                            class="flex justify-center rounded-lg p-4 text-3xl text-success"
                        >
                            <Icon icon="material-symbols:save"></Icon>
                            <div class="absolute animate-ping-once">
                                <Icon icon="material-symbols:save"></Icon>
                            </div>
                        </div>
                    {/if}
                </Button>

                <Button
                    id="blueprintCreate"
                    onclick={async () => {
                        await fixtureGroups.createOne(toCreateFixturegroup);
                        selectedGroupName = toCreateFixturegroup.name;
                    }}
                    grow
                >
                    <div class="flex items-center justify-center space-x-2">
                        <div class="text-lg">
                            <Icon icon="mdi:add"></Icon>
                        </div>
                        <span class="text-sm font-medium">New</span>
                    </div>
                </Button>

                {#if selectedGroup && selectedGroup.id !== null}
                    <Button
                        id="blueprintRemove"
                        onclick={() => {
                            if (selectedGroupName) {
                                const toDeleteGroup = $fixtureGroups.find(
                                    (group) => group.name === selectedGroupName,
                                );
                                if (toDeleteGroup) {
                                    fixtureGroups.deleteOne(toDeleteGroup);
                                }
                            }
                        }}
                        disabled={selectedGroupName === undefined}
                        grow
                    >
                        <div class="flex items-center justify-center space-x-2">
                            <div class="text-lg text-error">
                                <Icon icon="ri:subtract-fill"></Icon>
                            </div>
                            <span class="text-sm font-medium">Delete</span>
                        </div>
                    </Button>
                {/if}
            </div>
        </div>
    </div>
</div>
