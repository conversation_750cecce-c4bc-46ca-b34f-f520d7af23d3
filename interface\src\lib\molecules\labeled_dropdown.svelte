<script lang="ts" generics="T extends string | number | null">
    import Select from "$lib/atoms/select.svelte";
    import type { Snippet } from "svelte";

    let {
        value = $bindable(),
        label,
        disabled,
        onchange,
        children,
    }: {
        value: T;
        label: string;
        onchange?: (newValue: T) => any;
        disabled?: boolean;
        children: Snippet;
    } = $props();
</script>

<div class="text-sub flex flex-col">
    <label for="input">{label}</label>
    <Select bind:value name={label} {onchange} {disabled}>
        {@render children()}
    </Select>
</div>
