name: 'publish'

on:
  push:
    tags:
      - '[0-9]+.[0-9]+.[0-9]+'

jobs:
  build_interface:
    permissions:
      contents: write
    strategy:
      fail-fast: false
      matrix:
        include:
          # - platform: 'macos-latest' # for Arm based macs (M1 and above).
          #   args: '--target aarch64-apple-darwin'
          # - platform: 'macos-latest' # for Intel based macs.
          #   args: '--target x86_64-apple-darwin'
          # - platform: 'ubuntu-22.04'
          #   args: ''
          - platform: 'windows-latest'
            args: ''

    runs-on: ${{ matrix.platform }}

    steps:
      - uses: actions/checkout@v4

      - name: Set version
        run: |
          jq --arg version "${{ github.ref_name }}" '.version = $version' interface/package.json > interface/tmp.json
          del interface/package.json
          mv interface/tmp.json interface/package.json

      - name: install dependencies (ubuntu only)
        if: matrix.platform == 'ubuntu-22.04' # This must match the platform value defined above.
        run: |
          sudo apt-get update
          sudo apt-get install -y libwebkit2gtk-4.1-dev libappindicator3-dev librsvg2-dev patchelf

      - name: setup node
        uses: actions/setup-node@v4
        with:
          node-version: 22.9
          cache: 'npm'
          cache-dependency-path: '**/package-lock.json'

      - name: install Rust stable
        uses: dtolnay/rust-toolchain@stable
        with:
          # Those targets are only used on macos runners so it's in an `if` to slightly speed up windows and linux builds.
          targets: ${{ matrix.platform == 'macos-latest' && 'aarch64-apple-darwin,x86_64-apple-darwin' || '' }}

      - name: Rust cache
        uses: swatinem/rust-cache@v2
        with:
          workspaces: './src-tauri -> target'

      - name: install frontend dependencies
        working-directory: interface
        run: rm package-lock.json && npm i && npm run build

      - uses: tauri-apps/tauri-action@v0
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          projectPath: interface
          args: ${{ matrix.args }}

      - name: Rename
        run: mv interface/src-tauri/target/release/rw_interface.exe interface/src-tauri/target/release/rw_interface_${{ github.ref_name }}.exe

      - uses: actions/upload-artifact@v4
        with:
          path: interface/src-tauri/target/release/rw_interface_${{ github.ref_name }}.exe
          name: rw_interface_${{ github.ref_name }}.exe

  build_host:
    runs-on: rpi-4b-1.1
    steps:
      - uses: actions/checkout@v4

      - name: Set version
        run: sed -i "s/^version = \"0.0.0\"/version = \"${{ github.ref_name }}\"/" host/Cargo.toml

      - name: Compile
        run: cargo build --manifest-path host/Cargo.toml --release

      - name: Rename
        run: mv host/target/release/host host/target/release/rw_host_${{ github.ref_name }}_aarch64

      - uses: actions/upload-artifact@v4
        with:
          path: host/target/release/rw_host_${{ github.ref_name }}_aarch64
          name: rw_host_${{ github.ref_name }}_aarch64

  build_modules:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: ECHO TODO
        run: echo TODO

  create_release:
    runs-on: ubuntu-latest
    permissions:
      contents: write
    needs: [build_interface, build_host, build_modules]
    steps:
      - uses: actions/checkout@v4

      - uses: actions/download-artifact@v4

      - name: Prepare scripts
        run: |
          mv host/scripts/startup.sh host/scripts/rw_host_startup_${{ github.ref_name }}.sh
          mv host/scripts/setup.sh host/scripts/rw_host_setup_${{ github.ref_name }}_raspbian.sh

      - uses: softprops/action-gh-release@v2
        with:
          name: ${{ github.ref_name }}
          tag_name: ${{ github.ref_name }}
          files: |
            rw_host_${{ github.ref_name }}_aarch64/rw_host_${{ github.ref_name }}_aarch64
            rw_interface_${{ github.ref_name }}.exe/rw_interface_${{ github.ref_name }}.exe
            host/scripts/rw_host_startup_${{ github.ref_name }}.sh
            host/scripts/rw_host_setup_${{ github.ref_name }}_raspbian.sh
          token: ${{ secrets.GITHUB_TOKEN }}
