use crate::dmx_renderer::dynamics::property::{
    ColorPropertyCoordinate, UnimplementedChannelPropertyCoordinate,
};
use crate::dmx_renderer::InterpolationMethod;
use crate::logging;
use serde::{Deserialize, Serialize};
use splines::{Key, Spline};
use ts_rs::TS;

use super::property::{PropertyFileDescriptor, SnippetCall};

pub const DEFAULT_NAME: &str = "new";
pub const DEFAULT_REQUIRES_USER_ACTION_REASON: Option<&str> = Some("empty");

#[derive(Clone, Serialize, Deserialize, Debug, TS)]
#[ts(export)]
pub struct Track {
    pub properties: Vec<TimedPropertyFileDescriptor>,
    pub name: String,
    pub id: usize,
    pub requires_user_action_reason: Option<String>,
}

#[must_use]
pub fn all_properties_from_tracks(
    value: &Vec<Track>,
) -> Vec<TimedPropertyFileDescriptor> {
    let mut result = vec![];

    for track in value {
        for property in &track.properties {
            result.push(property.clone());
        }
    }

    result
}

#[derive(Clone, Serialize, Deserialize, Debug, TS)]
#[ts(export)]
pub struct TimecodeFileDescriptor {
    pub tracks: Vec<Track>,
    pub name: String,
    pub id: usize,
    pub requires_user_action_reason: Option<String>,
    pub audiofile_id: Option<usize>,
    pub bpm: Option<usize>,
}

// TODO: Wrap in struct (name, coordinates, x_offset, fixture_ids)
#[derive(Clone, Serialize, Deserialize, Debug, PartialEq, TS)]
#[ts(export)]
pub enum TimedPropertyFileDescriptor {
    // (name, coordinates, x_offset, fixture_ids)
    UnimplementedChannel(
        (
            String,
            Vec<UnimplementedChannelPropertyCoordinate>,
            usize,
            Vec<usize>,
        ),
    ),
    // (coordinates, x_offset, fixture_ids)
    ColorPropertyCoordinates(Vec<ColorPropertyCoordinate>, usize, Vec<usize>),
    // (snippet_id, x_offset, fixture_ids)
    CallSnippet(usize, usize, Vec<usize>),
}

#[must_use]
#[allow(clippy::cast_precision_loss)]
pub fn merge_by_type_for_fixture(
    properties: &Vec<TimedPropertyFileDescriptor>,
    fixture_id: usize,
) -> Vec<PropertyFileDescriptor> {
    let mut merged_properties: Vec<PropertyFileDescriptor> = vec![];

    for property in properties {
        match property {
            TimedPropertyFileDescriptor::ColorPropertyCoordinates(
                property_coordinates,
                x_offset,
                fixture_ids,
            ) => {
                if !fixture_ids.contains(&fixture_id) {
                    continue;
                }

                let mut property_coordinates = property_coordinates.clone();

                #[allow(clippy::as_conversions)]
                for coordinate in &mut property_coordinates {
                    coordinate.x += *x_offset as f32;
                }
                if let Some(merged_property) =
                    merged_properties.iter_mut().find(|merged_property| {
                        matches!(
                            merged_property,
                            PropertyFileDescriptor::ColorPropertyCoordinates(
                                _,
                                _
                            )
                        )
                    })
                {
                    merged_property
                        .append_color_coordinates(&mut property_coordinates);
                } else {
                    merged_properties.push(
                        PropertyFileDescriptor::ColorPropertyCoordinates(
                            property_coordinates.clone(),
                            InterpolationMethod::Linear,
                        ),
                    );
                }
            }
            TimedPropertyFileDescriptor::CallSnippet(
                snippet_id,
                x_offset,
                fixture_ids,
            ) => {
                if !fixture_ids.contains(&fixture_id) {
                    continue;
                }

                merged_properties.push(PropertyFileDescriptor::CallSnippet(
                    SnippetCall {
                        #[allow(clippy::as_conversions)]
                        x: (*x_offset) as f32,
                        snippet_id: *snippet_id,
                    },
                ));
            }
            TimedPropertyFileDescriptor::UnimplementedChannel(channel) => {
                if !channel.3.contains(&fixture_id) {
                    continue;
                }

                let mut property_coordinates = channel.1.clone();

                #[allow(clippy::as_conversions)]
                for coordinate in &mut property_coordinates {
                    coordinate.x += channel.2 as f32;
                }
                if let Some(merged_property) =
                    merged_properties.iter_mut().find(|merged_property| {
                        if let PropertyFileDescriptor::UnimplementedChannel(
                            merged_channel,
                        ) = merged_property
                        {
                            merged_channel.0 == channel.0
                        } else {
                            false
                        }
                    })
                {
                    merged_property.append_unimplemented_channel_coordinates(
                        &mut property_coordinates,
                    );
                } else {
                    merged_properties.push(
                        PropertyFileDescriptor::UnimplementedChannel((
                            channel.0.clone(),
                            property_coordinates.clone(),
                            InterpolationMethod::Linear,
                        )),
                    );
                }
            }
        }
    }

    merged_properties
}

impl From<&TimedPropertyFileDescriptor> for Spline<f32, f32> {
    fn from(value: &TimedPropertyFileDescriptor) -> Self {
        let mut keys: Vec<Key<f32, f32>> = vec![];
        match value {
            TimedPropertyFileDescriptor::UnimplementedChannel((
                _name,
                all_coordinates,
                _start,
                _fixture_ids,
            )) => {
                for coordinates in all_coordinates {
                    keys.push(Key::new(
                        coordinates.x,
                        coordinates.y,
                        splines::Interpolation::Cosine,
                    ));
                }
            }
            TimedPropertyFileDescriptor::ColorPropertyCoordinates(
                all_coordinates,
                _start,
                _fixture_ids,
            ) => {
                for coordinates in all_coordinates {
                    keys.push(Key::new(
                        coordinates.x,
                        coordinates.color.as_hsv().0,
                        splines::Interpolation::Cosine,
                    ));
                }
            }
            TimedPropertyFileDescriptor::CallSnippet(snippet_id, _, _) => {
                logging::log(format!(
                    "(Invallid) Attempt to create spline from snippet {snippet_id}"),
                    logging::LogLevel::Info,
                    false
                );
            }
        }
        Self::from_vec(keys)
    }
}

impl TimedPropertyFileDescriptor {
    pub fn sort(&mut self) {
        match self {
            Self::UnimplementedChannel(channel) => {
                channel.1.sort_by(|a, b| a.x.total_cmp(&b.x));
            }
            Self::ColorPropertyCoordinates(channel, _, _) => {
                channel.sort_by(|a, b| a.x.total_cmp(&b.x));
            }
            Self::CallSnippet(_, _, _) => (),
        }
    }
}
