[package]
name = "gdtf-parser"
version = "0.1.0"
authors = ["micha<PERSON><PERSON><PERSON> <<EMAIL>>"]
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
quick-xml = "0.22"
zip = "0.5"
dmx-struct = "0.1.0"
regex = "1.5.4"
lazy_static = "1.4.0"
unicode-segmentation = "1.7.1"
serde = { version = "1.0.182", features = ["derive"] }
ts-rs = "7"

[dev-dependencies]
backtrace = "0.3.59"
xmltree = "0.10"
clap = "2.33"
