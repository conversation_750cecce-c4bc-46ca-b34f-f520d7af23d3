use serde::{Deserialize, Serialize};
use ts_rs::TS;

pub trait Identifiable {
    type Identifier;

    fn identify(&self, identifier: Self::Identifier) -> bool;
}

impl Identifiable for () {
    type Identifier = ();

    fn identify(&self, (): Self::Identifier) -> bool {
        true
    }
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, PartialEq, TS)]
#[ts(export)]
pub enum TreeItem<T: PartialEq + Identifiable> {
    Directory(Directory<T>),
    Item(T),
}

impl<T: PartialEq + Identifiable> TreeItem<T> {
    pub const fn is_dir(&self) -> bool {
        match self {
            Self::Directory(_) => true,
            Self::Item(_) => false,
        }
    }
    pub const fn is_item(&self) -> bool {
        !self.is_dir()
    }
}

#[derive(Default, Debug, Clone, Serialize, Deserialize, PartialEq, TS)]
#[ts(export)]
pub struct Directory<T>
where
    T: PartialEq + Identifiable,
{
    pub id: usize,
    pub name: String,
    pub content: Vec<TreeItem<T>>,
}

impl<T: PartialEq + Identifiable> Directory<T> {
    #[must_use]
    pub const fn empty(name: String) -> Self {
        Self {
            id: 0,
            content: vec![],
            name,
        }
    }
    pub fn insert(&mut self, item: T) {
        self.content.push(TreeItem::Item(item));
    }
    pub fn create_dir(&mut self, name: String) {
        self.content.push(TreeItem::Directory(Self {
            id: 0,
            name,
            content: vec![],
        }));
    }
    pub fn rename(&mut self, name: String) {
        self.name = name;
    }
    pub fn retain_recursive<P>(&mut self, mut predicate: P)
    where
        P: FnMut(&T) -> bool,
    {
        self.items_recursive()
            .retain(|tree_item| predicate(tree_item));
    }
    #[must_use]
    pub fn items(&self) -> Vec<&T> {
        let mut result = vec![];
        for item in &self.content {
            if let TreeItem::Item(item) = item {
                result.push(item);
            }
        }
        result
    }
    #[must_use]
    pub fn items_recursive(&self) -> Vec<&T> {
        let mut result = vec![];
        for item in &self.content {
            match item {
                TreeItem::Item(item) => result.push(item),
                TreeItem::Directory(dir) => {
                    result.append(&mut dir.items_recursive());
                }
            }
        }
        result
    }
    pub fn items_mut(&mut self) -> Vec<&mut T> {
        let mut result = vec![];
        for item in &mut self.content {
            if let TreeItem::Item(item) = item {
                result.push(item);
            }
        }
        result
    }
    pub fn items_recursive_mut(&mut self) -> Vec<&mut T> {
        let mut result = vec![];
        for item in &mut self.content {
            match item {
                TreeItem::Item(item) => result.push(item),
                TreeItem::Directory(dir) => {
                    result.append(&mut dir.items_recursive_mut());
                }
            }
        }
        result
    }
}
