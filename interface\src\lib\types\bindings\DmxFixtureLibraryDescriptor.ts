// This file was generated by [ts-rs](https://github.com/Aleph-Alpha/ts-rs). Do not edit this file manually.
import type { DmxFixtureColor } from "./DmxFixtureColor";
import type { MovementChannels } from "./MovementChannels";
import type { UnimplementedChannel } from "./UnimplementedChannel";

export interface DmxFixtureLibraryDescriptor { fixturetype: string, movement_channels: MovementChannels | null, footprint_size: number, color: DmxFixtureColor | null, unimplemented_channels: Array<UnimplementedChannel>, }