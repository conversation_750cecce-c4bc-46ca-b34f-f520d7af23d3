#ifndef rw_utils
#define rw_utils

#include <Adafruit_NeoPixel.h>
#include <Arduino.h>

#define LED_OFF 0
#define LED_BLINK 1
#define LED_ON 2

class Button {
public:
  Button(int p_col_pin, int p_row_pin, int p_led_id, const int (&led_color)[3]);
  void tick(void (*on_press_callback)(int), void (*on_release_callback)(int),
            Adafruit_NeoPixel *p_pixels, bool p_led_blinker_state,
            int button_id);
  void update_led_state(int (*func)(int));
  void set_led_color(const int (&led_color)[3]);
  bool get_pressed();

private:
  int col_pin;
  int row_pin;
  int led_id;
  int led_color[3];
  int led_state;
  bool pressed;
};

class Fader {
  public:
    Fader(int p_poti_pin, int p_motor_r_pin, int p_motor_l_pin);
    void set_new_dest(int p_new_dest);
    int get_value();
    void tick(void (*on_move_callback)(int));

  private:
    int poti_pin;
    int motor_r_pin;
    int motor_l_pin;
    int dest;
    int motor_enabled_since;
    int last_seen_position;
    bool motor_enabled;
};

#endif
