[package]
name = "rwm_communication"
version = "0.1.0"
authors = ["jonas <@DISTRIB_ID=Pop.com>"]
edition = "2021"
license = "MIT OR Apache-2.0"

[lib]
name = "rwm_communication"
test = false
bench = false

[dependencies]
panic-halt = "0.2.0"
ufmt = "0.2.0"
nb = "0.1.2"
embedded-hal = "0.2.3"
avr-device = { version = "0.5.4", features = ["atmega2560", "critical-section-impl"] }
my_rust_utils = "0.1.1"
embedded-alloc = "0.5.1"
rwm_package_definitions = { path = "../rwm_package_definitions" }

[dependencies.arduino-hal]
git = "https://github.com/rahix/avr-hal"
rev = "3e362624547462928a219c40f9ea8e3a64f21e5f"
features = ["arduino-mega2560"]

# The latest releases of `proc-macro2` do not support the rust toolchain that
# we use.  Thus, we must fix this dependency to an older version where our
# toolchain is still supported.  See https://github.com/Rahix/avr-hal/issues/537
[build-dependencies.proc-macro2]
version = "=1.0.79"

[features]
tomahawk_1 = []
tomahawk_2 = []

[lints]
workspace = true
