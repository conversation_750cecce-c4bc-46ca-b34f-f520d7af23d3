<script lang="ts">
    import P5jssketch from "$lib/molecules/p5jssketch.svelte";
    import { onMount, untrack } from "svelte";
    import { remap } from "@anselan/maprange";
    import type { UnimplementedChannelPropertyCoordinate } from "$lib/types/bindings/UnimplementedChannelPropertyCoordinate";
    import { theme } from "$lib/stores/theme";

    let rootElement: HTMLElement | undefined = $state();

    interface Point {
        x: number;
        y: number;
        moving: boolean;
        border: boolean;
    }

    interface InterpolationPoint {
        x: number;
        y: number;
    }

    const HEIGHT = 72;
    const MARGIN = 5;
    const GUIDE_LINES = [0, 0.25, 0.5, 0.75, 1.0];

    let {
        points = $bindable([]),
    }: {
        points: UnimplementedChannelPropertyCoordinate[];
    } = $props();

    let processedPoints: Point[] = $state([]);
    let hovering = $state(false);

    let width = $derived(
        points && points.length >= 2 ? points[points.length - 1].x : 0,
    );

    $effect(() => {
        if (width && untrack(() => processedPoints.length >= 2)) {
            untrack(() => {
                processedPoints[processedPoints.length - 1].x = width;
                interaction_type = 1;
            });
        }
    });

    $effect(() => {
        if (processedPoints.length) {
            points = processedPoints.map((pnt) => {
                return {
                    x: pnt.x,
                    y: pnt.y,
                };
            });
        }
    });

    function mouseLeave() {
        processedPoints.forEach((pnt) => (pnt.moving = false));
        processedPoints = processedPoints.sort((a, b) => (a.x > b.x ? 1 : -1));
        hovering = false;
    }

    function mouseEnter() {
        hovering = true;
    }

    onMount(() => {
        points.forEach((pnt) => {
            processedPoints.push({
                x: pnt.x,
                y: pnt.y,
                moving: false,
                border: false,
            });
        });
        if (processedPoints.length) {
            processedPoints[0].border = true;
            processedPoints[processedPoints.length - 1].border = true;
            processedPoints.forEach(
                (pnt) =>
                    (pnt.y = remap(
                        pnt.y,
                        [0, 255],
                        [HEIGHT - MARGIN, 0 + MARGIN],
                    )),
            );
        }

        sortProcessedPoints();

        computeInterpolation();
    });

    let interpolatedPoints: InterpolationPoint[] = $state([]);
    let interaction_type = $state(1);
    let ctrlPressed = $state(false);

    function snapToGuideLine(y: number): number {
        if (!ctrlPressed) return y;

        let minDistance = Infinity;
        let snappedY = y;

        GUIDE_LINES.forEach((guidePercent) => {
            const guideY = (HEIGHT - MARGIN) * (1 - guidePercent) + MARGIN;
            const distance = Math.abs(y - guideY);
            if (distance < minDistance) {
                minDistance = distance;
                snappedY = guideY;
            }
        });

        return snappedY;
    }
    const sketch = (p5: any) => {
        p5.setup = () => {};

        p5.draw = () => {
            if (interaction_type === 1) {
                p5.createCanvas(width, HEIGHT);
                switch ($theme) {
                    case "light":
                        p5.background(255);
                        break;
                    case "dark":
                        p5.background(216);
                        break;
                    case "solar":
                        p5.background("#B58900");
                        break;
                    case "catpuccin":
                        p5.background(234);
                        break;
                }

                p5.stroke(160);
                if (ctrlPressed) {
                    p5.strokeWeight(2);
                } else {
                    p5.strokeWeight(0.1);
                }
                GUIDE_LINES.forEach((guidePercent) => {
                    const guideY =
                        (HEIGHT - MARGIN) * (1 - guidePercent) + MARGIN;
                    p5.line(0, guideY, width, guideY);
                });

                p5.stroke(200);
                p5.rect(0, 0, width, MARGIN);
                p5.rect(0, HEIGHT - MARGIN, width, MARGIN);
                p5.stroke(0);

                p5.strokeWeight(5);
                processedPoints.forEach((pnt) => p5.point(pnt.x, pnt.y));
                p5.strokeWeight(1);
            }
            for (let i = 1; i < interpolatedPoints.length - 1; i++) {
                p5.line(
                    interpolatedPoints[i - 1].x,
                    interpolatedPoints[i - 1].y,
                    interpolatedPoints[i].x,
                    interpolatedPoints[i].y,
                );
            }
            interaction_type = 0;
            processedPoints.forEach((pnt) => {
                pnt.border = false;
                if (
                    p5.mouseX > pnt.x - 10 &&
                    p5.mouseX < pnt.x + 10 &&
                    p5.mouseY > pnt.y - 10 &&
                    p5.mouseY < pnt.y + 10
                ) {
                    interaction_type = 1;
                }
            });
            if (interaction_type === 1) {
                processedPoints[0].border = true;
                processedPoints[processedPoints.length - 1].border = true;
            }
            p5.cursor(interaction_type === 0 ? p5.ARROW : p5.MOVE);

            processedPoints.forEach((pnt) => {
                if (pnt.moving && p5.mouseY < HEIGHT && p5.mouseY > 0) {
                    pnt.y = Math.min(
                        Math.max(snapToGuideLine(p5.mouseY), 0 + MARGIN),
                        HEIGHT - MARGIN,
                    );
                    if (!pnt.border && p5.mouseX < width && p5.mouseX > 0) {
                        pnt.x = p5.mouseX;
                    }
                }
            });

            if (interaction_type === 1) {
                computeInterpolation();
            }

            if (processedPoints.length) {
                points = processedPoints.map((pnt) => {
                    return {
                        x: pnt.x,
                        y: remap(
                            pnt.y,
                            [HEIGHT - MARGIN, 0 + MARGIN],
                            [0, 255],
                        ),
                    };
                });
            }
        };

        p5.mousePressed = () => {
            if (hovering)
                if (interaction_type === 0) {
                    processedPoints.push({
                        x: p5.mouseX,
                        y: p5.mouseY,
                        moving: false,
                        border: false,
                    });
                } else {
                    processedPoints.forEach((pnt) => (pnt.moving = false));
                    processedPoints.sort((a, b) =>
                        p5.dist(a.x, a.y, p5.mouseX, p5.mouseY) >
                        p5.dist(b.x, b.y, p5.mouseX, p5.mouseY)
                            ? 1
                            : -1,
                    )[0].moving = true;
                }
            sortProcessedPoints();
            computeInterpolation();
        };

        p5.mouseReleased = () => {
            processedPoints.forEach((pnt) => (pnt.moving = false));
            sortProcessedPoints();
            computeInterpolation();
        };

        p5.mouseDragged = () => {
            if (hovering) {
                sortProcessedPoints();
                computeInterpolation();
            }
        };

        p5.keyPressed = (e: KeyboardEvent) => {
            if (hovering) {
                if (e.code === "ControlLeft" || e.code === "ControlRight") {
                    ctrlPressed = true;
                }
                if (e.code === "Delete" && interaction_type === 1) {
                    processedPoints = processedPoints.filter(
                        (point) => !point.moving,
                    );
                    computeInterpolation();
                }
            }
        };

        p5.keyReleased = (e: KeyboardEvent) => {
            if (e.code === "ControlLeft" || e.code === "ControlRight") {
                ctrlPressed = false;
            }
        };
        processedPoints = processedPoints;
    };
    function sortProcessedPoints() {
        processedPoints = processedPoints.sort((a, b) => (a.x > b.x ? 1 : -1));
    }
    function computeInterpolation() {
        interpolatedPoints = [];
        for (let j = 0; j < processedPoints.length - 1; j++) {
            for (
                let i =
                    processedPoints[j].x < processedPoints[j + 1].x
                        ? processedPoints[j].x
                        : processedPoints[j + 1].x;
                i <
                Math.abs(processedPoints[j + 1].x - processedPoints[j].x) +
                    (processedPoints[j].x < processedPoints[j + 1].x
                        ? processedPoints[j].x
                        : processedPoints[j + 1].x);
                i += 3
            ) {
                interpolatedPoints.push({
                    x: i,
                    y:
                        (Math.cos(
                            ((i - processedPoints[j].x) /
                                (processedPoints[j + 1].x -
                                    processedPoints[j].x)) *
                                Math.PI,
                        ) -
                            1) *
                            0.5 *
                            (processedPoints[j].y - processedPoints[j + 1].y) +
                        processedPoints[j].y,
                });
            }
        }
    }

    let scrollLeft = $state(0);
    let clientWidth = $state(0);
    onMount(() => {
        scrollLeft = rootElement?.scrollLeft ?? 0;
        clientWidth = rootElement?.clientWidth ?? 0;
    });
</script>

<svelte:window
    onmouseup={() => {
        sortProcessedPoints();
        computeInterpolation();
        interaction_type = 1;
    }}
    onkeydown={(e) => {
        if (e.code === "ControlLeft" || e.code === "ControlRight") {
            ctrlPressed = true;
        }
    }}
    onkeyup={(e) => {
        if (e.code === "ControlLeft" || e.code === "ControlRight") {
            ctrlPressed = false;
        }
    }}
/>

<div
    bind:this={rootElement}
    onmouseenter={() => mouseEnter()}
    onmouseleave={() => mouseLeave()}
    onscroll={(e) => {
        // @ts-ignore -> for some reason ts thinks, that there is no outerWidth
        clientWidth = e.target?.clientWidth ? e.target.clientWidth : 0;
        // @ts-ignore -> for some reason ts thinks, that there is no scrollLeft
        scrollLeft = e.target?.scrollLeft ? e.target.scrollLeft : 0;
    }}
    role="cell"
    tabindex={1}
>
    <P5jssketch {sketch} />
</div>
