use crate::ACKNOWLEDGED;
use alloc::boxed::Box;
use avr_device::interrupt;
use rwm_package_definitions::RawInput;

#[derive(<PERSON><PERSON>, <PERSON><PERSON>ult)]
pub struct SendableBytesQueueHead {
    first_package: Option<Box<SendableByte>>,
    waiting_for_ack: bool,
}

impl SendableBytesQueueHead {
    #[must_use]
    pub fn first(&self) -> Option<RawInput> {
        let first_package = self.first_package.as_ref()?;
        Some(first_package.content)
    }
    #[must_use]
    pub fn waiting_for_ack(&self) -> bool {
        self.waiting_for_ack
    }
    pub fn set_waiting_for_ack(&mut self, waiting_for_ack: bool) {
        self.waiting_for_ack = waiting_for_ack;
    }
    #[allow(clippy::clone_on_ref_ptr)]
    pub fn discard_first(&mut self) {
        if let Some(package) = self.first_package.as_deref_mut() {
            self.first_package = package.next_package.clone();
        }
    }
    pub fn append_package(&mut self, content: RawInput) {
        interrupt::free(|cs| {
            ACKNOWLEDGED.borrow(cs).set(false);
        });
        let Some(next_package) = self.first_package.as_deref_mut() else {
            self.first_package = Some(Box::new(SendableByte {
                content,
                next_package: None,
            }));
            return;
        };
        next_package.append_package(content);
    }
    #[must_use]
    pub fn len(&self) -> u8 {
        if let Some(first_package) = self.first_package.as_ref() {
            return first_package.depth(0);
        }
        0
    }
    #[must_use]
    pub fn is_empty(&self) -> bool {
        self.len() == 0
    }
    #[must_use]
    /// Like `len()` but only counting packages of `key_id`
    pub fn package_count(&self, key_id: u16) -> u8 {
        if let Some(first_package) = self.first_package.as_ref() {
            return first_package.package_count(key_id);
        }
        0
    }
}

#[derive(Clone)]
struct SendableByte {
    content: RawInput,
    next_package: Option<Box<SendableByte>>,
}

impl SendableByte {
    fn append_package(&mut self, content: RawInput) {
        let Some(next_package) = self.next_package.as_deref_mut() else {
            self.next_package = Some(Box::new(SendableByte {
                content,
                next_package: None,
            }));
            return;
        };
        next_package.append_package(content);
    }
    fn depth(&self, level: u8) -> u8 {
        let level = level.saturating_add(1);
        if let Some(next_package) = self.next_package.as_ref() {
            return next_package.depth(level);
        }
        level
    }
    fn package_count(&self, key_id: u16) -> u8 {
        // if self.content[0] == key_id[0] && self.content[1] == key_id[1] {
        if self.content.id == key_id {
            if let Some(child) = self.next_package.as_ref() {
                return child.package_count(key_id).saturating_add(1);
            }
            return 1;
        }
        if let Some(child) = self.next_package.as_ref() {
            return child.package_count(key_id);
        }
        0
    }
}
