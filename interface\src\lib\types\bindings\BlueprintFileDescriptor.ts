// This file was generated by [ts-rs](https://github.com/Aleph-Alpha/ts-rs). Do not edit this file manually.
import type { PropertyFileDescriptor } from "./PropertyFileDescriptor";
import type { RegisteredBlueprintFixtureDelay } from "./RegisteredBlueprintFixtureDelay";

export interface BlueprintFileDescriptor { properties: Array<PropertyFileDescriptor>, registered_fixture_delays: Array<RegisteredBlueprintFixtureDelay>, name: string, id: number, requires_user_action_reason: string | null, }