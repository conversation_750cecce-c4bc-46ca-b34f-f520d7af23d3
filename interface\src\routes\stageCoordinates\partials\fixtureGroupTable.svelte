<script lang="ts">
    import { fixturesForGroup } from "$lib/stores/fixtureGroups";

    let {
        selectedGroupName,
    }: {
        selectedGroupName: string;
    } = $props();

    let fixtures_to_show = $derived.by(() => {
        return fixturesForGroup(selectedGroupName);
    });
</script>

{#if fixtures_to_show.length < 0}
    <div class="mt-4">
        <h3 class="text-lg font-semibold mb-2">
            <span class="text-sm text-gray-400"
                >({fixtures_to_show.length} fixture{fixtures_to_show.length !==
                1
                    ? "s"
                    : ""})</span
            >
        </h3>

        <div class="overflow-x-auto">
            <table class="w-full border-collapse">
                <thead class="w-3/4">
                    <tr class="border-b">
                        <th scope="col" class="px-6 py-4 text-left text-sm">
                            ID
                        </th>
                        <th scope="col" class="px-6 py-4 text-left text-sm">
                            Name
                        </th>
                        <th scope="col" class="px-6 py-4 text-left text-sm">
                            Type
                        </th>
                        <th scope="col" class="px-6 py-4 text-left text-sm">
                            Universe
                        </th>
                        <th scope="col" class="px-6 py-4 text-left text-sm">
                            Address
                        </th>
                        <th scope="col" class="px-6 py-4 text-left text-sm">
                            Stage Position
                        </th>
                    </tr>
                </thead>
                <tbody>
                    {#each fixtures_to_show as fixture}
                        <tr class="cursor-pointer border-b transition">
                            <td class="whitespace-nowrap px-6 py-4 text-sm">
                                {fixture.id ?? "N/A"}
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm">
                                {fixture.name}
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm">
                                {fixture.fixturetype}
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm">
                                {fixture.dmx_universe}
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm">
                                {fixture.dmx_address ?? "N/A"}
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm">
                                ({fixture.stage_coordinates[0]}, {fixture
                                    .stage_coordinates[1]})
                            </td>
                        </tr>
                    {/each}
                </tbody>
            </table>
        </div>
    </div>
{:else}
    <div class="text-gray-400 text-center py-4">No fixtures in this group</div>
{/if}
