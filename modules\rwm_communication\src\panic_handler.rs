use avr_device::interrupt;
use core::panic::PanicInfo;

#[panic_handler]
pub fn panic(_info: &PanicInfo) -> ! {
    unsafe {
        let dp = arduino_hal::Peripherals::steal();
        let pins = arduino_hal::pins!(dp);

        let mut serial = arduino_hal::default_serial!(dp, pins, 250_000);
        #[cfg(debug_assertions)]
        let mut debug_serial = arduino_hal::Usart::new(
            dp.USART3,
            pins.d15,
            pins.d14.into_output(),
            arduino_hal::hal::usart::BaudrateArduinoExt::into_baudrate(115_200),
        );
        loop {
            interrupt::free(|_| {
                #[cfg(debug_assertions)]
                {
                    debug_serial.write_byte(112);
                    debug_serial.write_byte(97);
                    debug_serial.write_byte(110);
                    debug_serial.write_byte(105);
                    debug_serial.write_byte(99);
                    debug_serial.write_byte(10);
                    debug_serial.write_byte(13);
                }

                serial.write_byte(122);
                serial.write_byte(127);
                serial.write_byte(115);
                serial.write_byte(255);
                serial.write_byte(1);
            });
            arduino_hal::delay_ms(10_000);
        }
    };
}
