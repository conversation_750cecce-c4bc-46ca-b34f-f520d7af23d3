use super::{DmxChannelEmitter, DmxChannelValue};
use crate::dmx_renderer::dynamics::property::PropertyFileDescriptor;
use crate::dmx_renderer::dynamics::IsDynamic;
use crate::dmx_renderer::DMX_RENDERER_BEAT_IS_INDEX_POINTS;
use crate::{dmx_renderer::clamp_index_to_spline_length, logging};
use serde::{Deserialize, Serialize};
use splines::{Key, Spline};
use ts_rs::TS;

#[derive(Clone, Serialize, Deserialize, Debug, TS)]
#[ts(export)]
pub struct DmxFixtureColor {
    pub color: RgbColor,
    pub channels: ColorChannel,
    #[serde(skip)]
    pub hue_spline: Option<Spline<f32, f32>>,
    #[serde(skip)]
    pub saturation_spline: Option<Spline<f32, f32>>,
    #[serde(skip)]
    pub oneshot: bool,
    #[serde(skip)]
    pub blueprint_id: Option<usize>,
}

impl DmxFixtureColor {
    pub fn create_spline_from_current_to_target_color(
        &mut self,
        target: RgbColor,
        duration_in_beats: f32,
        current_index: f32,
    ) {
        let current_hsv = self.color.as_hsv();
        let target_hsv = target.as_hsv();

        self.hue_spline = Some(Spline::from_vec(vec![
            Key::new(
                current_index,
                current_hsv.0,
                splines::Interpolation::Linear,
            ),
            #[allow(clippy::suboptimal_flops)]
            Key::new(
                current_index
                    + DMX_RENDERER_BEAT_IS_INDEX_POINTS * duration_in_beats,
                target_hsv.0,
                splines::Interpolation::Linear,
            ),
        ]));

        self.saturation_spline = Some(Spline::from_vec(vec![
            Key::new(
                current_index,
                current_hsv.1,
                splines::Interpolation::Linear,
            ),
            #[allow(clippy::suboptimal_flops)]
            Key::new(
                current_index
                    + DMX_RENDERER_BEAT_IS_INDEX_POINTS * duration_in_beats,
                target_hsv.1,
                splines::Interpolation::Linear,
            ),
        ]));

        self.blueprint_id = None;
    }
}

impl IsDynamic for DmxFixtureColor {
    fn clear_all_splines(&mut self) {
        self.hue_spline = None;
        self.saturation_spline = None;
        self.blueprint_id = None;
    }
    fn clear_splines_by_blueprint_id(&mut self, blueprint_id: usize) {
        if self.blueprint_id == Some(blueprint_id) {
            self.clear_all_splines();
        }
    }
    fn extract_internal_spline_from(
        &mut self,
        properties: &[PropertyFileDescriptor],
        oneshot: bool,
        blueprint_id: Option<usize>,
    ) {
        for property in properties {
            if let PropertyFileDescriptor::ColorPropertyCoordinates(_, _) =
                property
            {
                if let Some((hue_spline, saturation_spline)) =
                    property.into_color_splines()
                {
                    self.hue_spline = Some(hue_spline);
                    self.saturation_spline = Some(saturation_spline);
                }
                self.oneshot = oneshot;
                self.blueprint_id = blueprint_id;
            }
        }
    }
    fn apply_spline_index(&mut self, index: f32) {
        if let (Some(hue_spline), Some(saturation_spline)) =
            (self.hue_spline.as_mut(), self.saturation_spline.as_mut())
        {
            let mut hue_value = None;
            let mut saturation_value = None;
            let mut should_clear_splines = false;

            if self.oneshot {
                if let (Some(hue_last), Some(sat_last)) =
                    (hue_spline.keys().last(), saturation_spline.keys().last())
                {
                    let end_time = hue_last.t.max(sat_last.t);
                    if index >= end_time {
                        hue_value = Some(hue_last.value);
                        saturation_value = Some(sat_last.value);
                        should_clear_splines = true;
                    }
                }
            }

            if hue_value.is_none() {
                if let Some(sample) = hue_spline.clamped_sample(
                    clamp_index_to_spline_length(index, hue_spline),
                ) {
                    hue_value = Some(sample);
                }
            }

            if saturation_value.is_none() {
                if let Some(sample) = saturation_spline.clamped_sample(
                    clamp_index_to_spline_length(index, saturation_spline),
                ) {
                    saturation_value = Some(sample);
                }
            }

            if let (Some(hue), Some(saturation)) = (hue_value, saturation_value)
            {
                self.color = RgbColor::from_hue_saturation(hue, saturation);
            }

            if should_clear_splines {
                self.clear_all_splines();
            }
        }
    }
}

#[derive(Debug, PartialEq, Eq, Clone, Copy, Serialize, Deserialize, TS)]
#[ts(export)]
pub enum ColorTerm {
    Red,
    Green,
    Blue,
    LightBlue,
    Purple,
    Yellow,
    Pink,
    Orange,
    White,
}

#[derive(Copy, Clone, Serialize, Deserialize, Debug, PartialEq, Eq, TS)]
#[ts(export)]
pub struct RgbColor {
    pub red: u8,
    pub green: u8,
    pub blue: u8,
}
impl RgbColor {
    #[must_use]
    pub const fn new_from(red: u8, green: u8, blue: u8) -> Self {
        Self { red, green, blue }
    }
    #[must_use]
    pub const fn new_dim_red() -> Self {
        Self {
            red: 10,
            green: 0,
            blue: 0,
        }
    }
    #[must_use]
    pub const fn new_black() -> Self {
        Self {
            red: 0,
            green: 0,
            blue: 0,
        }
    }
    #[must_use]
    pub fn new_random() -> Self {
        let random_number: u32 = rand::random();
        let random_vec = random_number.to_le_bytes();
        Self {
            red: random_vec[0],
            green: random_vec[1],
            blue: random_vec[2],
        }
    }
    #[must_use]
    pub fn from_hue_saturation(hue: f32, saturation: f32) -> Self {
        if hue < 0. {
            return Self {
                red: 0,
                green: 0,
                blue: 0,
            };
        }

        let saturation = saturation.clamp(0.0, 1.0);

        let c = saturation;
        #[allow(clippy::modulo_arithmetic)]
        let x = c * (1.0 - ((hue / 60.0) % 2.0 - 1.0).abs());
        let m = 1.0 - saturation;

        let (red, green, blue) = match hue {
            0.0..=60.0 => (c, x, 0.0),
            60.0..=120.0 => (x, c, 0.0),
            120.0..=180.0 => (0.0, c, x),
            180.0..=240.0 => (0.0, x, c),
            240.0..=300.0 => (x, 0.0, c),
            300.0..=360.0 => (c, 0.0, x),
            _ => (0.0, 0.0, 0.0),
        };

        #[allow(
            clippy::cast_sign_loss,
            clippy::cast_possible_truncation,
            clippy::as_conversions
        )]
        Self {
            red: ((red + m) * 255.0) as u8,
            green: ((green + m) * 255.0) as u8,
            blue: ((blue + m) * 255.0) as u8,
        }
    }
    #[allow(clippy::float_cmp)]
    #[must_use]
    pub fn as_hsv(&self) -> (f32, f32, f32) {
        let r = f32::from(self.red) / 255.0;
        let g = f32::from(self.green) / 255.0;
        let b = f32::from(self.blue) / 255.0;

        let max = f32::max(f32::max(r, g), b);
        let min = f32::min(f32::min(r, g), b);
        let delta = max - min;

        let hue = if delta == 0.0 {
            0.0
        } else if max == r {
            60.0 * ((g - b) / delta).rem_euclid(6.0)
        } else if max == g {
            60.0 * ((b - r) / delta + 2.0)
        } else {
            60.0 * ((r - g) / delta + 4.0)
        };

        let saturation = if max == 0.0 { 0.0 } else { delta / max };

        let value = max;

        (hue, saturation, value)
    }
    #[allow(clippy::trivially_copy_pass_by_ref)]
    #[must_use]
    fn as_cmy(&self) -> (f32, f32, f32) {
        let r = f32::from(self.red) / 255.;
        let g = f32::from(self.green) / 255.;
        let b = f32::from(self.blue) / 255.;

        (r - 1., g - 1., b - 1.)
    }
    #[must_use]
    #[allow(clippy::many_single_char_names, clippy::trivially_copy_pass_by_ref)]
    fn as_xyz(&self) -> (f64, f64, f64) {
        const SRGB_TO_XYZ_MATRIX: [[f64; 3]; 3] = [
            [0.412_456_4, 0.357_576_1, 0.180_437_5],
            [0.212_672_9, 0.715_152_2, 0.072_175_0],
            [0.019_333_9, 0.119_192_0, 0.950_304_1],
        ];

        const D65_X: f64 = 0.950_470;
        const D65_Y: f64 = 1.;
        const D65_Z: f64 = 1.088_830;

        let r = f64::from(self.red) / 255.0;
        let g = f64::from(self.green) / 255.0;
        let b = f64::from(self.blue) / 255.0;

        let r_linear = if r <= 0.04045 {
            r / 12.92
        } else {
            ((r + 0.055) / 1.055).powf(2.4)
        };

        let g_linear = if g <= 0.04045 {
            g / 12.92
        } else {
            ((g + 0.055) / 1.055).powf(2.4)
        };

        let b_linear = if b <= 0.04045 {
            b / 12.92
        } else {
            ((b + 0.055) / 1.055).powf(2.4)
        };

        let x = r_linear
            .mul_add(SRGB_TO_XYZ_MATRIX[0][0], g_linear)
            .mul_add(SRGB_TO_XYZ_MATRIX[0][1], b_linear)
            * SRGB_TO_XYZ_MATRIX[0][2];
        let y = r_linear
            .mul_add(SRGB_TO_XYZ_MATRIX[1][0], g_linear)
            .mul_add(g_linear, SRGB_TO_XYZ_MATRIX[1][1])
            .mul_add(b_linear, SRGB_TO_XYZ_MATRIX[1][2]);
        let z = r_linear
            .mul_add(SRGB_TO_XYZ_MATRIX[2][0], g_linear)
            .mul_add(SRGB_TO_XYZ_MATRIX[2][1], b_linear)
            * SRGB_TO_XYZ_MATRIX[2][2];

        (x * D65_X, y * D65_Y, z * D65_Z)
    }
    #[must_use]
    pub fn as_hex(&self) -> String {
        format!("#{:02x}{:02x}{:02x}", self.red, self.green, self.blue)
    }
    #[must_use]
    pub fn from_hex(hex: &str) -> Self {
        let hex = hex.trim_start_matches('#');

        if hex.len() != 6 {
            logging::log(
                format!("Trying to parse invalid hex color: {hex}"),
                logging::LogLevel::Warning,
                false,
            );
            return Self::new_black();
        }

        let Ok(r) = u8::from_str_radix(&hex[0..2], 16) else {
            logging::log(
                format!("Trying to parse invalid hex color: {hex}"),
                logging::LogLevel::Warning,
                false,
            );
            return Self::new_black();
        };
        let Ok(g) = u8::from_str_radix(&hex[2..4], 16) else {
            logging::log(
                format!("Trying to parse invalid hex color: {hex}"),
                logging::LogLevel::Warning,
                false,
            );
            return Self::new_black();
        };
        let Ok(b) = u8::from_str_radix(&hex[4..6], 16) else {
            logging::log(
                format!("Trying to parse invalid hex color: {hex}"),
                logging::LogLevel::Warning,
                false,
            );
            return Self::new_black();
        };

        Self::new_from(r, g, b)
    }
    #[must_use]
    pub fn as_color_term(&self) -> Option<ColorTerm> {
        let (hue, saturation, value) = self.as_hsv();

        if value >= 0.7 {
            if saturation <= 0.2 {
                return Some(ColorTerm::White);
            } else if (0.0..30.0).contains(&hue) {
                return Some(ColorTerm::Red);
            } else if (30.0..60.0).contains(&hue) {
                return Some(ColorTerm::Orange);
            } else if (60.0..90.0).contains(&hue) {
                return Some(ColorTerm::Yellow);
            } else if (90.0..150.0).contains(&hue) {
                return Some(ColorTerm::Green);
            } else if (150.0..210.0).contains(&hue) {
                return Some(ColorTerm::LightBlue);
            } else if (210.0..270.0).contains(&hue) {
                return Some(ColorTerm::Blue);
            } else if (270.0..330.0).contains(&hue) {
                return Some(ColorTerm::Purple);
            } else if (330.0..=360.0).contains(&hue) {
                return Some(ColorTerm::Pink);
            }
        }
        None
    }
}
#[derive(Copy, Clone, Serialize, Deserialize, Debug, PartialEq, Eq, TS)]
#[ts(export)]
pub enum ColorChannel {
    RgbChannels(RgbChannels),
    ColorWheel(ColorWheel),
    CmyChannels(CmyChannels),
    HsvChannels(HsvChannels),
    XyzChannels(XyYChannels),
}
impl DmxChannelEmitter for DmxFixtureColor {
    #[allow(clippy::cast_sign_loss, clippy::cast_possible_truncation)]
    fn compute_dmx_channels(&mut self, _: u8) -> Vec<DmxChannelValue> {
        let mut result_channels: Vec<DmxChannelValue> = vec![];
        match self.channels {
            ColorChannel::RgbChannels(channels) => {
                if let Some(r_channel) = channels.r {
                    result_channels
                        .push((r_channel.into(), self.color.red).into());
                }
                if let Some(g_channel) = channels.g {
                    result_channels
                        .push((g_channel.into(), self.color.green).into());
                }
                if let Some(b_channel) = channels.b {
                    result_channels
                        .push((b_channel.into(), self.color.blue).into());
                }
            }
            ColorChannel::HsvChannels(channels) => {
                let hsv = self.color.as_hsv();
                if let Some(h_channel) = channels.h {
                    result_channels.push(
                        #[allow(clippy::as_conversions)]
                        (h_channel.into(), hsv.0.clamp(0., 255.) as u8).into(),
                    );
                }
                if let Some(s_channel) = channels.s {
                    result_channels.push(
                        #[allow(clippy::as_conversions)]
                        (s_channel.into(), hsv.1.clamp(0., 255.) as u8).into(),
                    );
                }
                if let Some(v_channel) = channels.v {
                    result_channels.push(
                        #[allow(clippy::as_conversions)]
                        (v_channel.into(), hsv.2.clamp(0., 255.) as u8).into(),
                    );
                }
            }
            ColorChannel::CmyChannels(channels) => {
                let cmy = self.color.as_cmy();
                if let Some(c_channel) = channels.c {
                    result_channels.push(
                        #[allow(clippy::as_conversions)]
                        (c_channel.into(), cmy.0.clamp(0., 255.) as u8).into(),
                    );
                }
                if let Some(m_channel) = channels.m {
                    result_channels.push(
                        #[allow(clippy::as_conversions)]
                        (m_channel.into(), cmy.1.clamp(0., 255.) as u8).into(),
                    );
                }
                if let Some(y_channel) = channels.y {
                    result_channels.push(
                        #[allow(clippy::as_conversions)]
                        (y_channel.into(), cmy.2.clamp(0., 255.) as u8).into(),
                    );
                }
            }
            ColorChannel::XyzChannels(channels) => {
                let xyz = self.color.as_xyz();
                if let Some(x_channel) = channels.x {
                    result_channels.push(
                        #[allow(clippy::as_conversions)]
                        (x_channel.into(), xyz.0.clamp(0., 255.) as u8).into(),
                    );
                }
                if let Some(y_channel) = channels.y {
                    result_channels.push(
                        #[allow(clippy::as_conversions)]
                        (y_channel.into(), xyz.1.clamp(0., 255.) as u8).into(),
                    );
                }
                if let Some(yy_channel) = channels.yy {
                    result_channels.push(
                        #[allow(clippy::as_conversions)]
                        (yy_channel.into(), xyz.2.clamp(0., 255.) as u8).into(),
                    );
                }
            }
            ColorChannel::ColorWheel(wheel) => {
                if let Some(term) = self.color.as_color_term() {
                    let color = wheel.channel_value_from_color_terms_enum(term);
                    result_channels.push((wheel.channel, color).into());
                } else {
                    result_channels.push((wheel.channel, 0).into());
                }
            }
        }
        result_channels
    }
}
// TODO: All the values need to be >u8 (usize for convenience)
// Edit: But why?
#[derive(Copy, Clone, Serialize, Deserialize, Debug, PartialEq, Eq, TS)]
#[ts(export)]
pub struct RgbChannels {
    pub r: Option<u8>,
    pub g: Option<u8>,
    pub b: Option<u8>,
}
#[derive(Copy, Clone, Serialize, Deserialize, Debug, PartialEq, Eq, TS)]
#[ts(export)]
pub struct CmyChannels {
    pub c: Option<u8>,
    pub m: Option<u8>,
    pub y: Option<u8>,
}
#[derive(Copy, Clone, Serialize, Deserialize, Debug, PartialEq, Eq, TS)]
#[ts(export)]
pub struct HsvChannels {
    pub h: Option<u8>,
    pub s: Option<u8>,
    pub v: Option<u8>,
}
impl From<RgbColor> for HsvChannels {
    fn from(value: RgbColor) -> Self {
        let hsv = value.as_hsv();
        #[allow(
            clippy::cast_sign_loss,
            clippy::cast_possible_truncation,
            clippy::as_conversions
        )]
        Self {
            h: Some(hsv.0.clamp(u8::MIN.into(), u8::MAX.into()) as u8),
            s: Some(hsv.1.clamp(u8::MIN.into(), u8::MAX.into()) as u8),
            v: Some(hsv.2.clamp(u8::MIN.into(), u8::MAX.into()) as u8),
        }
    }
}
#[derive(Copy, Clone, Serialize, Deserialize, Debug, PartialEq, Eq, TS)]
#[ts(export)]
pub struct XyYChannels {
    pub x: Option<u8>,
    pub y: Option<u8>,
    #[allow(non_snake_case)]
    pub yy: Option<u8>,
}
#[derive(Copy, Clone, Serialize, Deserialize, Debug, PartialEq, Eq, TS)]
#[ts(export)]
// TODO: Make the available color 'slots' more dynamic. (Some wheels might have only r,g,b ...)
pub struct ColorWheel {
    pub channel: usize,
    pub red: u8,
    pub green: u8,
    pub blue: u8,
    pub light_blue: u8,
    pub purple: u8,
    pub yellow: u8,
    pub pink: u8,
    pub orange: u8,
    pub white: u8,
}
impl ColorWheel {
    #[must_use]
    pub const fn channel_value_from_color_terms_enum(
        &self,
        color_term: ColorTerm,
    ) -> u8 {
        match color_term {
            ColorTerm::Red => self.red,
            ColorTerm::Blue => self.blue,
            ColorTerm::Green => self.green,
            ColorTerm::LightBlue => self.light_blue,
            ColorTerm::Purple => self.purple,
            ColorTerm::Yellow => self.yellow,
            ColorTerm::Pink => self.pink,
            ColorTerm::Orange => self.orange,
            ColorTerm::White => self.white,
        }
    }
}

impl PartialEq for DmxFixtureColor {
    fn eq(&self, other: &Self) -> bool {
        self.color.red == other.color.red
            && self.color.green == other.color.green
            && self.color.blue == other.color.blue
            && self.oneshot == other.oneshot
            && self.blueprint_id == other.blueprint_id
    }
}
