<script lang="ts">
	import Button from '$lib/atoms/button.svelte';
	import PanTiltPositionEditor from './partials/PanTiltPositionEditor.svelte';
	import SnippetTriggerConsole from './partials/SnippetTriggerConsole.svelte';
	import FixturePanTiltEditor from './partials/fixturePanTiltEditor.svelte';
	import { networking } from '$lib/stores/networking';
    import { get } from "svelte/store";
</script>

<div class="flex flex-col">
	<div class="flex w-full flex-row justify-center space-x-4">
		<SnippetTriggerConsole></SnippetTriggerConsole>
		<PanTiltPositionEditor></PanTiltPositionEditor>
	</div>
	<div class="flex w-full flex-row justify-center space-x-4 mt-4">
		<FixturePanTiltEditor></FixturePanTiltEditor>
        <div class="w-1/3">
            <Button
                id="reset-snippets-button"
                onclick={() => {
                    if (
                        confirm(
                            "Do you realy want to reset the current state of running snippets?",
                        )
                    ) {
                        fetch(
                            `http://${get(networking)}:${networking.port}/request_snippet_state_reset`,
                            {
                                method: "PUT",
                            },
                        );
                    }
                }}>
                <p>Reset snippet state</p>
            </Button>
        </div>
	</div>
</div>
