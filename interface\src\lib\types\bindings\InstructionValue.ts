// This file was generated by [ts-rs](https://github.com/Aleph-Alpha/ts-rs). Do not edit this file manually.
import type { FunctionGetter } from "./FunctionGetter";
import type { MathOperator } from "./MathOperator";
import type { NumberValue } from "./NumberValue";

export type InstructionValue = { "Number": NumberValue } | { "Variable": string } | { "Function": FunctionGetter } | { "Keypoint": number } | "Value" | "Pressed" | "Released" | "Default" | "Bpm" | "BpmModifier" | { "MathOperator": MathOperator } | { "Random": [InstructionValue, InstructionValue] };