import { writable } from 'svelte/store';
import { get } from 'svelte/store';

export type ToastType = 'success' | 'error' | 'warning' | 'info';

export interface Toast {
    id: number;
    message: string;
    type: ToastType;
    duration?: number;
}

function createToastStore() {
    const { subscribe, update } = writable<Toast[]>([]);

    return {
        subscribe,
        add: (toast: Omit<Toast, 'id'>) => {
            const id = Date.now() + get(toasts).length;
            const newToast: Toast = {
                ...toast,
                id,
                duration: toast.duration ?? 5000
            };

            update(toasts => [...toasts, newToast]);

            if (newToast.duration && newToast.duration > 0) {
                setTimeout(() => {
                    update(toasts => toasts.filter(t => t.id !== id));
                }, newToast.duration);
            }

            return id;
        },
        remove: (id: number) => {
            update(toasts => toasts.filter(t => t.id !== id));
        },
        clear: () => {
            update(() => []);
        }
    };
}

export const toasts = createToastStore();

export const TOAST = {
    success: (message: string, duration?: number) =>
        toasts.add({ message, type: 'success', duration }),
    error: (message: string, duration?: number) =>
        toasts.add({ message, type: 'error', duration }),
    warning: (message: string, duration?: number) =>
        toasts.add({ message, type: 'warning', duration }),
    info: (message: string, duration?: number) =>
        toasts.add({ message, type: 'info', duration })
};
