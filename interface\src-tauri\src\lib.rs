// Learn more about <PERSON>ri commands at https://tauri.app/v1/guides/features/command

use std::{process::Command, time::Duration};
use get_if_addrs::{IfAddr, get_if_addrs};
use ipnetwork::Ipv4Network;
use std::thread;
use serde::{Serialize, Deserialize};

#[derive(Default, Deserialize, Serialize)]
pub struct BackendInfo {
    name: String,
    version: String,
    artnet_sending: bool,
    uptime_seconds: u64,
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_shell::init())
        .invoke_handler(tauri::generate_handler![search_host_ips])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}

#[tauri::command]
async fn search_host_ips() -> Result<Vec<(String, BackendInfo)>, ()> {
    let all_hosts = get_hosts().unwrap();
    let mut rw_backends: Vec<(String, BackendInfo)> = vec![];
    if let Ok(request_client) = reqwest::ClientBuilder::new()
        .timeout(Duration::from_millis(60))
        .build()
    {
        for host in all_hosts {
            let response = request_client
                .get(format!("http://{host}:4000/rw_discover"))
                .send()
                .await;
            if let Ok(response) = response {
                let status = response.status();
                let payload = response
                    .json::<BackendInfo>()
                    .await
                    .unwrap_or_default();
                if status == 200 && payload.name == "ruhige_waldgeraeusche" {
                    rw_backends.push((host, payload));
                }
            }
        }
        return Ok(rw_backends);
    }
    Err(())
}

#[cfg(target_os = "linux")]
fn get_hosts() -> Result<Vec<String>, ()> {
    populate_arp_cache();

    let Ok(output) = Command::new("arp").arg("-a").output() else {
        return Err(());
    };

    let output_str = String::from_utf8_lossy(&output.stdout);
    let mut hosts = Vec::new();

    for line in output_str.lines() {
        let mut ip = line.split_whitespace();
        ip.next();
        if let Some(ip) = ip.next() {
            if ip.starts_with('(') && ip.ends_with(')') {
                let ip = &ip[1..ip.len() - 1];
                hosts.push(ip.to_string());
            }
        }
    }

    Ok(hosts)
}

#[cfg(target_os = "windows")]
fn get_hosts() -> Result<Vec<String>, ()> {
    populate_arp_cache();

    let Ok(output) = Command::new("arp").arg("-a").output() else {
        return Err(());
    };

    let output_str = String::from_utf8_lossy(&output.stdout);
    let mut hosts = Vec::new();

    for line in output_str.lines() {
        let mut ip = line.split_whitespace();
        if let Some(ip) = ip.next() {
                if ip.chars().all(|char| char.is_numeric() || char == '.') {
                    hosts.push(ip.to_string());
                }
        }
    }

    Ok(hosts)
}

fn populate_arp_cache() {
    let interfaces = get_if_addrs().expect("Failed to get network interfaces");

    for interface in interfaces {
        if !interface.is_loopback() {
            if let IfAddr::V4(interface_ip_info) = interface.addr {

                let Ok(network) = Ipv4Network::with_netmask(interface_ip_info.ip, interface_ip_info.netmask) else {
                    return;
                };

                if network.prefix() >= 18 {
                    let mut thread_handles = vec![];
                    for ip in network.iter() {
                        thread_handles.push(thread::spawn(move || {
                            let _ = ping_rs::send_ping(&ip.into(), Duration::from_millis(10), &[], None);
                        }));
                    }

                    for handle in thread_handles {
                        handle.join().unwrap();
                    }
                }
            }
        }
    }
}

// TODO: implement
#[cfg(target_os = "macos")]
fn get_hosts() -> Result<Vec<String>, ()> {
    Err(())
}
