<script lang="ts" module>
    export interface DirectoryItem {
        id: number;
        name: string;
        requires_user_action_reason: string | null;
    }
</script>

<script lang="ts" generics="T extends DirectoryItem, U">
    import type { Directory } from "$lib/types/bindings/Directory";
    import type { TreeItem } from "$lib/types/bindings/TreeItem";
    import { dropzone, draggable } from "$lib/useActions/dragAndDrop";
    import Icon from "$lib/atoms/icon.svelte";
    import Self from "./directory.svelte";
    import RequiresUseractionHint from "$lib/atoms/RequiresUseractionHint.svelte";
    import { teleportToBody } from "$lib/useActions/teleport.svelte";
    import type { Snippet } from "svelte";
    import { TOAST } from "$lib/stores/toast";

    let {
        selectedItem = $bindable(),
        directory,
        createItemAt,
        removeItem,
        createDirectoryAt,
        renameDirectory,
        removeDirectory,
        itemAddName,
        disabled,
        moveItem,
        moveDirectory,
        serializeItem,
        itemUpload,
        rootDirectory,
        level = 0,
    }: {
        selectedItem: T | undefined;
        directory: Directory<T>;
        createItemAt: (parentDirId: number) => U;
        removeItem: (item: T) => U;
        createDirectoryAt: (parentDirId: number) => U;
        renameDirectory: (id: number, newName: string) => U;
        removeDirectory: (id: number) => U;
        itemAddName?: Snippet;
        disabled?: boolean;
        moveItem?: (itemId: number, parentDirId: number) => U;
        moveDirectory?: (directoryId: number, parentDirId: number) => U;
        serializeItem?: (item: T) => { filename: string; content: string };
        itemUpload?: {
            acceptedFiletypes: string[];
            uploadItem: (file: File, parentDirId: number) => Promise<void>;
        };
        rootDirectory?: Directory<T>;
        level?: number;
    } = $props();

    let unfolded = $state(false);

    let isAddTypePromptOpen = $state(false);

    async function addDir() {
        unfolded = true;
        await createDirectoryAt(directory.id);
        isAddTypePromptOpen = false;
    }

    async function createNewItem() {
        unfolded = true;
        await createItemAt(directory.id);
        isAddTypePromptOpen = false;
    }

    function allItemsOf(directory: Directory<T>): T[] {
        let result: T[][] = [];
        directory.content.forEach((item) => {
            if ("Directory" in item) {
                result.push(allItemsOf(item.Directory));
            } else {
                result.push([item.Item]);
            }
        });
        return result.flat();
    }

    function isChildDirectory(
        targetDirId: number,
        searchDir: Directory<T>,
    ): boolean {
        return searchDir.content.some((item) => {
            if ("Directory" in item) {
                return (
                    item.Directory.id === targetDirId ||
                    isChildDirectory(targetDirId, item.Directory)
                );
            }
            return false;
        });
    }

    function findDirectoryById(
        dirId: number,
        searchDir: Directory<T>,
    ): Directory<T> | undefined {
        if (searchDir.id === dirId) {
            return searchDir;
        }

        for (const item of searchDir.content) {
            if ("Directory" in item) {
                const found = findDirectoryById(dirId, item.Directory);
                if (found) {
                    return found;
                }
            }
        }

        return undefined;
    }

    function renameDir() {
        const newName = prompt(`rename ${directory.name} to`);
        if (newName) {
            renameDirectory(directory.id, newName);
        }
    }

    function downloadItem(item: T) {
        if (!serializeItem) {
            TOAST.warning(
                "Download functionality not available for this item type",
            );
            return;
        }

        try {
            const { filename, content } = serializeItem(item);

            const blob = new Blob([content], { type: "application/json" });

            const link = document.createElement("a");
            link.href = URL.createObjectURL(blob);
            link.download = filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            URL.revokeObjectURL(link.href);
        } catch (error) {
            TOAST.error(`Failed to download ${item.name}: ${error}`);
        }
    }

    let sortedDirectory = $derived.by(() => {
        return sortDirectory(directory);
    });

    function sortDirectory(dir: Directory<T>): Directory<T> {
        if (!dir.content) {
            return dir;
        }
        dir.content = dir.content.sort((a: TreeItem<T>, b: TreeItem<T>) => {
            if ("Directory" in a && "Directory" in b) {
                return a.Directory.name.toLowerCase() >
                    b.Directory.name.toLowerCase()
                    ? 1
                    : -1;
            } else if (!("Directory" in a) && !("Directory" in b)) {
                return a.Item.name.toLowerCase() > b.Item.name.toLowerCase()
                    ? 1
                    : -1;
            } else if ("Directory" in a && !("Directory" in b)) {
                return -1;
            } else {
                return 1;
            }
        });
        dir.content = dir.content.map((item: TreeItem<T>) => {
            if ("Directory" in item) {
                return { Directory: sortDirectory(item.Directory) };
            } else {
                return item;
            }
        });
        return dir;
    }

    let fileInput: HTMLElement | undefined = $state();
    let isDragOver = $state(false);

    function triggerFileUpload() {
        fileInput?.click();
    }

    function handleModalFileUpload(
        event: Event & { currentTarget: EventTarget & HTMLInputElement },
    ) {
        const files = event.currentTarget.files;
        if (files && files.length > 0) {
            processFiles(files);
        }
    }

    function handleModalDragOver() {
        isDragOver = true;
    }

    function handleModalDrop(event: DragEvent) {
        isDragOver = false;
        const files = event.dataTransfer?.files;
        if (files && files.length > 0) {
            processFiles(files);
        }
    }

    function handleModalDragLeave(event: DragEvent) {
        if (
            event.currentTarget &&
            event.relatedTarget &&
            event.currentTarget instanceof Element &&
            !event.currentTarget.contains(event.relatedTarget as Node)
        ) {
            isDragOver = false;
        }
    }

    function processFiles(files: FileList) {
        for (let i = 0; i < files.length; i++) {
            const file = files[i];

            itemUpload?.uploadItem(file, directory.id);
        }
        isAddTypePromptOpen = false;
    }
</script>

<div
    class="flex pt-1 justify-between"
    use:dropzone={{
        on_dropzone(data) {
            if (disabled) {
                TOAST.warning(
                    "Dragging is not allowed while the component is disabled",
                );
                return;
            }

            if (data === "") {
                TOAST.warning("Dragging is not allowed for the root directory");
                return;
            }
            const [type, id] = data.split("_");
            const numericId = parseInt(id);

            if (type === "item" && moveItem) {
                moveItem(numericId, directory.id);
            } else if (type === "dir" && moveDirectory) {
                if (numericId === directory.id) {
                    TOAST.warning("Cannot move a directory into itself");
                    return;
                }

                const effectiveRoot = rootDirectory || directory;
                const draggedDirectory = findDirectoryById(
                    numericId,
                    effectiveRoot,
                );

                if (
                    draggedDirectory &&
                    isChildDirectory(directory.id, draggedDirectory)
                ) {
                    TOAST.warning("Cannot move a directory into its children");
                    return;
                }

                moveDirectory(numericId, directory.id);
            } else {
                TOAST.warning(
                    "Move operation not supported for this item type",
                );
            }
        },
        dropEffect: "move",
        dragover_class: "bg-primary/20",
    }}
>
    <RequiresUseractionHint
        reason={!unfolded
            ? allItemsOf(directory)
                  .filter((item) => item.requires_user_action_reason)
                  .map((item) => item.requires_user_action_reason)
                  .join(", ")
            : null}
    >
        <button
            class="flex w-full"
            onclick={() => (unfolded = !unfolded)}
            use:draggable={level > 0 ? `dir_${directory.id}` : ""}
        >
            <div class="pr-1 text-xl text-[#D69661]">
                {#if unfolded}
                    <Icon icon="mdi:folder-open"></Icon>
                {:else}
                    <Icon icon="mdi:folder"></Icon>
                {/if}
            </div>
            <p>{directory.name}</p>
        </button>
    </RequiresUseractionHint>
    <div class="flex justify-end space-x-2">
        {#if level > 0}
            <div class="text-xl">
                <button
                    class:text-disabled={disabled}
                    onclick={renameDir}
                    {disabled}
                >
                    <Icon icon="mdi:rename"></Icon>
                </button>
            </div>
        {/if}
        <div class="text-xl">
            <button
                onclick={() => (isAddTypePromptOpen = !isAddTypePromptOpen)}
                class:text-disabled={disabled}
                {disabled}
            >
                <Icon icon="mdi:add"></Icon>
            </button>
        </div>
        {#if directory.content.length === 0 && level > 0}
            <div class="text-xl">
                <button
                    onclick={() => removeDirectory(directory.id)}
                    class:text-disabled={disabled}
                    {disabled}
                >
                    <Icon icon="mdi:delete"></Icon>
                </button>
            </div>
        {/if}
    </div>
</div>
{#each sortedDirectory.content as item ("Directory" in item ? "dir_" + item.Directory.id : "item_" + item.Item.id)}
    {#if unfolded}
        <div class="ml-4 hover:bg-primary/5">
            {#if "Directory" in item}
                <Self
                    directory={item.Directory}
                    bind:selectedItem
                    {createItemAt}
                    {removeItem}
                    {createDirectoryAt}
                    {renameDirectory}
                    {removeDirectory}
                    {itemAddName}
                    {disabled}
                    {moveItem}
                    {moveDirectory}
                    {serializeItem}
                    {itemUpload}
                    rootDirectory={rootDirectory || directory}
                    level={level + 1}
                />
            {:else}
                <div
                    class="flex justify-between"
                    class:bg-primary={selectedItem
                        ? selectedItem.id === item.Item.id
                        : false}
                    class:bg-opacity-10={selectedItem
                        ? selectedItem.id === item.Item.id
                        : false}
                    use:draggable={`item_${item.Item.id}`}
                >
                    <RequiresUseractionHint
                        reason={item.Item.requires_user_action_reason}
                    >
                        <button
                            class="flex w-full"
                            onclick={() => (selectedItem = item.Item)}
                            {disabled}
                        >
                            <div class="pr-1 text-xl">
                                <Icon
                                    icon="material-symbols:text-snippet-outline"
                                ></Icon>
                            </div>
                            <p>{item.Item.name}</p>
                        </button>
                    </RequiresUseractionHint>
                    <div class="flex text-xl space-x-1">
                        {#if serializeItem}
                            <button
                                onclick={() => downloadItem(item.Item)}
                                class:text-disabled={disabled}
                                {disabled}
                                title="Export/Download"
                            >
                                <Icon icon="mdi:download"></Icon>
                            </button>
                        {/if}
                        <button
                            onclick={() => removeItem(item.Item)}
                            class:text-disabled={disabled}
                            {disabled}
                            title="Delete"
                        >
                            <Icon icon="mdi:delete"></Icon>
                        </button>
                    </div>
                </div>
            {/if}
        </div>
    {/if}
{/each}

<dialog
    open={isAddTypePromptOpen}
    class="absolute rounded-lg border border-input bg-object p-4 shadow-2xl min-w-96"
    style="top: 100px;"
    use:teleportToBody
>
    <button
        class="flex w-full justify-end mb-2"
        onclick={() => (isAddTypePromptOpen = false)}
    >
        <Icon icon="mdi:close"></Icon>
    </button>

    <p class="text-center mb-4">What do you want to add?</p>

    <div class="grid grid-cols-2 gap-4 mb-4">
        <button
            onclick={() => addDir()}
            class="rounded-lg border border-input p-6 hover:bg-primary/5 flex flex-col items-center space-y-2"
        >
            <div class="text-2xl text-[#D69661]">
                <Icon icon="mdi:folder"></Icon>
            </div>
            <p class="text-sm font-medium">FOLDER</p>
        </button>

        <button
            onclick={() => createNewItem()}
            class="rounded-lg border border-input p-6 hover:bg-primary/5 flex flex-col items-center space-y-2"
        >
            <div class="text-2xl text-blue-500">
                <Icon icon="material-symbols:text-snippet-outline"></Icon>
            </div>
            {#if itemAddName}
                {@render itemAddName()}
            {:else}
                <p class="text-sm font-medium">ITEM</p>
            {/if}
        </button>
    </div>

    {#if itemUpload}
        <div
            aria-hidden="true"
            ondragover={(e) => {
                e.preventDefault();
                handleModalDragOver();
            }}
            ondrop={(e) => {
                e.preventDefault();
                handleModalDrop(e);
            }}
            ondragleave={handleModalDragLeave}
            class:drag-over={isDragOver}
            class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-primary/50 transition-colors cursor-pointer"
            class:border-primary={isDragOver}
            class:bg-primary={isDragOver}
            class:bg-opacity-5={isDragOver}
            onclick={() => triggerFileUpload()}
        >
            <div class="text-3xl text-gray-400 mb-2 w-full flex justify-center">
                <Icon icon="mdi:upload"></Icon>
            </div>
            <p class="text-sm text-gray-600 mb-1">
                Drop files here or click to upload
            </p>
            <p class="text-xs text-gray-400">Supports multiple files</p>
        </div>

        <input
            type="file"
            bind:this={fileInput}
            onchange={handleModalFileUpload}
            class="hidden"
            multiple
            accept={itemUpload.acceptedFiletypes.join(", ")}
        />
    {/if}
</dialog>
