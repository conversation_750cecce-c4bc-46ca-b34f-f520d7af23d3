<script lang="ts">
    import Icon from "$lib/atoms/icon.svelte";
    import Select from "$lib/atoms/select.svelte";
    import Button from "$lib/atoms/button.svelte";
    import { onMount } from "svelte";
    import { networking } from "$lib/stores/networking";
    import { get } from "svelte/store";
    import { TOAST } from "$lib/stores/toast";

    let {
        selected_audiofile = $bindable(),
    }: {
        selected_audiofile: number | null;
    } = $props();

    let uploadValue = $state();
    let audiofiles: [number, String][] | undefined = $state();

    onMount(() => all_available_audiofile_names());

    async function all_available_audiofile_names() {
        fetch(
            `http://${get(networking)}:${networking.port}/available_audiofiles`,
        ).then(async (data) => (audiofiles = await data.json()));
    }

    async function upload_audiofile(
        e: Event & { currentTarget: EventTarget & HTMLInputElement },
    ) {
        // @ts-ignore
        let file = e.target.files[0];

        const formData = new FormData();

        formData.append("audiofile", file);

        uploadValue = null;

        fetch(`http://${get(networking)}:${networking.port}/audiofile`, {
            method: "POST",
            body: formData,
        })
            .then((response) => {
                if (response.status === 201) {
                    TOAST.success(`${file.name} uploaded`);
                    all_available_audiofile_names();
                } else {
                    TOAST.error(
                        `Could not upload ${file.name}. Does it already exist?`,
                    );
                }
            })
            .catch((response) =>
                TOAST.error(`Unable to upload ${file.name}: ${response}`),
            );
    }

    function downloadSelectedAudiofile() {
        if (selected_audiofile) {
            var link = document.createElement("a");
            link.download = selected_audiofile.toString();
            link.href = `http://${get(networking)}:${networking.port}/audiofile/${selected_audiofile}`;
            link.click();
        } else {
            TOAST.warning("Please select an audiofile");
        }
    }

    function deleteAudiofile() {
        if (selected_audiofile) {
            fetch(
                `http://${get(networking)}:${networking.port}/audiofile/${selected_audiofile}`,
                {
                    method: "DELETE",
                },
            )
                .then((response) => {
                    if (response.status === 200) {
                        TOAST.success(`${selected_audiofile} deleted`);
                    } else {
                        TOAST.error(`Could not delete ${selected_audiofile}.`);
                    }
                    all_available_audiofile_names();
                })
                .catch((response) =>
                    TOAST.error(
                        `Unable to delete ${selected_audiofile}: ${response}`,
                    ),
                );
        }
    }

    function syncAudiofiles() {
        fetch(`http://${get(networking)}:${networking.port}/audiofiles/sync`, {
            method: "PUT",
        })
            .then((response) => {
                if (response.status === 200) {
                    TOAST.success("Audiofiles synced with filesystem");
                } else {
                    TOAST.error("Could not sync audiofiles");
                }
                all_available_audiofile_names();
            })
            .catch((response) =>
                TOAST.error(`Unable to sync audiofiles: ${response}`),
            );
    }
</script>

<label
    for="dropzone-file"
    class="flex cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-input p-2"
>
    <div class="flex flex-col items-center justify-center pb-6 pt-5">
        <Icon icon="mdi:upload"></Icon>
        <p class="font-semibold">Click to upload</p>
        <p>or drag and drop</p>
        <p class="text-xs">.mp3</p>
    </div>
    <input
        bind:value={uploadValue}
        oninput={upload_audiofile}
        id="dropzone-file"
        type="file"
        class="hidden"
        accept=".mp3"
    />
</label>

<Select bind:value={selected_audiofile}>
    <option value={0}>-</option>
    {#if audiofiles}
        {#each audiofiles as audiofile}
            <option value={audiofile[0]}>{audiofile[1]}</option>
        {/each}
    {/if}
</Select>

<Button id="download-audiofile" onclick={() => downloadSelectedAudiofile()}>
    <Icon icon="mdi:download"></Icon>
</Button>
<Button id="delete-audiofile" onclick={() => deleteAudiofile()}>
    <Icon icon="mdi:garbage-can-empty"></Icon>
</Button>
<Button id="sync-audiofiles" onclick={() => syncAudiofiles()}>
    <Icon icon="mdi:refresh"></Icon>
</Button>
