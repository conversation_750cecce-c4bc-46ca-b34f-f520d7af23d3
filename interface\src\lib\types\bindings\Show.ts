// This file was generated by [ts-rs](https://github.com/Aleph-Alpha/ts-rs). Do not edit this file manually.
import type { BlueprintFileDescriptor } from "./BlueprintFileDescriptor";
import type { ComposedPanTiltPosition } from "./ComposedPanTiltPosition";
import type { Directory } from "./Directory";
import type { DmxFixtureFileDescriptor } from "./DmxFixtureFileDescriptor";
import type { FixtureGroup } from "./FixtureGroup";
import type { Snippet } from "./Snippet";
import type { TimecodeFileDescriptor } from "./TimecodeFileDescriptor";

export interface Show { snippets_dir: Directory<Snippet> | null, blueprints: Array<BlueprintFileDescriptor>, timecodes: Array<TimecodeFileDescriptor>, fixtures: Array<DmxFixtureFileDescriptor>, positions: Array<ComposedPanTiltPosition>, groups: Array<FixtureGroup>, name: string, }