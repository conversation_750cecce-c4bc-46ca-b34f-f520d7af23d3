use serde::{Deserialize, Serialize};
use ts_rs::TS;

pub mod color_channels;
pub mod movement_channels;
pub mod unimplemented_channels;

#[derive(
    Debug, PartialEq, Eq, <PERSON>lone, Copy, Serialize, Deserialize, TS, Default,
)]
#[ts(export)]
pub struct DmxChannelValue {
    pub channel: usize,
    #[serde(skip)]
    pub value: u8,
}
impl From<(usize, u8)> for DmxChannelValue {
    fn from(value: (usize, u8)) -> Self {
        Self {
            channel: value.0,
            value: value.1,
        }
    }
}

pub trait DmxChannelEmitter {
    fn compute_dmx_channels(
        &mut self,
        master_dimmer: u8,
    ) -> Vec<DmxChannelValue>;
}
