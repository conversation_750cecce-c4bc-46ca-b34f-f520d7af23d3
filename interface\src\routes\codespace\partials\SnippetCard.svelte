<script lang="ts" module>
    declare const __APP_VERSION__: string;
</script>

<script lang="ts">
    import Button from "$lib/atoms/button.svelte";
    import LabeledDropdown from "$lib/molecules/labeled_dropdown.svelte";
    import LabeledTextinput from "$lib/molecules/labeled_textinput.svelte";
    import { snippetDir } from "$lib/stores/snippets";
    import type { Snippet } from "$lib/types/bindings/Snippet";
    import Icon from "$lib/atoms/icon.svelte";
    import Directory from "$lib/organisms/directory.svelte";
    import Tooltip from "$lib/atoms/tooltip.svelte";

    let {
        selectedSnippet = $bindable(),
        needsSaving,
        isBuildable,
        updateKeyToRecentlyPressed,
        updateInstructions,
    }: {
        selectedSnippet: Snippet | undefined;
        needsSaving: boolean;
        isBuildable: boolean;
        updateKeyToRecentlyPressed: () => {};
        updateInstructions: () => Promise<void>;
    } = $props();

    function deleteSpecificSnippet(snippet: Snippet): Promise<void> {
        if (confirm(`Do you realy want to delete snippet "${snippet.name}"?`)) {
            if (selectedSnippet && selectedSnippet.id === snippet.id) {
                selectedSnippet = undefined;
            }
            return snippetDir.deleteSnippet(snippet.id);
        } else {
            return new Promise((_, reject) => reject());
        }
    }

    function serializeSnippet(snippet: Snippet): {
        filename: string;
        content: string;
    } {
        const filename = `${snippet.name.replace(/[^a-zA-Z0-9-_]/g, "_")}_${__APP_VERSION__}.snippet`;
        const content = JSON.stringify(snippet, null, 2);
        return { filename, content };
    }
</script>

<Directory
    bind:selectedItem={selectedSnippet}
    directory={$snippetDir}
    createItemAt={(parentDirId) => snippetDir.createDefaultSnippet(parentDirId)}
    removeItem={(snippet) => deleteSpecificSnippet(snippet)}
    createDirectoryAt={(parentDirId) =>
        snippetDir.createDefaultSnippetDirectory(parentDirId)}
    renameDirectory={(id, newName) =>
        snippetDir.setSnippetDirectoryName(id, newName)}
    removeDirectory={(id) => snippetDir.deleteSnippetDirectory(id)}
    moveItem={(snippetId, parentDirId) =>
        snippetDir.moveSnippetToDirectory(snippetId, parentDirId)}
    moveDirectory={(directoryId, parentDirId) =>
        snippetDir.moveSnippetDirectoryToDirectory(directoryId, parentDirId)}
    disabled={needsSaving}
    serializeItem={serializeSnippet}
    itemUpload={{
        uploadItem: (file, parentDirId) =>
            snippetDir.uploadSnippet(file, parentDirId),
        acceptedFiletypes: [".snippet"],
    }}
>
    {#snippet itemAddName()}
        <p>SNIPPET</p>
    {/snippet}
</Directory>
{#key needsSaving && isBuildable}
    <div class="mb-2">
        {#if needsSaving}
            {#if !isBuildable}
                <Tooltip>
                    <button
                        class="w-full rounded-lg bg-accent/20 p-4 text-3xl text-yellow-500"
                        onclick={() => updateInstructions()}
                    >
                        <div class="flex justify-center">
                            <div class="animate-shake-once">
                                <Icon icon="material-symbols:warning"></Icon>
                            </div>
                        </div>
                    </button>
                    {#snippet tooltip()}
                        <p>
                            This snippet cannot be used by RW, because it is not
                            buildable right now. The snippet is stored in this
                            application until it becomes buildable again. The
                            previously working build is stored in RW for savety
                            reasons, but it is NOT USED, until we say so. To
                            make a snippet usable for RW, connect all blocks.
                            Also get rid of any excramation triangles on the
                            blocks (Clicking on them reveals why they are
                            displayed)
                        </p>
                    {/snippet}
                </Tooltip>
            {:else if selectedSnippet?.do_not_use_instructions && !(`snippet-${selectedSnippet.id}` in localStorage)}
                <Tooltip>
                    <button
                        class="w-full rounded-lg bg-accent/20 p-4 text-3xl text-red-500"
                        onclick={() => {
                            if (
                                confirm(
                                    "Do you realy want to overwrite the currently saved snippet?",
                                )
                            ) {
                                updateInstructions();
                            }
                        }}
                    >
                        <div class="flex justify-center">
                            <div class="animate-shake-once">
                                <Icon icon="material-symbols:warning"></Icon>
                            </div>
                        </div>
                    </button>
                    {#snippet tooltip()}
                        <p>
                            It seems, that the snippet you have selected was not
                            buildable before, so it got stored in another
                            desktop application . You see the last that got
                            successfully saved to the database.
                        </p>
                    {/snippet}
                </Tooltip>
            {:else}
                <button
                    class="w-full rounded-lg bg-accent/20 p-4 text-3xl text-blue-500"
                    onclick={() => updateInstructions()}
                >
                    <div class="flex justify-center">
                        <div class="animate-shake-once">
                            <Icon icon="material-symbols:save"></Icon>
                        </div>
                    </div>
                </button>
            {/if}
        {:else}
            <div
                class="flex justify-center rounded-lg bg-object p-4 text-3xl text-green-500"
            >
                <Icon icon="material-symbols:save"></Icon>
                <div class="absolute animate-ping-once">
                    <Icon icon="material-symbols:save"></Icon>
                </div>
            </div>
        {/if}
    </div>
{/key}
<div class="mt-2 flex flex-col space-y-2">
    {#if selectedSnippet}
        <Tooltip>
            <LabeledTextinput
                bind:value={selectedSnippet.name}
                label="Name"
                onchange={() => {
                    if (selectedSnippet) {
                        snippetDir.setSnippetName(
                            selectedSnippet.id,
                            selectedSnippet.name,
                        );
                    }
                }}
                disabled={needsSaving}
            />
            {#snippet tooltip()}
                {#if selectedSnippet && selectedSnippet.category === "Callable"}
                    <p>
                        Please pay attention to giving the snippet a unique
                        name!
                    </p>
                {/if}
            {/snippet}
        </Tooltip>
        <LabeledDropdown
            bind:value={selectedSnippet.category}
            label="Category"
            onchange={() => {
                if (selectedSnippet) {
                    if (selectedSnippet.category !== "Key" && selectedSnippet.serial_module_key !== null) {
                        selectedSnippet.serial_module_key = null;
                        snippetDir.setSnippetSerialModuleKey(
                            selectedSnippet.id,
                            null,
                        );
                    }
                    snippetDir.setSnippetCategory(
                        selectedSnippet.id,
                        selectedSnippet.category,
                    );
                }
            }}
            disabled={needsSaving}
        >
            <option value="Startup">Startup</option>
            <option value="Key">Key</option>
            <option value="Watcher">Watcher</option>
            <option value="Callable">Callable</option>
        </LabeledDropdown>
        <div class="flex justify-evenly space-x-2">
            {#if selectedSnippet.category === "Key"}
                <Button
                    id="update-key-button"
                    onclick={() => {
                        updateKeyToRecentlyPressed();
                    }}
                    disabled={needsSaving}
                >
                    {#if selectedSnippet.serial_module_key === null}
                        <div class="text-warning">
                            <Icon icon="bi:hand-index-fill"></Icon>
                            Please set a key!
                        </div>
                    {:else}
                        <div class="flex">
                            {selectedSnippet.serial_module_key}
                            <div class="ml-1 mt-1">
                                <Icon icon="bi:hand-index-fill"></Icon>
                            </div>
                        </div>
                    {/if}
                </Button>
            {/if}
        </div>
    {/if}
</div>
