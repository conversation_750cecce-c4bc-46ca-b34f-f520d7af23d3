use mysql::{prelude::Queryable, PooledConn};

use crate::{
    dmx_renderer::{
        channel::color_channels::RgbColor,
        dynamics::{
            property::{
                ColorPropertyCoordinate, UnimplementedChannelPropertyCoordinate,
            },
            timecode::{
                self, TimecodeFileDescriptor, TimedPropertyFileDescriptor,
                Track,
            },
        },
        DEFAULT_BPM,
    },
    logging,
};

use super::DbHandler;

fn saturate_track_metadata(
    db_connection: &mut PooledConn,
    track: &mut Track,
    id: usize,
) {
    db_connection
        .query_map(
            format!(
                "
                    SELECT
                        t.id,
                        t.name,
                        t.requires_user_action_reason
                    FROM tracks t
                    WHERE t.id = {id}
                "
            ),
            |(id, name, requires_user_action_reason)| {
                track.id = id;
                track.name = name;
                track.requires_user_action_reason = requires_user_action_reason;
            },
        )
        .unwrap_or_else(|err| {
            logging::log(
                format!("{err:?}\n        (Saturating track metadata)"),
                logging::LogLevel::DbError,
                true,
            );
            vec![]
        });
}

#[allow(clippy::too_many_lines)]
fn saturate_timecode_properties(
    db_connection: &mut PooledConn,
    id: usize,
) -> Vec<(TimedPropertyFileDescriptor, usize)> {
    let mut tmp_props: Vec<(TimedPropertyFileDescriptor, usize)> = vec![];
    #[allow(clippy::type_complexity)]
        db_connection.query_map(
            format!(
                "
                    SELECT
                        props.variant, pc.x, pc.y, cpc.x AS color_x, cpc.red, cpc.green, cpc.blue, props.x_offset, props.id, props.snippet_id
                    FROM tracks t
                    JOIN properties props on props.track_id = t.id
                    LEFT JOIN property_coordinates pc on props.id = pc.property_id
                    LEFT JOIN color_property_coordinates cpc ON cpc.property_id = props.id
                    WHERE t.id =  {id}
                "
            ),
            |( variant, x, y, color_x, red, green, blue, x_offset, props_id, props_snippet_id):
                (String, Option<f32>, Option<f32>, Option<f32>, Option<u8>, Option<u8>, Option<u8>, Option<usize>, usize, Option<usize>)| {
                match variant.as_str() {
                    "SnippetCall" => {
                        if let Some(snippet_id) = props_snippet_id {
                            tmp_props.push(
                                (
                                    TimedPropertyFileDescriptor::CallSnippet(
                                        snippet_id,
                                        x_offset.unwrap_or_default(),
                                        vec![]
                                    ),
                                    props_id
                                    )
                                );
                        }
                    }
                    "color_property_coordinates" => {
                        if let Some((TimedPropertyFileDescriptor::ColorPropertyCoordinates(color_property, _, _), _)) = tmp_props.iter_mut().find(|property| {
                            matches!(property, (TimedPropertyFileDescriptor::ColorPropertyCoordinates(_, _, _), _)) && props_id == property.1
                        }) {
                            color_property
                                .push(
                                    ColorPropertyCoordinate{
                                        x: color_x.unwrap_or_default(),
                                        color: RgbColor{
                                            red: red.unwrap_or_default(),
                                            green: green.unwrap_or_default(),
                                            blue: blue.unwrap_or_default(),
                                        }
                                    }
                                );

                        } else {
                            tmp_props.push(
                                (TimedPropertyFileDescriptor::ColorPropertyCoordinates(
                                    vec![
                                    ColorPropertyCoordinate {
                                        x: color_x.unwrap_or_default(),
                                        color: RgbColor {
                                            red: red.unwrap_or_default(),
                                            green: green.unwrap_or_default(),
                                            blue: blue.unwrap_or_default(),
                                            }
                                        }
                                    ],
                                    x_offset.unwrap_or_default(),
                                    vec![]
                                ), props_id)
                            );
                        }
                    }
                    name => {
                        if let Some((TimedPropertyFileDescriptor::UnimplementedChannel(ref mut property), _)) = tmp_props.iter_mut().find(|property| {
                            if let (TimedPropertyFileDescriptor::UnimplementedChannel(uchannel), _) = property {
                                uchannel.0 == name && props_id == property.1
                            } else {
                                false
                            }
                        }) {
                            property.1.push(UnimplementedChannelPropertyCoordinate {
                                x: x.unwrap_or_default(),
                                y: y.unwrap_or_default()
                            });
                        }
                         else {
                            tmp_props.push(
                                (TimedPropertyFileDescriptor::UnimplementedChannel(
                                    (
                                        name.to_owned(),
                                        vec![UnimplementedChannelPropertyCoordinate {
                                            x: x.unwrap_or_default(),
                                            y: y.unwrap_or_default()
                                        }],
                                        x_offset.unwrap_or_default(),
                                        vec![]
                                    )
                                ), props_id)
                            );
                        }
                    }
                }
            },
        )
        .unwrap_or_else(|err| {
            logging::log(
                format!("{err:?}\n        (Saturating properties in timecode-tracks)"),
                logging::LogLevel::DbError,
                true,
            );
            vec![]
        });
    tmp_props
}

fn saturate_property_fixtures(
    db_connection: &mut PooledConn,
    property: &mut (TimedPropertyFileDescriptor, usize),
) {
    match &mut property.0 {
        TimedPropertyFileDescriptor::ColorPropertyCoordinates(
            _,
            _,
            ref mut fixture_ids,
        ) => {
            db_connection
                .query_map(
                    format!(
                        "
                                SELECT
                                    fx.id
                                FROM property_fixtures pf
                                JOIN  properties p ON p.id = pf.property_id
                                JOIN  fixtures fx ON fx.id = pf.fixture_id
                                WHERE p.id = {}
                            ",
                        property.1
                    ),
                    |id: usize| {
                        fixture_ids.push(id);
                    },
                )
                .unwrap_or_else(|err| {
                    logging::log(
                        format!(
                            "{err:?}\n        (Collecting property_fixtures)",
                        ),
                        logging::LogLevel::DbError,
                        true,
                    );
                    vec![]
                });
        }
        TimedPropertyFileDescriptor::CallSnippet(_, _, ref mut fixture_ids) => {
            db_connection
                .query_map(
                    format!(
                        "
                                SELECT
                                    fx.id
                                FROM property_fixtures pf
                                JOIN  properties p ON p.id = pf.property_id
                                JOIN  fixtures fx ON fx.id = pf.fixture_id
                                WHERE p.id = {}
                            ",
                        property.1
                    ),
                    |id: usize| {
                        fixture_ids.push(id);
                    },
                )
                .unwrap_or_else(|err| {
                    logging::log(
                        format!(
                            "{err:?}\n        (Collecting property_fixtures)",
                        ),
                        logging::LogLevel::DbError,
                        true,
                    );
                    vec![]
                });
        }
        TimedPropertyFileDescriptor::UnimplementedChannel(ref mut tmp_prop) => {
            db_connection
                .query_map(
                    format!(
                        "
                                SELECT
                                    fx.id
                                FROM property_fixtures pf
                                JOIN  properties p ON p.id = pf.property_id
                                JOIN  fixtures fx ON fx.id = pf.fixture_id
                                WHERE p.id = {}
                            ",
                        property.1
                    ),
                    |id: usize| {
                        tmp_prop.3.push(id);
                    },
                )
                .unwrap_or_else(|err| {
                    logging::log(
                        format!(
                            "{err:?}\n        (Collecting property_fixtures)",
                        ),
                        logging::LogLevel::DbError,
                        true,
                    );
                    vec![]
                });
        }
    }
}

impl DbHandler {
    #[must_use]
    pub fn track(db_connection: &mut PooledConn, id: usize) -> Track {
        let mut result: Track = Track {
            properties: vec![],
            name: String::new(),
            id: 0,
            requires_user_action_reason: None,
        };

        saturate_track_metadata(db_connection, &mut result, id);

        let mut tmp_props = saturate_timecode_properties(db_connection, id);

        for prop in &mut tmp_props {
            saturate_property_fixtures(db_connection, prop);
        }
        result.properties = tmp_props
            .iter()
            .map(|indexed_prop| indexed_prop.0.clone())
            .collect();

        result
            .properties
            .iter_mut()
            .for_each(TimedPropertyFileDescriptor::sort);

        result
    }
    #[must_use]
    pub fn timecodes_for_active_show(
        db_connection: &mut PooledConn,
    ) -> Vec<TimecodeFileDescriptor> {
        let mut timecodes = vec![];

        db_connection.query_map(
                "
                    SELECT
                        t.id,
                        t.name,
                        t.requires_user_action_reason,
                        t.audiofile_id,
                        t.bpm
                    FROM timecodes t
                    JOIN shows s ON t.show_id = s.id
                    WHERE s.active
                "
            ,
            | (
                id,
                name,
                requires_user_action_reason,
                audiofile_id,
                bpm
            ):(
                usize,
                Option<String>,
                Option<String>,
                Option<usize>,
                Option<usize>
            ) | {
                timecodes.push(TimecodeFileDescriptor{
                    id,
                    tracks: vec![],
                    name: name.unwrap_or_default(),
                    requires_user_action_reason,
                    audiofile_id,
                    bpm
                });
            },
            ).unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Collecting timecodes for active show)"),
                    logging::LogLevel::DbError,
                    true,
                );
                vec![]
            });
        for timecode in &mut timecodes {
            db_connection.query_map(
                format!(
                    "
                        SELECT tracks.id
                        FROM tracks
                        JOIN  timecodes ON timecodes.id = tracks.timecode_id
                        WHERE timecodes.id = {}
                    ",
                    timecode.id
                )
            ,
            | id: usize | {
                timecode.tracks.push(Track{
                    id,
                    name: String::new(),
                    properties: vec![],
                    requires_user_action_reason: None
                });
            },
            ).unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Collecting tracks for timecode ({})", timecode.id),
                    logging::LogLevel::DbError,
                    true,
                );
                vec![]
            });
        }
        for timecode in &mut timecodes {
            for track in &mut timecode.tracks {
                *track = Self::track(db_connection, track.id);
            }
        }
        timecodes
    }
    pub fn create_default_timecode_in_active_show(&mut self) -> usize {
        self.db_connection().query_drop(format!(
            "
                INSERT INTO timecodes
                    (
                        name,
                        requires_user_action_reason,
                        show_id,
                        bpm
                    )
                VALUES ('{}', '{:?}', (SELECT id FROM shows WHERE active), {})
            ",
            timecode::DEFAULT_NAME,
            timecode::DEFAULT_REQUIRES_USER_ACTION_REASON.map_or("NULL", |e| e),
            DEFAULT_BPM
            )).unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Creating default timecode in active show)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        let mut timecode_id = 0;
        self.db_connection()
            .query_map(
                "
                    SELECT MAX(id) FROM timecodes

                    ",
                |id| timecode_id = id,
            )
            .unwrap_or_else(|err| {
                logging::log(
                    format!(
                        "{err:?}\n        (Selecting the just-created timecode)"
                    ),
                    logging::LogLevel::DbError,
                    true,
                );
                vec![]
            });
        self.active_show.timecodes =
            Self::timecodes_for_active_show(&mut self.db_connection());
        timecode_id
    }
    pub fn update_timecode(&mut self, timecode: &TimecodeFileDescriptor) {
        self.db_connection()
            .query_drop(format!(
                "
                    UPDATE timecodes t SET
                        t.name = '{}',
                        t.requires_user_action_reason = '{}',
                        t.audiofile_id = {},
                        t.bpm = {}
                    WHERE t.id = {}
                ",
                timecode.name,
                timecode
                    .requires_user_action_reason
                    .as_ref()
                    .unwrap_or(&String::new()),
                timecode
                    .audiofile_id
                    .map_or("NULL".to_owned(), |id| id.to_string()),
                timecode.bpm.map_or(DEFAULT_BPM, |bpm| u16::try_from(bpm)
                    .unwrap_or(u16::MAX)),
                timecode.id
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!(
                        "{err:?}\n        (Updating timecode {})",
                        timecode.id
                    ),
                    logging::LogLevel::DbError,
                    true,
                );
            });

        self.db_connection()
            .query_drop(format!(
                "
                    DELETE t
                    FROM tracks t
                    JOIN timecodes tc ON tc.id = t.timecode_id
                    WHERE tc.id = {}
                ",
                timecode.id
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!(
                        "{err:?}\n        (Removing outdated timecode tracks)",
                    ),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        for track in &timecode.tracks {
            self.db_connection()
                .query_drop(format!(
                    "
                            INSERT INTO tracks
                            (
                                name,
                                requires_user_action_reason,
                                timecode_id
                            )
                            VALUES
                            (
                                '{}',
                                '{}',
                                {}
                            );
                        ",
                    track.name,
                    track
                        .requires_user_action_reason
                        .clone()
                        .unwrap_or_default(),
                    timecode.id
                ))
                .unwrap_or_else(|err| {
                    logging::log(
                        format!(
                            "{err:?}\n        (Inserting updated timecode tracks)",
                        ),
                        logging::LogLevel::DbError,
                        true,
                    );
                });
            for property in &track.properties {
                persist_timecode_property(&mut self.db_connection(), property);
            }
        }
        self.active_show.timecodes =
            Self::timecodes_for_active_show(&mut self.db_connection());
    }
    pub fn delete_timecode(&mut self, id: usize) {
        self.db_connection()
            .query_drop(format!(
                "
                    DELETE FROM timecodes WHERE id = {id}
                "
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Deleting timecode {id})"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        self.active_show.timecodes =
            Self::timecodes_for_active_show(&mut self.db_connection());
        self.publish_changes();
    }
}

#[allow(clippy::too_many_lines)]
fn persist_timecode_property(
    db_connection: &mut PooledConn,
    property: &TimedPropertyFileDescriptor,
) {
    match property {
        TimedPropertyFileDescriptor::CallSnippet(
            snippet_id,
            x_offset,
            fixture_ids,
        ) => {
            db_connection
                .query_drop(format!(
                    "
                        INSERT INTO properties
                        (variant, track_id, x_offset, snippet_id)
                        VALUES ( 'SnippetCall', (SELECT MAX(id) FROM tracks), {x_offset}, {snippet_id})
                    "
                ))
                .unwrap_or_else(|err| {
                    logging::log(
                        format!(
                    "{err:?}\n        (Inserting updated snippetcall for track)"
                ),
                        logging::LogLevel::DbError,
                        true,
                    );
                });
            for fixture_id in fixture_ids {
                db_connection
                    .query_drop(format!(
                        "
                            INSERT INTO property_fixtures
                            (property_id, fixture_id)
                            VALUES ((SELECT MAX(id) FROM properties), {fixture_id})
                        ",
                    ))
                    .unwrap_or_else(|err| {
                        logging::log(
                            format!("{err:?}\n        (Inserting updated property_fixtures)",),
                            logging::LogLevel::DbError,
                            true,
                        );
                    });
            }
        }
        TimedPropertyFileDescriptor::UnimplementedChannel(channel) => {
            db_connection
                .query_drop(format!(
                    "
                INSERT INTO properties
                (variant, track_id, x_offset)
                VALUES (
                        '{}',
                        (SELECT MAX(id) FROM tracks)
                        , {}
                    )
                ",
                    channel.0, channel.2
                ))
                .unwrap_or_else(|err| {
                    logging::log(
                        format!(
                        "{err:?}\n        (Inserting updated custom property for track)"
                ),
                        logging::LogLevel::DbError,
                        true,
                    );
                });
            for coordinate in &channel.1 {
                db_connection
                    .query_drop(format!(
                        "
                            INSERT INTO property_coordinates
                            (x, y, property_id)
                            VALUES ({}, {}, (SELECT MAX(id) FROM properties))
                        ",
                        coordinate.x,
                        coordinate.y
                    ))
                    .unwrap_or_else(|err| {
                        logging::log(
                            format!("{err:?}\n        (Inserting updated property_coordinates)",),
                            logging::LogLevel::DbError,
                            true,
                        );
                    });
            }
            for fixture_id in &channel.3 {
                db_connection
                    .query_drop(format!(
                        "
                            INSERT INTO property_fixtures
                            (property_id, fixture_id)
                            VALUES ((SELECT MAX(id) FROM properties), {fixture_id})
                        ",
                    ))
                    .unwrap_or_else(|err| {
                        logging::log(
                            format!("{err:?}\n        (Inserting updated property_fixtures)",),
                            logging::LogLevel::DbError,
                            true,
                        );
                    });
            }
        }
        TimedPropertyFileDescriptor::ColorPropertyCoordinates(
            channel,
            x_offset,
            fixture_ids,
        ) => {
            db_connection
                .query_drop(format!(
                    "
                        INSERT INTO properties
                        (variant, track_id, x_offset)
                        VALUES ( 'color_property_coordinates', (SELECT MAX(id) FROM tracks), {x_offset})
                    "
                ))
                .unwrap_or_else(|err| {
                    logging::log(
                        format!(
                    "{err:?}\n        (Inserting updated color_property_coordinates for track)"
                ),
                        logging::LogLevel::DbError,
                        true,
                    );
                });
            for coordinate in channel {
                db_connection
                    .query_drop(format!(
                        "
                            INSERT INTO color_property_coordinates
                            (x, red, green, blue, property_id)
                            VALUES ({}, {}, {}, {}, (SELECT MAX(id) FROM properties))
                        ",
                        coordinate.x,
                        coordinate.color.red,
                        coordinate.color.green,
                        coordinate.color.blue
                    ))
                    .unwrap_or_else(|err| {
                        logging::log(
                            format!("{err:?}\n        (Inserting updated color_property_coordinates)"),
                            logging::LogLevel::DbError,
                            true,
                        );
                    });
            }
            for fixture_id in fixture_ids {
                db_connection
                    .query_drop(format!(
                        "
                            INSERT INTO property_fixtures
                            (property_id, fixture_id)
                            VALUES ((SELECT MAX(id) FROM properties), {fixture_id})
                        ",
                    ))
                    .unwrap_or_else(|err| {
                        logging::log(
                            format!("{err:?}\n        (Inserting updated property_fixtures)",),
                            logging::LogLevel::DbError,
                            true,
                        );
                    });
            }
        }
    }
}
