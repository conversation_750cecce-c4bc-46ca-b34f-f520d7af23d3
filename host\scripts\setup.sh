#!/bin/bash

# Prepares the host-machine for running the rw-backend, including installing dependency and setting up the database

if [ "$EUID" -ne 0 ]; then
  echo "Please run as root (use sudo)"
  exit 1
fi

# Add `pi ALL=(ALL) NOPASSWD: /sbin/shutdown` to sudoer file (sudo visudo) at the END, if you want to control the power of the controller remotly

sudo apt-get install tmux mysql-server -y

sudo systemctl start mysql

while ! systemctl is-active --quiet mysql; do
    echo "Waiting for MySQL to start..."
    sleep 1
done
echo "MySQL is up"

DB_NAME="rw_db"
DB_USER="rw_usr"
DB_PASS="rw_pass"
MYSQL_ROOT_PASS="root"

sudo mysql -e "CREATE DATABASE $DB_NAME;" --user=root --password=root
sudo mysql -e "CREATE DATABASE ${DB_NAME}_dev;" --user=root --password=root
sudo mysql -e "CREATE USER '$DB_USER'@'localhost' IDENTIFIED BY '$DB_PASS';" --user=root --password=root
sudo mysql -e "GRANT ALL PRIVILEGES ON $DB_NAME.* TO '$DB_USER'@'localhost';" --user=root --password=root
sudo mysql -e "GRANT ALL PRIVILEGES ON ${DB_NAME}_dev.* TO '$DB_USER'@'localhost';" --user=root --password=root
sudo mysql -e "FLUSH PRIVILEGES;" --user=root --password=root

echo "Setup complete"
