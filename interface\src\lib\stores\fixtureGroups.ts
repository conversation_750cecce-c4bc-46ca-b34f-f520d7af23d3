import { get, writable } from 'svelte/store';
import { shows } from './shows';
import { DmxFixtureFileDescriptor } from '$lib/types/bindings/DmxFixtureFileDescriptor';
import { fixtures } from '$lib/stores/fixtures';
import { FixtureGroup } from '$lib/types/bindings/FixtureGroup';
import { networking } from './networking';

function createFixtureGroupStore() {
    const { subscribe, set } = writable<FixtureGroup[]>([]);

    shows.subscribe(() => fetchFixtureGroups().then(data => set(data)));
    fixtures.subscribe(() => fetchFixtureGroups().then(data => set(data)));

    return {
        subscribe,
        patchOne: (fixtureGroup: FixtureGroup) =>
            patchFixtureGroup(fixtureGroup).then(data => { set(data); return true }),
        createOne: (newGroup: FixtureGroup) =>
            createFixtureGroup(newGroup).then(data => set(data)),
        deleteOne: (fixtureGroup: FixtureGroup) =>
            deleteFixtureGroup(fixtureGroup).then(data => set(data)),
    };
}

export const fixtureGroups = createFixtureGroupStore();

async function fetchFixtureGroups(): Promise<FixtureGroup[]> {
    return new Promise(async (resolve, _) => {
        const ip = get(networking);
        if (ip) {
            const response = await fetch(`http://${ip}:${networking.port}/fixturegroups`);
            resolve(response.json());
        } else {
            setTimeout(async () => resolve(await fetchFixtureGroups()), 1000)
        }
    })
}

async function createFixtureGroup(newGroup: FixtureGroup): Promise<FixtureGroup[]> {
    await fetch(`http://${get(networking)}:${networking.port}/fixturegroup`, {
        method: 'POST',
        body: JSON.stringify(newGroup),
        headers: new Headers({ 'content-type': 'application/json' }),
    });
    return fetchFixtureGroups();
}
async function patchFixtureGroup(
    fixtureGroup: FixtureGroup,
): Promise<FixtureGroup[]> {
    if (fixtureGroup.id !== null) {
        await fetch(`http://${get(networking)}:${networking.port}/fixturegroup`, {
            method: 'PATCH',
            body: JSON.stringify(fixtureGroup),
            headers: new Headers({ 'content-type': 'application/json' }),
        });
    }
    return fetchFixtureGroups();
}
async function deleteFixtureGroup(
    fixtureGroup: FixtureGroup,
): Promise<FixtureGroup[]> {
    if (fixtureGroup.id !== null) {
        await fetch(`http://${get(networking)}:${networking.port}/fixturegroup/${fixtureGroup.id}`, {
            method: 'DELETE',
        });
    }
    return fetchFixtureGroups();
}

export function fixturesForGroup(needleName: string): DmxFixtureFileDescriptor[] {
    const fixtureGroup = get(fixtureGroups).find(group => group.name === needleName);
    if (fixtureGroup) {
        return get(fixtures).filter(fixture => {
            if (fixture.id !== null) {
                return fixtureGroup.fixture_ids.includes(fixture.id)
            }
            else {
                return false
            }
        })
    } else {
        return []
    }
}
