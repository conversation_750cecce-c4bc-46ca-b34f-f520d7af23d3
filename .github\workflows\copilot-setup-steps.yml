name: "Copilot Setup Steps"

on:
  workflow_dispatch:
  push:
    paths:
      - .github/workflows/copilot-setup-steps.yml
  pull_request:
    paths:
      - .github/workflows/copilot-setup-steps.yml

jobs:
  copilot-setup-steps:
    runs-on: ubuntu-latest

    permissions:
      contents: read

    steps:
      - name: Install MariaDB
        run: |
          # sudo apt install mysql-server
          # sudo systemctl start mysql
          sudo /etc/init.d/mysql start
          mysql -uroot  -e "CREATE DATABASE rw_db_dev;" -proot
          mysql -uroot -e "CREATE USER 'rw_usr'@'localhost' IDENTIFIED BY 'rw_pass';" -proot
          mysql -uroot -e "GRANT ALL PRIVILEGES ON rw_db_dev.* TO 'rw_usr'@'localhost';" -proot
          mysql -uroot -e "FLUSH PRIVILEGES;" -proot
          
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          cache-dependency-path: 'interface'
          node-version: "20"
          cache: "npm"

      - name: Install frontend dependencies
        run: npm ci
        working-directory: interface

      - name: Install e2e-test dependencies
        run: npm ci

      - name: install host deps
        working-directory: host
        run: sudo apt install libudev-dev mold

      - name: Build Host
        working-directory: host
        run: cargo build
