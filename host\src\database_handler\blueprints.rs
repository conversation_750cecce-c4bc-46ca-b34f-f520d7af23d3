use mysql::{prelude::Queryable, PooledConn};

use crate::{
    dmx_renderer::{
        channel::color_channels::RgbColor,
        dynamics::{
            blueprint::{
                self, BlueprintFileDescriptor, RegisteredBlueprintFixtureDelay,
            },
            property::{
                ColorPropertyCoordinate, PanTiltPositionPropertyCoordinate,
                PropertyFileDescriptor, UnimplementedChannelPropertyCoordinate,
            },
        },
    },
    logging,
};

use super::DbHandler;

fn saturate_blueprint_metadata(
    db_connection: &mut PooledConn,
    id: usize,
    blueprint_filedescriptor: &mut BlueprintFileDescriptor,
) {
    db_connection
        .query_map(
            format!(
                "
                    SELECT
                        bp.id,
                        bp.name,
                        bp.requires_user_action_reason
                    FROM blueprints bp
                    WHERE bp.id = {id}
                "
            ),
            |(id, name, requires_user_action_reason)| {
                blueprint_filedescriptor.id = id;
                blueprint_filedescriptor.name = name;
                blueprint_filedescriptor.requires_user_action_reason =
                    requires_user_action_reason;
            },
        )
        .unwrap_or_else(|err| {
            logging::log(
                format!("{err:?}\n        (Saturating blueprint metadata)"),
                logging::LogLevel::DbError,
                true,
            );
            vec![]
        });
}

#[allow(clippy::too_many_lines)]
fn saturate_blueprint_properties(
    db_connection: &mut PooledConn,
    id: usize,
    blueprint_filedescriptor: &mut BlueprintFileDescriptor,
) {
    #[allow(clippy::type_complexity)]
        db_connection.query_map(
            format!(
                "
                    SELECT
                        props.variant,
                        props.interpolation_method,
                        pc.x,
                        pc.y,
                        cpc.x AS color_x,
                        cpc.red,
                        cpc.green,
                        cpc.blue,
                        ptpp.position_id AS position_id,
                        ptpp.x AS position_x,
                        ptp.name AS position_name
                    FROM blueprints bp
                    JOIN properties props on props.blueprint_id = bp.id
                    LEFT JOIN property_coordinates pc on props.id = pc.property_id
                    LEFT JOIN color_property_coordinates cpc ON cpc.property_id = props.id
                    LEFT JOIN pan_tilt_position_property ptpp ON ptpp.property_id = props.id
                    LEFT JOIN pan_tilt_positions ptp ON ptp.id = ptpp.position_id
                    WHERE bp.id = {id}
                "
            ),
            |(
                        variant,
                        interpolation_method,
                        x,
                        y,
                        color_x,
                        red,
                        green,
                        blue,
                        position_id,
                        position_x,
                        position_name
                ): (String, String, Option<f32>, Option<f32>, Option<f32>, Option<u8>, Option<u8>, Option<u8>, Option<usize>, Option<f32>, Option<String>)| {
                match variant.as_str() {
                    "color_property_coordinates" => {
                        if let Some(PropertyFileDescriptor::ColorPropertyCoordinates(color_property, _)) = blueprint_filedescriptor.properties.iter_mut().find(|property| {
                            matches!(property, PropertyFileDescriptor::ColorPropertyCoordinates(_, _))
                        }) {
                            color_property
                                .push(
                                    ColorPropertyCoordinate{
                                        x: color_x.unwrap_or_default(),
                                        color: RgbColor{
                                            red: red.unwrap_or_default(),
                                            green: green.unwrap_or_default(),
                                            blue: blue.unwrap_or_default(),
                                        }
                                }
                            );
                        } else {
                            blueprint_filedescriptor.properties.push(
                                PropertyFileDescriptor::ColorPropertyCoordinates(
                                    vec![
                                    ColorPropertyCoordinate{
                                        x: color_x.unwrap_or_default(),
                                        color: RgbColor {
                                            red: red.unwrap_or_default(),
                                            green: green.unwrap_or_default(),
                                            blue: blue.unwrap_or_default(),
                                        }
                                    }],
                                    interpolation_method.into()
                                )
                            );
                        }
                    }
                    "pan_tilt_position_property" => {
                        if let Some(PropertyFileDescriptor::PanTiltPositions(color_property, _)) = blueprint_filedescriptor.properties.iter_mut().find(|property| {
                            matches!(property, PropertyFileDescriptor::PanTiltPositions(_, _))
                        }) {
                            color_property
                                .push(
                                    PanTiltPositionPropertyCoordinate {
                                        position_id: position_id.unwrap_or_default(),
                                        position_name: position_name.unwrap_or_default(),
                                        x: position_x.unwrap_or_default()
                                    }
                                );
                        } else {
                            blueprint_filedescriptor.properties.push(
                                PropertyFileDescriptor::PanTiltPositions(
                                    vec![
                                        PanTiltPositionPropertyCoordinate {
                                                position_id: position_id.unwrap_or_default(),
                                                position_name: position_name.unwrap_or_default(),
                                                x: position_x.unwrap_or_default()
                                        }
                                    ],
                                    interpolation_method.into()
                                )
                            );
                        }
                    }
                    name => {
                        if let Some(PropertyFileDescriptor::UnimplementedChannel(ref mut property)) = blueprint_filedescriptor.properties.iter_mut().find(|property| {
                            if let PropertyFileDescriptor::UnimplementedChannel(uchannel) = property {
                                uchannel.0 == name
                            } else {
                                false
                            }
                        }) {
                            property.1.push(UnimplementedChannelPropertyCoordinate {
                                x: x.unwrap_or_default(),
                                y: y.unwrap_or_default()
                            });
                        }
                         else {
                            blueprint_filedescriptor.properties.push(
                                PropertyFileDescriptor::UnimplementedChannel(
                                    (
                                        name.to_owned(),
                                        vec![UnimplementedChannelPropertyCoordinate {
                                            x: x.unwrap_or_default(),
                                            y: y.unwrap_or_default()
                                        }],
                                        interpolation_method.into()
                                    )
                                )
                            );
                        }
                    }
                }
            },
        )
        .unwrap_or_else(|err| {
            logging::log(
                format!("{err:?}\n        (Saturating properties in blueprints)"),
                logging::LogLevel::DbError,
                true,
            );
            vec![]
        });
}

fn saturate_blueprint_fixture_delays(
    db_connection: &mut PooledConn,
    id: usize,
    blueprint_filedescriptor: &mut BlueprintFileDescriptor,
) {
    db_connection
        .query_map(
            format!(
                "
                        SELECT
                            delay_by_eights,
                            fixture_id
                        FROM registered_fixture_delays rfd
                        JOIN blueprints b ON blueprint_id = b.id
                        WHERE b.id = {id}
                    "
            ),
            |(delay_eights, fixture_id)| {
                blueprint_filedescriptor.registered_fixture_delays.push(
                    RegisteredBlueprintFixtureDelay {
                        fixture_id,
                        delay_eights,
                    },
                );
            },
        )
        .unwrap_or_else(|err| {
            logging::log(
                format!(
                    "{err:?}\n        (Saturating blueprint fixture_delays)"
                ),
                logging::LogLevel::DbError,
                true,
            );
            vec![]
        });
}

impl DbHandler {
    #[must_use]
    pub fn blueprint(
        db_connection: &mut PooledConn,
        id: usize,
    ) -> BlueprintFileDescriptor {
        let mut result: BlueprintFileDescriptor = BlueprintFileDescriptor {
            properties: vec![],
            registered_fixture_delays: vec![],
            name: String::new(),
            id: 0,
            requires_user_action_reason: None,
        };

        saturate_blueprint_metadata(db_connection, id, &mut result);

        saturate_blueprint_properties(db_connection, id, &mut result);

        result
            .properties
            .iter_mut()
            .for_each(PropertyFileDescriptor::sort);

        saturate_blueprint_fixture_delays(db_connection, id, &mut result);

        result
    }
    #[must_use]
    pub fn blueprints_for_active_show(
        db_connection: &mut PooledConn,
    ) -> Vec<BlueprintFileDescriptor> {
        let mut blueprint_ids = vec![];
        let mut result = vec![];

        db_connection.query_map(
                "
                    SELECT
                        b.id
                    FROM blueprints b
                    JOIN shows ON show_id = shows.id
                    WHERE shows.active
                "
            ,
            |id: usize| {
                blueprint_ids.push(id);
            },
            ).unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Collecting blueprints for active show)"),
                    logging::LogLevel::DbError,
                    true,
                );
                vec![]
            });
        for id in &blueprint_ids {
            result.push(Self::blueprint(db_connection, *id));
        }
        result
    }
    pub fn create_default_blueprint_in_active_show(&mut self) -> usize {
        self.db_connection().query_drop(format!(
            "
                INSERT INTO blueprints
                    (
                        name,
                        requires_user_action_reason,
                        show_id
                    )
                VALUES ('{}', '{:?}', (SELECT id FROM shows WHERE active))
            ",
            blueprint::DEFAULT_NAME,
            blueprint::DEFAULT_REQUIRES_USER_ACTION_REASON
            )).unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Creating default blueprint in active show)"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        let mut blueprint_id = 0;
        self.db_connection()
                    .query_map(
                        "
                    SELECT MAX(id) FROM blueprints

                    ",
                        |id| blueprint_id = id,
                    )
                    .unwrap_or_else(|err| {
                        logging::log(
                            format!(
                        "{err:?}\n        (Selecting the just-created blueprint)"
                    ),
                            logging::LogLevel::DbError,
                            true,
                        );
                        vec![]
                    });
        self.active_show.blueprints =
            Self::blueprints_for_active_show(&mut self.db_connection());
        blueprint_id
    }

    pub fn update_blueprint(&mut self, blueprint: &BlueprintFileDescriptor) {
        self.db_connection()
            .query_drop(format!(
                "
                    UPDATE blueprints b
                    SET b.name = '{}', b.requires_user_action_reason = '{}'
                    WHERE id = {}
                ",
                blueprint.name,
                blueprint
                    .requires_user_action_reason
                    .as_ref()
                    .unwrap_or(&String::new()),
                blueprint.id
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!(
                        "{err:?}\n        (Updating blueprint {})",
                        blueprint.id
                    ),
                    logging::LogLevel::DbError,
                    true,
                );
            });

        persist_blueprint_properties(&mut self.db_connection(), blueprint);

        self.db_connection()
                .query_drop(format!(
                        "
                            DELETE FROM registered_fixture_delays
                            WHERE blueprint_id = {}
                        ",
                        blueprint.id
                ))
                .unwrap_or_else(|err| {
                    logging::log(
                        format!(
                            "{err:?}\n        (Removing outdated blueprint fixture_delays)",
                        ),
                        logging::LogLevel::DbError,
                        true,
                    );
                });
        for fx_delay in &blueprint.registered_fixture_delays {
            self.db_connection()
                    .query_drop(format!(
                            "
                                INSERT INTO registered_fixture_delays
                                (delay_by_eights, blueprint_id, fixture_id)
                                VALUES ({}, {}, {})
                            ",
                            fx_delay.delay_eights, blueprint.id, fx_delay.fixture_id
                    ))
                    .unwrap_or_else(|err| {
                        logging::log(
                            format!(
                                "{err:?}\n        (Inserting updated fixture_delays)",
                            ),
                            logging::LogLevel::DbError,
                            true,
                        );
                    });
        }
        self.active_show.blueprints =
            Self::blueprints_for_active_show(&mut self.db_connection());
    }
    pub fn delete_blueprint(&mut self, id: usize) {
        self.db_connection()
            .query_drop(format!(
                "
                    DELETE FROM blueprints WHERE id = {id}
                "
            ))
            .unwrap_or_else(|err| {
                logging::log(
                    format!("{err:?}\n        (Deleting blueprint {id})"),
                    logging::LogLevel::DbError,
                    true,
                );
            });
        self.active_show.blueprints =
            Self::blueprints_for_active_show(&mut self.db_connection());
        self.publish_changes();
    }
}
fn remove_outdated_blueprint_properties(
    db_connection: &mut PooledConn,
    blueprint: &BlueprintFileDescriptor,
) {
    db_connection
        .query_drop(format!(
            "
                    DELETE p
                    FROM properties p
                    JOIN blueprints b ON b.id = p.blueprint_id
                    WHERE b.id = {}
                ",
            blueprint.id
        ))
        .unwrap_or_else(|err| {
            logging::log(
                format!(
                    "{err:?}\n        (Removing outdated blueprint properties)",
                ),
                logging::LogLevel::DbError,
                true,
            );
        });
}
#[allow(clippy::too_many_lines)]
fn persist_blueprint_properties(
    db_connection: &mut PooledConn,
    blueprint: &BlueprintFileDescriptor,
) {
    remove_outdated_blueprint_properties(db_connection, blueprint);

    for property in &blueprint.properties {
        match property {
            PropertyFileDescriptor::UnimplementedChannel(channel) => {
                db_connection
                        .query_drop(format!(
                            "
                        INSERT INTO properties
                        (variant, interpolation_method, blueprint_id)
                        VALUES ('{}', '{}', {})
                        ",
                            channel.0, channel.2, blueprint.id
                        ))
                        .unwrap_or_else(|err| {
                            logging::log(
                                format!(
                            "{err:?}\n        (Inserting updated custom property)",
                        ),
                                logging::LogLevel::DbError,
                                true,
                            );
                        });
                for coordinate in &channel.1 {
                    db_connection
                            .query_drop(format!(
                                "
                                    INSERT INTO property_coordinates
                                    (x, y, property_id)
                                    VALUES ({}, {}, (SELECT MAX(id) FROM properties))
                                ",
                                coordinate.x,
                                coordinate.y
                            ))
                            .unwrap_or_else(|err| {
                                logging::log(
                                    format!("{err:?}\n        (Inserting updated property_coordinates)",),
                                    logging::LogLevel::DbError,
                                    true,
                                );
                            });
                }
            }
            PropertyFileDescriptor::PanTiltPositions(
                channel,
                interpolation_method,
            ) => {
                db_connection
                        .query_drop(format!(
                            "
                            INSERT INTO properties
                            (variant, interpolation_method, blueprint_id)
                            VALUES ('pan_tilt_position_property', '{interpolation_method}', {})
                            ",
                            blueprint.id
                        ))
                        .unwrap_or_else(|err| {
                            logging::log(
                                format!(
                            "{err:?}\n        (Inserting updated property for pan_tilt_position)",
                        ),
                                logging::LogLevel::DbError,
                                true,
                            );
                        });
                for coordinate in channel {
                    db_connection
                            .query_drop(format!(
                                "
                                    INSERT INTO pan_tilt_position_property
                                    (x, position_id, property_id)
                                    VALUES ({}, {}, (SELECT MAX(id) FROM properties))
                                ",
                                coordinate.x,
                                coordinate.position_id

                            ))
                            .unwrap_or_else(|err| {
                                logging::log(
                                    format!("{err:?}\n        (Inserting updated pan_tilt_position_property)",),
                                    logging::LogLevel::DbError,
                                    true,
                                );
                            });
                }
            }
            PropertyFileDescriptor::ColorPropertyCoordinates(
                channel,
                interpolation_method,
            ) => {
                db_connection
                        .query_drop(format!(
                            "
                            INSERT INTO properties
                            (variant, interpolation_method, blueprint_id)
                            VALUES ('color_property_coordinates', '{interpolation_method}', {})
                            ",
                            blueprint.id
                        ))
                        .unwrap_or_else(|err| {
                            logging::log(
                                format!(
                            "{err:?}\n        (Inserting updated color_property_coordinates)",
                        ),
                                logging::LogLevel::DbError,
                                true,
                            );
                        });
                for coordinate in channel {
                    db_connection
                            .query_drop(format!(
                                "
                                    INSERT INTO color_property_coordinates
                                    (x, red, green, blue, property_id)
                                    VALUES ({}, {}, {}, {}, (SELECT MAX(id) FROM properties))
                                ",
                                coordinate.x,
                                coordinate.color.red,
                                coordinate.color.green,
                                coordinate.color.blue
                            ))
                            .unwrap_or_else(|err| {
                                logging::log(
                                    format!("{err:?}\n        (Inserting updated color_property_coordinates)",),
                                    logging::LogLevel::DbError,
                                    true,
                                );
                            });
                }
            }
            PropertyFileDescriptor::CallSnippet(snippetcall) => {
                db_connection
                    .query_drop(format!(
                        "
                            INSERT INTO properties
                            (variant, snippet_id)
                            VALUES ('SnippetCall', {})
                            ",
                        snippetcall.snippet_id
                    ))
                    .unwrap_or_else(|err| {
                        logging::log(
                            format!(
                            "{err:?}\n        (Inserting updated snippetcall)",
                        ),
                            logging::LogLevel::DbError,
                            true,
                        );
                    });
            }
        }
    }
}
