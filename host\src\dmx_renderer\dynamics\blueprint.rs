use crate::database_handler::Db<PERSON><PERSON><PERSON>;
use crate::dmx_renderer::channel::color_channels::RgbColor;
use crate::dmx_renderer::dynamics::property::{
    ColorPropertyCoordinate, PanTiltPositionPropertyCoordinate,
    PropertyFileDescriptor, UnimplementedChannelPropertyCoordinate,
};
use serde::{Deserialize, Serialize};
use ts_rs::TS;

pub const DEFAULT_NAME: &str = "new";
pub const DEFAULT_REQUIRES_USER_ACTION_REASON: Option<&str> = Some("empty");

#[derive(Clone, Serialize, Deserialize, Debug, TS)]
#[ts(export)]
pub struct BlueprintFileDescriptor {
    pub properties: Vec<PropertyFileDescriptor>,
    pub registered_fixture_delays: Vec<RegisteredBlueprintFixtureDelay>,
    pub name: String,
    pub id: usize,
    pub requires_user_action_reason: Option<String>,
}

impl BlueprintFileDescriptor {
    #[must_use]
    pub fn max_len_in_eights(db_handler: &mut DbHandler, id: usize) -> u8 {
        #[allow(
            clippy::cast_possible_truncation,
            clippy::cast_sign_loss,
            clippy::as_conversions
        )]
        let mut bp_duration_in_eights: Vec<u8> =
            DbHandler::blueprint(&mut db_handler.db_connection(), id)
                .properties
                .iter()
                .map(|property| match property {
                    PropertyFileDescriptor::UnimplementedChannel(channel) => {
                        (channel
                            .1
                            .last()
                            // TODO: use `unwrap_or_default`
                            .unwrap_or(
                                &UnimplementedChannelPropertyCoordinate {
                                    x: 0.,
                                    y: 0.,
                                },
                            )
                            .x
                            / 20.)
                            .clamp(0., 255.) as u8
                    }
                    PropertyFileDescriptor::PanTiltPositions(
                        pantiltpositions,
                        _,
                    ) => {
                        (pantiltpositions
                            .last()
                            // TODO: use `unwrap_or_default`
                            .unwrap_or(&PanTiltPositionPropertyCoordinate {
                                position_id: 0,
                                position_name: String::new(),
                                x: 0.,
                            })
                            .x
                            / 20.)
                            .clamp(0., 255.) as u8
                    }
                    PropertyFileDescriptor::ColorPropertyCoordinates(
                        color,
                        _,
                    ) => {
                        (color
                            .last()
                            // TODO: use `unwrap_or_default`
                            .unwrap_or(&ColorPropertyCoordinate {
                                x: 0.,
                                color: RgbColor {
                                    red: 0,
                                    green: 0,
                                    blue: 0,
                                },
                            })
                            .x
                            / 20.)
                            .clamp(0., 255.) as u8
                    }
                    PropertyFileDescriptor::CallSnippet(snippetcall) => {
                        (snippetcall.x / 20.).clamp(0., 255.) as u8
                    }
                })
                .collect();
        bp_duration_in_eights.sort_unstable();
        let Some(bp_duration_in_eights) = bp_duration_in_eights.last() else {
            return 0;
        };
        *bp_duration_in_eights
    }
}

#[derive(Clone, Serialize, Deserialize, Debug, PartialEq, Eq, TS)]
#[ts(export)]
pub struct RegisteredBlueprintFixtureDelay {
    pub fixture_id: usize,
    pub delay_eights: usize,
}
