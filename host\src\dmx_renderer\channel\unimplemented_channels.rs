use super::{Dmx<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, DmxChannelValue};
use crate::dmx_renderer::dynamics::property::PropertyFileDescriptor;
use crate::dmx_renderer::{clamp_index_to_spline_length, dynamics::IsDynamic};
use crate::input_parser::structs::InstructionValue;
use map_to_range::MapRange;
use serde::{Deserialize, Serialize};
use splines::Spline;
use ts_rs::TS;

#[derive(Clone, Serialize, Deserialize, Debug, TS)]
#[ts(export)]
pub struct Keypoint {
    pub id: Option<usize>,
    pub name: String,
    pub value: u8,
}

#[derive(Clone, Serialize, Deserialize, Debug, TS)]
#[ts(export)]
pub struct UnimplementedChannel {
    pub id: Option<usize>,
    pub channel: usize,
    pub name: String,
    pub dimmable: bool,
    pub inverted: bool,
    pub default: u8,
    pub keypoints: Vec<Keypoint>,
    #[serde(skip)]
    pub value: u8,
    #[serde(skip)]
    pub spline: Option<Spline<f32, f32>>,
    #[serde(skip)]
    pub oneshot: bool,
    #[serde(skip)]
    pub blueprint_id: Option<usize>,
}

impl UnimplementedChannel {
    #[must_use]
    pub fn name(&self) -> String {
        self.name.clone()
    }
    #[must_use]
    pub const fn value(&self) -> u8 {
        self.value
    }
    pub fn set_to_default(&mut self) {
        self.clear_all_splines();
        self.value = self.default;
    }
    pub fn set_value(&mut self, value: u8) {
        self.clear_all_splines();
        self.value = value;
    }
    #[must_use]
    pub fn keypoints(&self) -> Vec<&Keypoint> {
        self.keypoints.iter().collect()
    }
    #[must_use]
    pub fn from_unimplemented_channel_file_descriptors(
        values: Vec<&mut Self>,
    ) -> Vec<Self> {
        let mut result = vec![];
        for value in values {
            value.value = value.default;
            result.push(value.clone());
        }
        result
    }
}

impl IsDynamic for UnimplementedChannel {
    fn extract_internal_spline_from(
        &mut self,
        properties: &[PropertyFileDescriptor],
        oneshot: bool,
        blueprint_id: Option<usize>,
    ) {
        self.oneshot = oneshot;
        for property in properties {
            if let PropertyFileDescriptor::UnimplementedChannel((name, _, _)) =
                property
            {
                if *name == self.name() {
                    self.spline = Some(property.into());
                    self.blueprint_id = blueprint_id;
                }
            }
        }
    }
    fn clear_all_splines(&mut self) {
        self.spline = None;
        self.blueprint_id = None;
    }
    fn clear_splines_by_blueprint_id(&mut self, blueprint_id: usize) {
        if self.blueprint_id == Some(blueprint_id) {
            self.clear_all_splines();
        }
    }
    fn apply_spline_index(&mut self, index: f32) {
        if let Some(spline) = self.spline.as_mut() {
            if self.oneshot {
                if let Some(last) = spline.keys().last() {
                    if index >= last.t {
                        let value = last.value;
                        #[allow(
                            clippy::cast_possible_truncation,
                            clippy::cast_sign_loss,
                            clippy::as_conversions
                        )]
                        self.set_value(value.clamp(0., 255.) as u8);
                        self.clear_all_splines();
                        return;
                    }
                }
            }
            #[allow(
                clippy::cast_possible_truncation,
                clippy::cast_sign_loss,
                clippy::as_conversions
            )]
            if let Some(sample) = spline
                .clamped_sample(clamp_index_to_spline_length(index, spline))
            {
                self.value = sample.clamp(0., 255.) as u8;
            }
        }
    }
}

impl DmxChannelEmitter for UnimplementedChannel {
    fn compute_dmx_channels(
        &mut self,
        master_dimmer: u8,
    ) -> Vec<DmxChannelValue> {
        let mapped_value = f32::from(self.value);
        let mut value = self.value;
        if self.dimmable {
            #[allow(
                clippy::cast_possible_truncation,
                clippy::cast_sign_loss,
                clippy::as_conversions
            )]
            if let Some(mapped_master_dimmer) =
                f32::from(master_dimmer).map_range((0., 255.), (0., 1.))
            {
                value = (mapped_value * mapped_master_dimmer)
                    .clamp(0., u8::MAX.into()) as u8;
            }
        }

        if self.inverted {
            value = 255_u8.saturating_sub(value);
        }

        vec![(self.channel, value).into()]
    }
}

#[derive(Clone, Serialize, Deserialize, Debug, PartialEq, Eq, TS)]
#[ts(export)]
pub struct UnimplementedChannelSetter {
    pub value: InstructionValue,
    pub name: String,
}

impl PartialEq for UnimplementedChannel {
    fn eq(&self, other: &Self) -> bool {
        self.channel == other.channel
            && self.name == other.name
            && self.dimmable == other.dimmable
            && self.inverted == other.inverted
            && self.default == other.default
            && self.value == other.value
            && self.oneshot == other.oneshot
            && self.blueprint_id == other.blueprint_id
    }
}
