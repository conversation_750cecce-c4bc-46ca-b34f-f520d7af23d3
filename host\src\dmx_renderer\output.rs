use crate::logging;
use core::net::SocketAddr;
use std::net::{ToSocketAddrs, UdpSocket};

const PORT: u16 = 6454;

pub struct ArtNet {
    socket: UdpSocket,
    broadcast_addr: SocketAddr,
}

impl ArtNet {
    #[must_use]
    pub fn new() -> Option<Self> {
        // if cfg!(debug_assertions) {
        //     logging::log(
        //         format!("ArtNet is only available in RELEASE mode"),
        //         logging::LogLevel::Info,
        //         false,
        //     );
        //     None
        // } else {
        let Ok(socket) = UdpSocket::bind(("0.0.0.0", PORT)) else {
            logging::log(
                format!("Unable to bind to UdpSocket on port {PORT}"),
                logging::LogLevel::Warning,
                true,
            );
            return None;
        };
        let Ok(mut socket_addrs) = ("255.255.255.255", PORT).to_socket_addrs()
        else {
            logging::log(
                format!("Could not create socket_addrs on port {PORT}"),
                logging::LogLevel::Warning,
                true,
            );
            return None;
        };
        let Some(broadcast_addr) = socket_addrs.next() else {
            logging::log(
                format!("artnet socket addrs seem to be empty"),
                logging::LogLevel::Warning,
                true,
            );
            return None;
        };
        if socket.set_broadcast(true).is_err() {
            logging::log(
                format!("Could not set artnet socket to broadcast"),
                logging::LogLevel::Warning,
                true,
            );
            return None;
        }
        logging::log(
            format!("Initialized ArtNet"),
            logging::LogLevel::Info,
            false,
        );

        Some(Self {
            socket,
            broadcast_addr,
        })
        // }
    }
    pub fn send(&self, universes: &Vec<(u16, Vec<u8>)>) {
        // if !cfg!(debug_assertions) {
        for universe in universes {
            let package = ARTNET_HEADER;
            let mut package = package.to_vec();
            package.push(universe.0.to_le_bytes()[0]);
            package.push(universe.0.to_le_bytes()[1]);
            package.push(ARTNET_PACKAGE_LENGTH[0]);
            package.push(ARTNET_PACKAGE_LENGTH[1]);
            for channel in &universe.1 {
                package.push(*channel);
            }
            while package.len() < ARTNET_BUFFER_LEN {
                package.push(0);
            }

            if self.socket.send_to(&package, self.broadcast_addr).is_err() {
                logging::log(
                    format!("Unable to send ArtNet"),
                    logging::LogLevel::Warning,
                    true,
                );
                return;
                // };
            }
        }
    }
}

const ARTNET_HEADER: [u8; 14] =
    [65, 114, 116, 45, 78, 101, 116, 0, 0, 80, 0, 14, 0, 0];
const ARTNET_PACKAGE_LENGTH: [u8; 2] = [2, 0];
const ARTNET_BUFFER_LEN: usize = 530;
