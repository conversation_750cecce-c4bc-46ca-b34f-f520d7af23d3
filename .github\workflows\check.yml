name: Check

on:
  push:

# Make sure C<PERSON> fails on all warnings, including Clippy lints
env:
  RUSTFLAGS: "-Dwarnings"

jobs:
  check_host:
    runs-on: rpi-4b-1.1
    steps:
      - uses: actions/checkout@v4

      - uses: dorny/paths-filter@v3
        id: changes
        with:
          filters: |
            src:
              - 'host/**'

      - name: clippy
        if: steps.changes.outputs.src == 'true'
        run: |
          export CARGO_TARGET_DIR=/home/<USER>/repos/ruhige-waldgeraeusche/host/target
          cargo clippy --all-targets --all-features --manifest-path host/Cargo.toml --verbose


  check_frontend:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: dorny/paths-filter@v3
        id: changes
        with:
          filters: |
            src:
              - 'interface/src/**'

      - name: install interface deps
        if: steps.changes.outputs.src == 'true'
        working-directory: interface
        run: npm i

      - name: typecheck
        if: steps.changes.outputs.src == 'true'
        working-directory: interface
        run: npm run check

  check_frontend_tauri_layer:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: dorny/paths-filter@v3
        id: changes
        with:
          filters: |
            src:
              - 'interface/src-tauri/**'

      - name: install tauri deps
        if: steps.changes.outputs.src == 'true'
        run: |
          sudo apt-get update
          sudo apt-get install -y libwebkit2gtk-4.1-dev libappindicator3-dev librsvg2-dev patchelf

      - name: clippy
        if: steps.changes.outputs.src == 'true'
        run: cargo clippy --all-targets --all-features --manifest-path interface/src-tauri/Cargo.toml --verbose

  check_modules:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: dorny/paths-filter@v3
        id: changes
        with:
          filters: |
            src:
              - 'modules/**'

      - name: log todo
        if: steps.changes.outputs.src == 'true'
        run: echo "TODO; modules-clippy-check is currently not executed. Fix it!"

  e2e_tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: dorny/paths-filter@v3
        id: changes
        with:
          filters: |
            src:
              - 'cypress/**'
              - 'host/**'
              - 'interface/**'
              - '.github/workflows/**'

      - name: setup mysql
        if: steps.changes.outputs.src == 'true'
        working-directory: host/scripts
        run: sudo ./setup.sh

      - name: setup rust toolchain
        if: steps.changes.outputs.src == 'true'
        uses: dtolnay/rust-toolchain@stable

      - name: install system dependencies
        if: steps.changes.outputs.src == 'true'
        run: |
          sudo apt-get update
          sudo apt-get install -y libudev-dev pkg-config libssl-dev

      - name: setup node.js
        if: steps.changes.outputs.src == 'true'
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: interface/package-lock.json

      - name: install frontend dependencies
        if: steps.changes.outputs.src == 'true'
        working-directory: interface
        run: npm ci

      - name: install cypress
        if: steps.changes.outputs.src == 'true'
        run: |
          npm ci
          npx cypress install

      - name: build backend
        if: steps.changes.outputs.src == 'true'
        working-directory: host
        run: cargo build

      - name: start backend
        if: steps.changes.outputs.src == 'true'
        working-directory: host
        run: |
          echo "Starting backend..."
          nohup ./target/debug/host > backend.log 2>&1 &
          echo $! > backend.pid
          echo "Backend PID: $(cat backend.pid)"


      - name: start frontend
        if: steps.changes.outputs.src == 'true'
        working-directory: interface
        run: |
          echo "Starting frontend..."
          nohup npm run dev > frontend.log 2>&1 &
          echo $! > frontend.pid
          echo "Frontend PID: $(cat frontend.pid)"

      - name: wait for processes
        if: steps.changes.output.src == 'true'
        run: |
          # Wait for backend to be ready with more detailed logging
          for i in {1..60}; do
            if curl -s -f http://localhost:4000/ping > /dev/null 2>&1; then
              echo "✅ Backend is ready on attempt $i"
              curl -s http://localhost:4000/ping || echo "Ping response check failed"
              break
            fi
            echo "⏳ Waiting for backend... ($i/60)"
            if [ $i -eq 60 ]; then
              echo "❌ Backend failed to start after 60 attempts"
              echo "Backend log:"
              cat backend.log || echo "No backend log found"
              exit 1
            fi
            sleep 1
          done

          # Wait for frontend to be ready with more detailed logging
          for i in {1..60}; do
            if curl -s -f http://localhost:1420 > /dev/null 2>&1; then
              echo "✅ Frontend is ready on attempt $i"
              break
            fi
            echo "⏳ Waiting for frontend... ($i/60)"
            if [ $i -eq 60 ]; then
              echo "❌ Frontend failed to start after 60 attempts"
              echo "Frontend log:"
              cat frontend.log || echo "No frontend log found"
              exit 1
            fi
            sleep 1
          done


      - name: run cypress tests
        if: steps.changes.outputs.src == 'true'
        run: npx cypress run --headless --browser electron

        env:
          CYPRESS_baseUrl: http://localhost:1420

      - name: upload backend logs
        if: failure() && steps.changes.outputs.src == 'true'
        uses: actions/upload-artifact@v4
        with:
          name: backend-logs
          path: host/backend.log

      - name: upload frontend logs
        if: failure() && steps.changes.outputs.src == 'true'
        uses: actions/upload-artifact@v4
        with:
          name: frontend-logs
          path: interface/frontend.log

      - name: upload cypress screenshots
        if: failure() && steps.changes.outputs.src == 'true'
        uses: actions/upload-artifact@v4
        with:
          name: cypress-screenshots
          path: cypress/screenshots
          if-no-files-found: ignore

      - name: upload cypress videos
        if: failure() && steps.changes.outputs.src == 'true'
        uses: actions/upload-artifact@v4
        with:
          name: cypress-videos
          path: cypress/videos
          if-no-files-found: ignore
