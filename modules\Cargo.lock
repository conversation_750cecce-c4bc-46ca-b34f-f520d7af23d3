# This file is automatically @generated by Cargo.
# It is not intended for manual editing.
version = 3

[[package]]
name = "arduino-hal"
version = "0.1.0"
source = "git+https://github.com/rahix/avr-hal?rev=1aacefb335517f85d0de858231e11055d9768cdf#1aacefb335517f85d0de858231e11055d9768cdf"
dependencies = [
 "avr-device 0.3.4",
 "avr-hal-generic 0.1.0 (git+https://github.com/rahix/avr-hal?rev=1aacefb335517f85d0de858231e11055d9768cdf)",
 "cfg-if 1.0.0",
 "embedded-hal 0.2.7",
 "ufmt 0.1.2",
 "void",
]

[[package]]
name = "arduino-hal"
version = "0.1.0"
source = "git+https://github.com/rahix/avr-hal?rev=3e362624547462928a219c40f9ea8e3a64f21e5f#3e362624547462928a219c40f9ea8e3a64f21e5f"
dependencies = [
 "atmega-hal",
 "avr-device 0.5.4",
 "avr-hal-generic 0.1.0 (git+https://github.com/rahix/avr-hal?rev=3e362624547462928a219c40f9ea8e3a64f21e5f)",
 "cfg-if 1.0.0",
 "embedded-hal 0.2.7",
 "embedded-hal 1.0.0",
 "ufmt 0.2.0",
]

[[package]]
name = "atmega-hal"
version = "0.1.0"
source = "git+https://github.com/rahix/avr-hal?rev=3e362624547462928a219c40f9ea8e3a64f21e5f#3e362624547462928a219c40f9ea8e3a64f21e5f"
dependencies = [
 "avr-device 0.5.4",
 "avr-hal-generic 0.1.0 (git+https://github.com/rahix/avr-hal?rev=3e362624547462928a219c40f9ea8e3a64f21e5f)",
]

[[package]]
name = "avr-device"
version = "0.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "edb5cf74147f0a7ef4b7b2b5cdd6bc04e749d050cf48ed2a40048db290165db4"
dependencies = [
 "avr-device-macros 0.3.4",
 "bare-metal 0.2.5",
 "cfg-if 0.1.10",
 "rustversion",
 "vcell",
]

[[package]]
name = "avr-device"
version = "0.5.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "546b09da5e933d18b790ccb5aae351371c6c4f8094a7b011dcd7c7e7fb69cc94"
dependencies = [
 "avr-device-macros 0.5.4",
 "bare-metal 1.0.0",
 "cfg-if 1.0.0",
 "critical-section",
 "vcell",
]

[[package]]
name = "avr-device-macros"
version = "0.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "474dda7ee758065593ae9fd53c9f95a38c1f7d44fac02a4d354838a6c40070d8"
dependencies = [
 "proc-macro2",
 "quote",
 "syn",
]

[[package]]
name = "avr-device-macros"
version = "0.5.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6b108541dc1ea060dfa9b824acbded94f658f8daecca549144d05a4d01e65b6f"
dependencies = [
 "proc-macro2",
 "quote",
 "syn",
]

[[package]]
name = "avr-hal-generic"
version = "0.1.0"
source = "git+https://github.com/rahix/avr-hal?rev=1aacefb335517f85d0de858231e11055d9768cdf#1aacefb335517f85d0de858231e11055d9768cdf"
dependencies = [
 "avr-device 0.3.4",
 "cfg-if 0.1.10",
 "embedded-hal 0.2.7",
 "nb 0.1.3",
 "paste",
 "rustversion",
 "ufmt 0.1.2",
 "void",
]

[[package]]
name = "avr-hal-generic"
version = "0.1.0"
source = "git+https://github.com/rahix/avr-hal?rev=3e362624547462928a219c40f9ea8e3a64f21e5f#3e362624547462928a219c40f9ea8e3a64f21e5f"
dependencies = [
 "avr-device 0.5.4",
 "cfg-if 0.1.10",
 "embedded-hal 0.2.7",
 "embedded-hal 1.0.0",
 "embedded-hal-bus",
 "embedded-storage",
 "nb 1.1.0",
 "paste",
 "rustversion",
 "ufmt 0.2.0",
 "unwrap-infallible",
]

[[package]]
name = "bare-metal"
version = "0.2.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5deb64efa5bd81e31fcd1938615a6d98c82eafcbcd787162b6f63b91d6bac5b3"
dependencies = [
 "rustc_version",
]

[[package]]
name = "bare-metal"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f8fe8f5a8a398345e52358e18ff07cc17a568fbca5c6f73873d3a62056309603"

[[package]]
name = "button"
version = "0.1.0"
dependencies = [
 "arduino-hal 0.1.0 (git+https://github.com/rahix/avr-hal?rev=3e362624547462928a219c40f9ea8e3a64f21e5f)",
 "avr-device 0.5.4",
 "embedded-alloc",
 "embedded-hal 0.2.7",
 "my_rust_utils",
 "nb 0.1.3",
 "panic-halt",
 "proc-macro2",
 "rwm_package_definitions",
 "ufmt 0.2.0",
]

[[package]]
name = "bytemuck"
version = "1.16.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b236fc92302c97ed75b38da1f4917b5cdda4984745740f153a5d3059e48d725e"

[[package]]
name = "cfg-if"
version = "0.1.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4785bdd1c96b2a846b2bd7cc02e86b6b3dbf14e7e53446c4f54c92a361040822"

[[package]]
name = "cfg-if"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "baf1de4339761588bc0619e3cbc0120ee582ebb74b53b4efbf79117bd2da40fd"

[[package]]
name = "critical-section"
version = "1.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7059fff8937831a9ae6f0fe4d658ffabf58f2ca96aa9dec1c889f936f705f216"

[[package]]
name = "embedded-alloc"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ddae17915accbac2cfbc64ea0ae6e3b330e6ea124ba108dada63646fd3c6f815"
dependencies = [
 "critical-section",
 "linked_list_allocator",
]

[[package]]
name = "embedded-hal"
version = "0.2.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "35949884794ad573cf46071e41c9b60efb0cb311e3ca01f7af807af1debc66ff"
dependencies = [
 "nb 0.1.3",
 "void",
]

[[package]]
name = "embedded-hal"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "361a90feb7004eca4019fb28352a9465666b24f840f5c3cddf0ff13920590b89"

[[package]]
name = "embedded-hal-bus"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "57b4e6ede84339ebdb418cd986e6320a34b017cdf99b5cc3efceec6450b06886"
dependencies = [
 "critical-section",
 "embedded-hal 1.0.0",
]

[[package]]
name = "embedded-storage"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "723dce4e9f25b6e6c5f35628e144794e5b459216ed7da97b7c4b66cdb3fa82ca"

[[package]]
name = "linked_list_allocator"
version = "0.10.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9afa463f5405ee81cdb9cc2baf37e08ec7e4c8209442b5d72c04cfb2cd6e6286"

[[package]]
name = "millis"
version = "0.1.0"
dependencies = [
 "arduino-hal 0.1.0 (git+https://github.com/rahix/avr-hal?rev=3e362624547462928a219c40f9ea8e3a64f21e5f)",
 "avr-device 0.5.4",
 "proc-macro2",
]

[[package]]
name = "my_rust_utils"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "67d115d887da0bdf8eaa2cb7e88f8a884835b6555bbeb8f320529cc31cbecd0e"

[[package]]
name = "nb"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "801d31da0513b6ec5214e9bf433a77966320625a37860f910be265be6e18d06f"
dependencies = [
 "nb 1.1.0",
]

[[package]]
name = "nb"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8d5439c4ad607c3c23abf66de8c8bf57ba8adcd1f129e699851a6e43935d339d"

[[package]]
name = "panic-halt"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "de96540e0ebde571dc55c73d60ef407c653844e6f9a1e2fdbd40c07b9252d812"

[[package]]
name = "paste"
version = "1.0.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "57c0d7b74b563b49d38dae00a0c37d4d6de9b432382b2892f0574ddcae73fd0a"

[[package]]
name = "proc-macro-hack"
version = "0.5.20+deprecated"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dc375e1527247fe1a97d8b7156678dfe7c1af2fc075c9a4db3690ecd2a148068"

[[package]]
name = "proc-macro2"
version = "1.0.79"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e835ff2298f5721608eb1a980ecaee1aef2c132bf95ecc026a11b7bf3c01c02e"
dependencies = [
 "unicode-ident",
]

[[package]]
name = "quote"
version = "1.0.36"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0fa76aaf39101c457836aec0ce2316dbdc3ab723cdda1c6bd4e6ad4208acaca7"
dependencies = [
 "proc-macro2",
]

[[package]]
name = "rgb"
version = "0.8.37"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "05aaa8004b64fd573fc9d002f4e632d51ad4f026c2b5ba95fcb6c2f32c2c47d8"
dependencies = [
 "bytemuck",
]

[[package]]
name = "rustc_version"
version = "0.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "138e3e0acb6c9fb258b19b67cb8abd63c00679d2851805ea151465464fe9030a"
dependencies = [
 "semver",
]

[[package]]
name = "rustversion"
version = "1.0.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "955d28af4278de8121b7ebeb796b6a45735dc01436d898801014aced2773a3d6"

[[package]]
name = "rwm_communication"
version = "0.1.0"
dependencies = [
 "arduino-hal 0.1.0 (git+https://github.com/rahix/avr-hal?rev=3e362624547462928a219c40f9ea8e3a64f21e5f)",
 "avr-device 0.5.4",
 "embedded-alloc",
 "embedded-hal 0.2.7",
 "my_rust_utils",
 "nb 0.1.3",
 "panic-halt",
 "proc-macro2",
 "rwm_package_definitions",
 "ufmt 0.2.0",
]

[[package]]
name = "rwm_package_definitions"
version = "0.1.0"

[[package]]
name = "semver"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1d7eb9ef2c18661902cc47e535f9bc51b78acd254da71d375c2f6720d9a40403"
dependencies = [
 "semver-parser",
]

[[package]]
name = "semver-parser"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "388a1df253eca08550bef6c72392cfe7c30914bf41df5269b68cbd6ff8f570a3"

[[package]]
name = "smart-leds"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "38dd45fa275f70b4110eac5f5182611ad384f88bb22b68b9a9c3cafd7015290b"
dependencies = [
 "smart-leds-trait",
]

[[package]]
name = "smart-leds-trait"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ebf6d833fa93f16a1c1874e62c2aebe8567e5bdd436d59bf543ed258b6f7a8e3"
dependencies = [
 "rgb",
]

[[package]]
name = "syn"
version = "1.0.109"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "72b64191b275b66ffe2469e8af2c1cfe3bafa67b529ead792a6d0160888b4237"
dependencies = [
 "proc-macro2",
 "quote",
 "unicode-ident",
]

[[package]]
name = "tomahawk_1"
version = "0.1.0"
dependencies = [
 "arduino-hal 0.1.0 (git+https://github.com/rahix/avr-hal?rev=3e362624547462928a219c40f9ea8e3a64f21e5f)",
 "avr-device 0.5.4",
 "button",
 "embedded-alloc",
 "embedded-hal 0.2.7",
 "millis",
 "my_rust_utils",
 "nb 0.1.3",
 "panic-halt",
 "proc-macro2",
 "rwm_communication",
 "rwm_package_definitions",
 "ufmt 0.2.0",
]

[[package]]
name = "tomahawk_2"
version = "0.1.0"
dependencies = [
 "arduino-hal 0.1.0 (git+https://github.com/rahix/avr-hal?rev=3e362624547462928a219c40f9ea8e3a64f21e5f)",
 "avr-device 0.5.4",
 "button",
 "embedded-alloc",
 "embedded-hal 0.2.7",
 "millis",
 "my_rust_utils",
 "nb 0.1.3",
 "panic-halt",
 "proc-macro2",
 "rwm_communication",
 "rwm_package_definitions",
 "smart-leds",
 "ufmt 0.2.0",
 "ws2812-avr",
]

[[package]]
name = "ufmt"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "31d3c0c63312dfc9d8e5c71114d617018a19f6058674003c0da29ee8d8036cdd"
dependencies = [
 "proc-macro-hack",
 "ufmt-macros 0.2.0",
 "ufmt-write",
]

[[package]]
name = "ufmt"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1a64846ec02b57e9108d6469d98d1648782ad6bb150a95a9baac26900bbeab9d"
dependencies = [
 "ufmt-macros 0.3.0",
 "ufmt-write",
]

[[package]]
name = "ufmt-macros"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e4ab6c92f30c996394a8bd525aef9f03ce01d0d7ac82d81902968057e37dd7d9"
dependencies = [
 "proc-macro-hack",
 "proc-macro2",
 "quote",
 "syn",
]

[[package]]
name = "ufmt-macros"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d337d3be617449165cb4633c8dece429afd83f84051024079f97ad32a9663716"
dependencies = [
 "proc-macro2",
 "quote",
 "syn",
]

[[package]]
name = "ufmt-write"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e87a2ed6b42ec5e28cc3b94c09982969e9227600b2e3dcbc1db927a84c06bd69"

[[package]]
name = "unicode-ident"
version = "1.0.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3354b9ac3fae1ff6755cb6db53683adb661634f67557942dea4facebec0fee4b"

[[package]]
name = "unwrap-infallible"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "151ac09978d3c2862c4e39b557f4eceee2cc72150bc4cb4f16abf061b6e381fb"

[[package]]
name = "vcell"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "77439c1b53d2303b20d9459b1ade71a83c716e3f9c34f3228c00e6f185d6c002"

[[package]]
name = "void"
version = "1.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6a02e4885ed3bc0f2de90ea6dd45ebcbb66dacffe03547fadbb0eeae2770887d"

[[package]]
name = "ws2812-avr"
version = "0.1.0"
source = "git+https://github.com/devcexx/ws2812-avr?rev=f6fa2189690b8f21ad9b4ff694a7d815f5507812#f6fa2189690b8f21ad9b4ff694a7d815f5507812"
dependencies = [
 "arduino-hal 0.1.0 (git+https://github.com/rahix/avr-hal?rev=1aacefb335517f85d0de858231e11055d9768cdf)",
 "avr-hal-generic 0.1.0 (git+https://github.com/rahix/avr-hal?rev=1aacefb335517f85d0de858231e11055d9768cdf)",
]
