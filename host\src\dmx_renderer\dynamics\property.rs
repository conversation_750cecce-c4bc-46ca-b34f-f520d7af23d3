use serde::{Deserialize, Serialize};
use splines::{Key, Spline};
use ts_rs::TS;

use crate::{
    dmx_renderer::{channel::color_channels::RgbColor, InterpolationMethod},
    logging,
};

#[derive(Clone, Serialize, Deserialize, Debug, PartialEq, TS)]
#[ts(export)]
pub struct UnimplementedChannelPropertyCoordinate {
    pub x: f32,
    pub y: f32,
}
#[derive(Clone, Serialize, Deserialize, Debug, PartialEq, TS)]
#[ts(export)]
pub struct PanTiltPositionPropertyCoordinate {
    pub position_id: usize,
    pub position_name: String,
    pub x: f32,
}
#[derive(Clone, Serialize, Deserialize, Debug, PartialEq, TS)]
#[ts(export)]
pub struct ColorPropertyCoordinate {
    pub x: f32,
    pub color: RgbColor,
}
#[derive(Clone, Serialize, Deserialize, Debug, PartialEq, TS)]
#[ts(export)]
pub struct SnippetCall {
    pub x: f32,
    pub snippet_id: usize,
}

// TODO: Wrap this in a struct
#[derive(Clone, Serialize, Deserialize, Debug, PartialEq, TS)]
#[ts(export)]
pub enum PropertyFileDescriptor {
    UnimplementedChannel(
        (
            String,
            Vec<UnimplementedChannelPropertyCoordinate>,
            InterpolationMethod,
        ),
    ),
    PanTiltPositions(
        Vec<PanTiltPositionPropertyCoordinate>,
        InterpolationMethod,
    ),
    ColorPropertyCoordinates(Vec<ColorPropertyCoordinate>, InterpolationMethod),
    CallSnippet(SnippetCall),
}
impl PropertyFileDescriptor {
    pub fn append_color_coordinates(
        &mut self,
        other: &mut Vec<ColorPropertyCoordinate>,
    ) {
        match self {
            Self::ColorPropertyCoordinates(coordinates, _) => {
                if let Some(self_last) = coordinates.last() {
                    other.insert(
                        0,
                        ColorPropertyCoordinate {
                            x: other
                                .first()
                                .map(|channel| channel.x)
                                .unwrap_or_default()
                                - 1.,
                            color: self_last.color,
                        },
                    );
                }
                coordinates.append(other);
                coordinates.sort_by(|a, b| a.x.total_cmp(&b.x));
            }
            Self::PanTiltPositions(_, _)
            | Self::UnimplementedChannel(_)
            | Self::CallSnippet(_) => (),
        }
    }
    pub fn append_unimplemented_channel_coordinates(
        &mut self,
        other: &mut Vec<UnimplementedChannelPropertyCoordinate>,
    ) {
        match self {
            Self::UnimplementedChannel(channel) => {
                if let Some(self_last) = channel.1.last() {
                    other.insert(
                        0,
                        UnimplementedChannelPropertyCoordinate {
                            x: other
                                .first()
                                .map(|channel| channel.x)
                                .unwrap_or_default()
                                - 1.,
                            y: self_last.y,
                        },
                    );
                }
                channel.1.append(other);
                channel.1.sort_by(|a, b| a.x.total_cmp(&b.x));
            }
            Self::ColorPropertyCoordinates(_, _)
            | Self::PanTiltPositions(_, _)
            | Self::CallSnippet(_) => (),
        }
    }
    #[must_use]
    pub fn into_color_splines(
        &self,
    ) -> Option<(Spline<f32, f32>, Spline<f32, f32>)> {
        if let Self::ColorPropertyCoordinates(
            coordinates,
            interpolation_method,
        ) = self
        {
            Some((
                Spline::from_vec(
                    coordinates
                        .iter()
                        .map(|coordinate| {
                            Key::new(
                                coordinate.x,
                                coordinate.color.as_hsv().0,
                                (*interpolation_method).into(),
                            )
                        })
                        .collect(),
                ),
                Spline::from_vec(
                    coordinates
                        .iter()
                        .map(|coordinate| {
                            Key::new(
                                coordinate.x,
                                coordinate.color.as_hsv().1,
                                (*interpolation_method).into(),
                            )
                        })
                        .collect(),
                ),
            ))
        } else {
            None
        }
    }
}
impl From<&PropertyFileDescriptor> for Spline<f32, f32> {
    fn from(value: &PropertyFileDescriptor) -> Self {
        let mut keys: Vec<Key<f32, f32>> = vec![];
        match value {
            PropertyFileDescriptor::UnimplementedChannel((
                _name,
                all_coordinates,
                interpolation_method,
            )) => {
                for coordinates in all_coordinates {
                    keys.push(Key::new(
                        coordinates.x,
                        coordinates.y,
                        (*interpolation_method).into(),
                    ));
                }
            }
            PropertyFileDescriptor::PanTiltPositions(_, _) => {
                logging::log(format!("Unable to create splines from PanTiltPositions. Use `fixture.rs`."), logging::LogLevel::Warning, true);
            }
            PropertyFileDescriptor::ColorPropertyCoordinates(_, _) => {
                logging::log(format!("Unable to create colorsplines ColorPropertyCoordinates. Use `into_color_spline`."), logging::LogLevel::Warning, true);
            }
            PropertyFileDescriptor::CallSnippet(snippetcall) => {
                logging::log(
                    format!(
                        "Unable to create spline from call to snippet {}",
                        snippetcall.snippet_id
                    ),
                    logging::LogLevel::Info,
                    false,
                );
            }
        }
        Self::from_vec(keys)
    }
}

impl PropertyFileDescriptor {
    pub fn sort(&mut self) {
        match self {
            Self::UnimplementedChannel(channel) => {
                channel.1.sort_by(|a, b| a.x.total_cmp(&b.x));
            }
            Self::PanTiltPositions(channel, _) => {
                channel.sort_by(|a, b| a.x.total_cmp(&b.x));
            }
            Self::ColorPropertyCoordinates(channel, _) => {
                channel.sort_by(|a, b| a.x.total_cmp(&b.x));
            }
            Self::CallSnippet(_) => (),
        }
    }
}
