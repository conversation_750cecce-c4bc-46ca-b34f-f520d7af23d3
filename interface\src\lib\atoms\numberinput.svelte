<script lang="ts">
	let {
		value = $bindable(),
		max,
		min,
		placeholder,
		onchange,
	}: {
		id?: string;
		value: number | null;
		placeholder?: string;
		min?: number;
		max?: number;
		onchange?: () => any;
	} = $props();

	$effect(() => {
		if (typeof value !== 'number') return;

		if (min && value < min) {
			value = min;
		}
		if (max && value > max) {
			value = max;
		}
	});
</script>

<input
	type="number"
	class="rounded-lg border border-input bg-input px-4 py-1 text-primary drop-shadow focus:outline-none focus:ring-2 focus:ring-accent"
	bind:value
	onchange={() => {
		onchange ? onchange() : null;
	}}
	{max}
	{min}
	{placeholder}
/>
