// Generated type definitions for fixture types system
import type { MovementChannels } from './MovementChannels';
import type { DmxFixtureColor } from './DmxFixtureColor';
import type { UnimplementedChannel } from './UnimplementedChannel';

export interface FixtureType {
    id?: number;
    name: string;
    fixturetype: string;
    footprint_size: number;
    movement_channels?: MovementChannels;
    color?: DmxFixtureColor;
    unimplemented_channels: UnimplementedChannel[];
}

export interface ShowFixtureInstance {
    id?: number;
    show_id: number;
    fixture_type_id: number;
    quantity: number;
    name_prefix: string;
}

export interface FixtureInstance {
    id?: number;
    show_fixture_instance_id: number;
    instance_name: string;
    dmx_address?: number;
    dmx_universe: number;
    stage_coordinates: [number, number];
}