<script lang="ts">
    import type { Snippet } from "svelte";
    import { tick } from "svelte";

    type TooltipPosition = "top" | "bottom" | "left" | "right" | "auto";

    let {
        children,
        tooltip,
        overwriteOrigin,
        hidden,
        position = "auto",
        delay = 300,
        offset = 8,
    }: {
        children: Snippet;
        tooltip: Snippet;
        overwriteOrigin?: { x: number; y: number };
        hidden?: boolean;
        position?: TooltipPosition;
        delay?: number;
        offset?: number;
    } = $props();

    let tooltipOriginElement: HTMLElement | undefined = $state();
    let tooltipElement: HTMLElement | undefined = $state();
    let isHovered = $state(false);
    let isVisible = $state(false);
    let timeoutId: NodeJS.Timeout | undefined = $state();
    let computedPosition: TooltipPosition = $state("bottom");

    let tooltipOrigin: { x: number; y: number } | undefined = $derived.by(
        () => {
            if (!tooltipOriginElement || !tooltipElement || !isVisible)
                return undefined;

            const triggerRect = tooltipOriginElement.getBoundingClientRect();
            const tooltipRect = tooltipElement.getBoundingClientRect();
            const viewportWidth = window.innerWidth;
            const viewportHeight = window.innerHeight;

            if (overwriteOrigin) {
                return overwriteOrigin;
            }

            let finalPosition = position;

            if (position === "auto") {
                const spaceTop = triggerRect.top;
                const spaceBottom = viewportHeight - triggerRect.bottom;
                const spaceLeft = triggerRect.left;
                const spaceRight = viewportWidth - triggerRect.right;

                const spaces = [
                    { pos: "top", space: spaceTop },
                    { pos: "bottom", space: spaceBottom },
                    { pos: "left", space: spaceLeft },
                    { pos: "right", space: spaceRight },
                ] as const;

                finalPosition = spaces.reduce((max, current) =>
                    current.space > max.space ? current : max,
                ).pos;
            }

            computedPosition = finalPosition;

            let x = 0;
            let y = 0;

            switch (finalPosition) {
                case "top":
                    x =
                        triggerRect.left +
                        triggerRect.width / 2 -
                        tooltipRect.width / 2;
                    y = triggerRect.top - tooltipRect.height - offset;
                    break;
                case "bottom":
                    x =
                        triggerRect.left +
                        triggerRect.width / 2 -
                        tooltipRect.width / 2;
                    y = triggerRect.bottom + offset;
                    break;
                case "left":
                    x = triggerRect.left - tooltipRect.width - offset;
                    y =
                        triggerRect.top +
                        triggerRect.height / 2 -
                        tooltipRect.height / 2;
                    break;
                case "right":
                    x = triggerRect.right + offset;
                    y =
                        triggerRect.top +
                        triggerRect.height / 2 -
                        tooltipRect.height / 2;
                    break;
            }

            x = Math.max(8, Math.min(x, viewportWidth - tooltipRect.width - 8));
            y = Math.max(
                8,
                Math.min(y, viewportHeight - tooltipRect.height - 8),
            );

            return {
                x: x + window.scrollX,
                y: y + window.scrollY,
            };
        },
    );

    async function mouseEnter() {
        if (timeoutId) {
            clearTimeout(timeoutId);
        }

        timeoutId = setTimeout(async () => {
            isHovered = true;
            isVisible = true;
            await tick();
        }, delay);
    }

    function mouseLeave() {
        if (timeoutId) {
            clearTimeout(timeoutId);
        }
        isHovered = false;
        isVisible = false;
    }

    // Clean up timeout on unmount
    $effect(() => {
        return () => {
            if (timeoutId) {
                clearTimeout(timeoutId);
            }
        };
    });
</script>

<div
    onfocus={() => {}}
    onmouseleave={mouseLeave}
    onmouseenter={mouseEnter}
    role="tooltip"
    bind:this={tooltipOriginElement}
>
    {@render children()}
    {#if isHovered && !hidden && isVisible}
        <div
            bind:this={tooltipElement}
            class="fixed z-50 px-3 py-2 text-sm text-primary bg-object border border-accent/20 rounded-lg shadow-lg backdrop-blur-sm
                   transform transition-all duration-200 ease-out
                   {computedPosition === 'top'
                ? 'animate-in slide-in-from-bottom-2'
                : ''}
                   {computedPosition === 'bottom'
                ? 'animate-in slide-in-from-top-2'
                : ''}
                   {computedPosition === 'left'
                ? 'animate-in slide-in-from-right-2'
                : ''}
                   {computedPosition === 'right'
                ? 'animate-in slide-in-from-left-2'
                : ''}"
            style="top: {tooltipOrigin?.y}px; left: {tooltipOrigin?.x}px;"
        >
            <!-- Arrow -->
            <div
                class="absolute w-2 h-2 bg-object border-accent/20 rotate-45
                        {computedPosition === 'top'
                    ? 'border-b border-r -bottom-1 left-1/2 -translate-x-1/2'
                    : ''}
                        {computedPosition === 'bottom'
                    ? 'border-t border-l -top-1 left-1/2 -translate-x-1/2'
                    : ''}
                        {computedPosition === 'left'
                    ? 'border-t border-r -right-1 top-1/2 -translate-y-1/2'
                    : ''}
                        {computedPosition === 'right'
                    ? 'border-b border-l -left-1 top-1/2 -translate-y-1/2'
                    : ''}"
            ></div>

            <!-- Content -->
            <div class="relative z-10">
                {@render tooltip()}
            </div>
        </div>
    {/if}
</div>
