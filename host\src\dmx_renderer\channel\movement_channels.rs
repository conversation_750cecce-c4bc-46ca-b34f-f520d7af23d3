use super::{DmxC<PERSON>nelEmitter, DmxChannelValue};
use crate::dmx_renderer::clamp_index_to_spline_length;
use crate::dmx_renderer::dynamics::property::PropertyFileDescriptor;
use crate::dmx_renderer::dynamics::IsDynamic;
use crate::{
    database_handler::pan_tilt_positions::ComposedPanTiltPosition,
    dmx_renderer::InterpolationMethod,
};
use core::cmp::Ordering;
use serde::{Deserialize, Serialize};
use splines::{Interpolation, Key, Spline};
use ts_rs::TS;

pub const PAN_DEFAULT: u8 = 128;
pub const TILT_DEFAULT: u8 = 128;

#[derive(Clone, Serialize, Deserialize, Debug, TS)]
#[ts(export)]
pub struct MovementChannels {
    pub pan: Option<DmxChannelValue>,
    pub tilt: Option<DmxChannelValue>,
    #[serde(skip)]
    pub pan_spline: Option<Spline<f32, f32>>,
    #[serde(skip)]
    pub tilt_spline: Option<Spline<f32, f32>>,
    #[serde(skip)]
    pub pan_origin: u8,
    #[serde(skip)]
    pub tilt_origin: u8,
    #[serde(skip)]
    pub pan_oneshot: bool,
    #[serde(skip)]
    pub tilt_oneshot: bool,
    #[serde(skip)]
    pub pan_blueprint_id: Option<usize>,
    #[serde(skip)]
    pub tilt_blueprint_id: Option<usize>,
}

#[derive(
    Clone, Copy, Serialize, Deserialize, Debug, Default, PartialEq, TS,
)]
#[ts(export)]
pub struct TimedPanTiltPosition {
    pub pan: u8,
    pub tilt: u8,
    pub x: f32,
}

impl MovementChannels {
    #[must_use]
    pub const fn pan_tilt(&self) -> (u8, u8) {
        let mut result = (0, 0);
        if let Some(pan) = self.pan.as_ref() {
            result.0 = pan.value;
        }
        if let Some(tilt) = self.tilt.as_ref() {
            result.1 = tilt.value;
        }
        result
    }
    pub const fn set_pan(&mut self, new_pan: u8) {
        if let Some(pan) = self.pan.as_mut() {
            pan.value = new_pan;
        }
        self.pan_blueprint_id = None;
    }
    pub const fn set_pan_origin(&mut self, new_pan_origin: u8) {
        self.pan_origin = new_pan_origin;
    }
    /// > 128 => `+`
    /// < 128 => `-`
    pub fn add_to_pan(&mut self, modifier: u8) -> u8 {
        self.pan_blueprint_id = None;
        if let Some(pan) = self.pan.as_mut() {
            let mut modifier: i32 = modifier.into();
            modifier = modifier.saturating_sub(128);
            let mut pan_value: i32 = pan.value.into();
            pan_value = pan_value.saturating_add(modifier);
            pan_value = pan_value.clamp(u8::MIN.into(), u8::MAX.into());
            pan.value = u8::try_from(pan_value).unwrap_or_default();
            pan.value
        } else {
            PAN_DEFAULT
        }
    }
    pub const fn set_tilt(&mut self, new_tilt: u8) {
        if let Some(tilt) = self.tilt.as_mut() {
            tilt.value = new_tilt;
        }
        self.tilt_blueprint_id = None;
    }
    pub const fn set_tilt_origin(&mut self, new_tilt_origin: u8) {
        self.tilt_origin = new_tilt_origin;
    }
    /// > 128 => `+`
    /// < 128 => `-`
    pub fn add_to_tilt(&mut self, modifier: u8) -> u8 {
        self.tilt_blueprint_id = None;
        if let Some(tilt) = self.tilt.as_mut() {
            let mut modifier: i32 = modifier.into();
            modifier = modifier.saturating_sub(128);
            let mut tilt_value: i32 = tilt.value.into();
            tilt_value = tilt_value.saturating_add(modifier);
            tilt_value = tilt_value.clamp(u8::MIN.into(), u8::MAX.into());
            tilt.value = u8::try_from(tilt_value).unwrap_or_default();
            tilt.value
        } else {
            TILT_DEFAULT
        }
    }
    pub fn extract_internal_spline_from_positions_in(
        &mut self,
        properties: &Vec<PropertyFileDescriptor>,
        all_positions: &[ComposedPanTiltPosition],
        fixture_id: usize,
        blueprint_id: Option<usize>,
        oneshot: bool,
    ) {
        let (interpolation_method, mut relevant_positions) =
            Self::extract_relevant_pan_tilt_positions(
                properties,
                all_positions,
                fixture_id,
            );

        relevant_positions.sort_unstable_by(|a, b| {
            if a.x > b.x {
                Ordering::Greater
            } else {
                Ordering::Less
            }
        });

        self.pan_spline = Some(splines::Spline::from_vec(
            relevant_positions
                .iter()
                .map(|position| Key {
                    t: position.x,
                    value: position.pan.into(),
                    interpolation: interpolation_method.map_or(
                        Interpolation::Cosine,
                        InterpolationMethod::into,
                    ),
                })
                .collect(),
        ));
        self.pan_blueprint_id = blueprint_id;
        self.pan_oneshot = oneshot;

        self.tilt_spline = Some(splines::Spline::from_vec(
            relevant_positions
                .iter()
                .map(|position| Key {
                    t: position.x,
                    value: position.tilt.into(),
                    interpolation: interpolation_method.map_or(
                        Interpolation::Linear,
                        InterpolationMethod::into,
                    ),
                })
                .collect(),
        ));
        self.tilt_blueprint_id = blueprint_id;
        self.tilt_oneshot = oneshot;
    }
    fn extract_relevant_pan_tilt_positions(
        properties: &Vec<PropertyFileDescriptor>,
        all_positions: &[ComposedPanTiltPosition],
        fixture_id: usize,
    ) -> (Option<InterpolationMethod>, Vec<TimedPanTiltPosition>) {
        let mut timed_pan_tilt_positions: Vec<TimedPanTiltPosition> = vec![];
        let mut result_interpolation_method: Option<InterpolationMethod> = None;

        for property_file_descriptor in properties {
            if let PropertyFileDescriptor::PanTiltPositions(
                position_coordinates,
                interpolation_method,
            ) = property_file_descriptor
            {
                for blueprint_position in position_coordinates {
                    if let Some(fixture_positions) =
                        all_positions.iter().find(|fixture_positions| {
                            fixture_positions.id
                                == blueprint_position.position_id
                        })
                    {
                        if let Some(self_position) = fixture_positions
                            .fixture_positions
                            .iter()
                            .find(|fixture_positions| {
                                fixture_positions.fixture_id() == fixture_id
                            })
                        {
                            timed_pan_tilt_positions.push(
                                TimedPanTiltPosition {
                                    pan: self_position.pan(),
                                    tilt: self_position.tilt(),
                                    x: blueprint_position.x,
                                },
                            );
                            result_interpolation_method =
                                Some(*interpolation_method);
                        }
                    }
                }
            }
        }

        (result_interpolation_method, timed_pan_tilt_positions)
    }
}
impl IsDynamic for MovementChannels {
    fn clear_all_splines(&mut self) {
        self.pan_spline = None;
        self.tilt_spline = None;
        self.pan_oneshot = false;
        self.tilt_oneshot = false;
        self.pan_blueprint_id = None;
        self.tilt_blueprint_id = None;
    }
    fn clear_splines_by_blueprint_id(&mut self, blueprint_id: usize) {
        if self.pan_blueprint_id == Some(blueprint_id) {
            self.pan_spline = None;
            self.pan_oneshot = false;
            self.pan_blueprint_id = None;
        }
        if self.tilt_blueprint_id == Some(blueprint_id) {
            self.tilt_spline = None;
            self.tilt_oneshot = false;
            self.tilt_blueprint_id = None;
        }
    }
    fn extract_internal_spline_from(
        &mut self,
        properties: &[PropertyFileDescriptor],
        oneshot: bool,
        blueprint_id: Option<usize>,
    ) {
        for property_file_descriptor in properties {
            if let PropertyFileDescriptor::UnimplementedChannel(channel_name) =
                property_file_descriptor
            {
                if channel_name.0 == "Pan" {
                    self.pan_spline = Some(property_file_descriptor.into());
                    self.pan_oneshot = oneshot;
                    self.pan_blueprint_id = blueprint_id;
                }
                if channel_name.0 == "Tilt" {
                    self.tilt_spline = Some(property_file_descriptor.into());
                    self.tilt_oneshot = oneshot;
                    self.tilt_blueprint_id = blueprint_id;
                }
            }
        }
    }
    fn apply_spline_index(&mut self, index: f32) {
        let mut take_sample_from_pan_spline = false;
        if let Some(ref pan_spline) = self.pan_spline {
            take_sample_from_pan_spline = true;
            if self.pan_oneshot {
                if let Some(last) = pan_spline.keys().last() {
                    if index >= last.t {
                        let value = last.value;
                        #[allow(
                            clippy::cast_possible_truncation,
                            clippy::cast_sign_loss,
                            clippy::as_conversions
                        )]
                        if let Some(mut pan) = self.pan {
                            pan.value = value.clamp(0., 255.) as u8;
                        }
                        take_sample_from_pan_spline = false;
                        self.pan_oneshot = false;
                        self.pan_blueprint_id = None;
                    }
                }
            }
            if take_sample_from_pan_spline {
                if let Some(sample) = pan_spline.clamped_sample(
                    clamp_index_to_spline_length(index, pan_spline),
                ) {
                    #[allow(
                        clippy::cast_possible_truncation,
                        clippy::cast_sign_loss,
                        clippy::as_conversions
                    )]
                    if let Some(pan) = self.pan.as_mut() {
                        pan.value = sample.clamp(0., 255.) as u8;
                    }
                }
            }
        }
        if !take_sample_from_pan_spline {
            self.pan_spline = None;
            self.pan_oneshot = false;
            self.pan_blueprint_id = None;
        }

        let mut take_sample_from_tilt_spline = false;
        if let Some(ref tilt_spline) = self.tilt_spline {
            take_sample_from_tilt_spline = true;
            if self.tilt_oneshot {
                if let Some(last) = tilt_spline.keys().last() {
                    if index >= last.t {
                        let value = last.value;
                        #[allow(
                            clippy::cast_possible_truncation,
                            clippy::cast_sign_loss,
                            clippy::as_conversions
                        )]
                        if let Some(mut tilt) = self.tilt {
                            tilt.value = value.clamp(0., 255.) as u8;
                        }
                        take_sample_from_tilt_spline = false;
                        self.tilt_oneshot = false;
                        self.tilt_blueprint_id = None;
                    }
                }
            }
            if take_sample_from_tilt_spline {
                if let Some(sample) = tilt_spline.clamped_sample(
                    clamp_index_to_spline_length(index, tilt_spline),
                ) {
                    #[allow(
                        clippy::cast_possible_truncation,
                        clippy::cast_sign_loss,
                        clippy::as_conversions
                    )]
                    if let Some(tilt) = self.tilt.as_mut() {
                        tilt.value = sample.clamp(0., 255.) as u8;
                    }
                }
            }
        }
        if !take_sample_from_tilt_spline {
            self.tilt_spline = None;
            self.tilt_oneshot = false;
            self.tilt_blueprint_id = None;
        }
    }
}

impl DmxChannelEmitter for MovementChannels {
    fn compute_dmx_channels(&mut self, _: u8) -> Vec<DmxChannelValue> {
        let mut result: Vec<DmxChannelValue> = vec![];

        if let Some(pan) = self.pan.as_mut() {
            let mut origin: i32 = self.pan_origin.into();
            origin = origin.saturating_sub(128);
            let mut pan_value: i32 = pan.value.into();
            pan_value = pan_value.saturating_add(origin);
            pan_value = pan_value.clamp(u8::MIN.into(), u8::MAX.into());
            pan.value = u8::try_from(pan_value).unwrap_or_default();
            result.push((pan.channel, pan.value).into());
        }
        if let Some(tilt) = self.tilt.as_mut() {
            let mut origin: i32 = self.tilt_origin.into();
            origin = origin.saturating_sub(128);
            let mut tilt_value: i32 = tilt.value.into();
            tilt_value = tilt_value.saturating_add(origin);
            tilt_value = tilt_value.clamp(u8::MIN.into(), u8::MAX.into());
            tilt.value = u8::try_from(tilt_value).unwrap_or_default();
            result.push((tilt.channel, tilt.value).into());
        }

        result
    }
}
