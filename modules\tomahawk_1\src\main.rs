#![no_std]
#![no_main]
#![feature(abi_avr_interrupt)]
#![feature(cell_update)]

use arduino_hal::{adc::AdcSettings, hal::usart::Event};
use avr_device::interrupt;
use button::{<PERSON><PERSON>, ButtonTick};
use core::cell::RefCell;
use millis::{millis, millis_init};
use my_rust_utils::clamp_cast::ClampCast;
use rwm_package_definitions::{RawInput, RwModuleUsbPackage, HEAD_1, HEAD_2, HEAD_3};

use rwm_communication::{
    sendable_bytes_queue::SendableBytesQueueHead, ACKNOWLEDGED, HEAP, HEAP_MEM, HEAP_SIZE,
    IDENTIFIED,
};

const PAUSE_BETWEEN_SENDS_MS: u32 = 25;

#[arduino_hal::entry]
fn main() -> ! {
    unsafe { HEAP.init(HEAP_MEM.as_ptr() as usize, HEAP_SIZE) }

    #[allow(clippy::unwrap_used)]
    let dp = arduino_hal::Peripherals::take().unwrap();
    let pins = arduino_hal::pins!(dp);

    #[cfg(debug_assertions)]
    let mut debug_serial = arduino_hal::Usart::new(
        dp.USART3,
        pins.d15,
        pins.d14.into_output(),
        arduino_hal::hal::usart::BaudrateArduinoExt::into_baudrate(115_200),
    );

    millis_init(dp.TC0);

    let mut ack_timestamp = millis();

    let mut serial = arduino_hal::default_serial!(dp, pins, 250_000);
    let mut watchdog = arduino_hal::hal::wdt::Wdt::new(dp.WDT, &dp.CPU.mcusr);
    #[allow(clippy::unwrap_used)]
    watchdog
        .start(arduino_hal::hal::wdt::Timeout::Ms1000)
        .unwrap();

    let out_d22 = RefCell::new(pins.d22.into_output());
    let out_d24 = RefCell::new(pins.d24.into_output());
    let out_d26 = RefCell::new(pins.d26.into_output());
    let out_d28 = RefCell::new(pins.d28.into_output());
    let out_d30 = RefCell::new(pins.d30.into_output());
    let out_d32 = RefCell::new(pins.d32.into_output());
    let out_d34 = RefCell::new(pins.d34.into_output());
    let out_d36 = RefCell::new(pins.d36.into_output());
    let out_d38 = RefCell::new(pins.d38.into_output());
    let out_d40 = RefCell::new(pins.d40.into_output());

    let in_d16 = RefCell::new(pins.d16.into_pull_up_input());
    let in_d17 = RefCell::new(pins.d17.into_pull_up_input());
    let in_d18 = RefCell::new(pins.d18.into_pull_up_input());
    let in_d19 = RefCell::new(pins.d19.into_pull_up_input());
    let in_d20 = RefCell::new(pins.d20.into_pull_up_input());
    let in_d23 = RefCell::new(pins.d23.into_pull_up_input());
    let in_d25 = RefCell::new(pins.d25.into_pull_up_input());
    let in_d27 = RefCell::new(pins.d27.into_pull_up_input());
    let in_d29 = RefCell::new(pins.d29.into_pull_up_input());
    let in_d31 = RefCell::new(pins.d31.into_pull_up_input());
    let in_d33 = RefCell::new(pins.d33.into_pull_up_input());
    let in_d35 = RefCell::new(pins.d35.into_pull_up_input());
    let in_d37 = RefCell::new(pins.d37.into_pull_up_input());
    let in_d39 = RefCell::new(pins.d39.into_pull_up_input());
    let in_d41 = RefCell::new(pins.d41.into_pull_up_input());
    let in_d43 = RefCell::new(pins.d43.into_pull_up_input());
    let in_d45 = RefCell::new(pins.d45.into_pull_up_input());
    let in_d47 = RefCell::new(pins.d47.into_pull_up_input());
    let in_d49 = RefCell::new(pins.d49.into_pull_up_input());
    let in_d51 = RefCell::new(pins.d51.into_pull_up_input());
    let in_d53 = RefCell::new(pins.d53.into_pull_up_input());


    // selection
    let mut button_1 = Button::new(&in_d23, &out_d32);
    let mut button_2 = Button::new(&in_d25, &out_d32);
    let mut button_3 = Button::new(&in_d27, &out_d32);
    let mut button_4 = Button::new(&in_d29, &out_d32);
    let mut button_5 = Button::new(&in_d31, &out_d32);
    let mut button_6 = Button::new(&in_d33, &out_d32);
    let mut button_7 = Button::new(&in_d23, &out_d34);
    let mut button_8 = Button::new(&in_d25, &out_d34);
    let mut button_9 = Button::new(&in_d27, &out_d34);
    let mut button_10 = Button::new(&in_d29, &out_d34);
    let mut button_11 = Button::new(&in_d31, &out_d34);
    let mut button_12 = Button::new(&in_d33, &out_d34);
    let mut button_13 = Button::new(&in_d23, &out_d36);
    let mut button_14 = Button::new(&in_d23, &out_d38);
    let mut button_15 = Button::new(&in_d25, &out_d36);
    let mut button_16 = Button::new(&in_d25, &out_d38);
    let mut button_17 = Button::new(&in_d27, &out_d36);
    let mut button_18 = Button::new(&in_d27, &out_d38);
    let mut button_19 = Button::new(&in_d29, &out_d36);
    let mut button_20 = Button::new(&in_d29, &out_d38);
    let mut button_21 = Button::new(&in_d31, &out_d36);
    let mut button_22 = Button::new(&in_d31, &out_d38);
    let mut button_23 = Button::new(&in_d33, &out_d36);
    let mut button_24 = Button::new(&in_d33, &out_d38);
    let mut button_25 = Button::new(&in_d31, &out_d40);

    // loopstation
    let mut button_26 = Button::new(&in_d35, &out_d34);
    let mut button_27 = Button::new(&in_d35, &out_d38);
    let mut button_28 = Button::new(&in_d35, &out_d40);
    let mut button_29 = Button::new(&in_d37, &out_d34);
    let mut button_30 = Button::new(&in_d37, &out_d38);
    let mut button_31 = Button::new(&in_d37, &out_d40);
    let mut button_32 = Button::new(&in_d39, &out_d34);
    let mut button_33 = Button::new(&in_d39, &out_d38);

    // color
    let mut button_34 = Button::new(&in_d41, &out_d34);
    let mut button_35 = Button::new(&in_d41, &out_d36);
    let mut button_36 = Button::new(&in_d43, &out_d32);
    let mut button_37 = Button::new(&in_d45, &out_d30);
    let mut button_38 = Button::new(&in_d47, &out_d30);
    let mut button_39 = Button::new(&in_d49, &out_d32);
    let mut button_40 = Button::new(&in_d51, &out_d30);
    let mut button_41 = Button::new(&in_d51, &out_d34);
    let mut button_42 = Button::new(&in_d51, &out_d36);
    let mut button_43 = Button::new(&in_d43, &out_d38);
    let mut button_44 = Button::new(&in_d45, &out_d40);
    let mut button_45 = Button::new(&in_d47, &out_d40);
    let mut button_46 = Button::new(&in_d49, &out_d38);

    // animations
    let mut button_47 = Button::new(&in_d53, &out_d30);
    let mut button_48 = Button::new(&in_d53, &out_d32);
    let mut button_49 = Button::new(&in_d53, &out_d34);
    let mut button_50 = Button::new(&in_d16, &out_d30);
    let mut button_51 = Button::new(&in_d16, &out_d32);
    let mut button_52 = Button::new(&in_d16, &out_d34);
    let mut button_53 = Button::new(&in_d17, &out_d30);
    let mut button_54 = Button::new(&in_d17, &out_d32);
    let mut button_55 = Button::new(&in_d17, &out_d34);
    let mut button_56 = Button::new(&in_d18, &out_d30);
    let mut button_57 = Button::new(&in_d18, &out_d32);
    let mut button_58 = Button::new(&in_d18, &out_d34);
    let mut button_59 = Button::new(&in_d19, &out_d30);
    let mut button_60 = Button::new(&in_d19, &out_d32);
    let mut button_61 = Button::new(&in_d19, &out_d34);
    let mut button_62 = Button::new(&in_d20, &out_d30);
    let mut button_63 = Button::new(&in_d20, &out_d32);
    let mut button_64 = Button::new(&in_d20, &out_d34);
    let mut button_65 = Button::new(&in_d53, &out_d38);
    let mut button_66 = Button::new(&in_d53, &out_d40);
    let mut button_67 = Button::new(&in_d16, &out_d38);
    let mut button_68 = Button::new(&in_d16, &out_d40);
    let mut button_69 = Button::new(&in_d18, &out_d38);
    let mut button_70 = Button::new(&in_d19, &out_d40);
    let mut button_71 = Button::new(&in_d20, &out_d40);

    // other
    let mut button_72 = Button::new(&in_d35, &out_d22);
    let mut button_73 = Button::new(&in_d35, &out_d24);

    // admin
    let mut button_74 = Button::new(&in_d23, &out_d22);
    let mut button_75 = Button::new(&in_d25, &out_d22);
    let mut button_76 = Button::new(&in_d27, &out_d22);

    // group
    let mut button_77 = Button::new(&in_d37, &out_d22);
    let mut button_78 = Button::new(&in_d37, &out_d24);
    let mut button_79 = Button::new(&in_d37, &out_d26);
    let mut button_80 = Button::new(&in_d39, &out_d22);
    let mut button_81 = Button::new(&in_d39, &out_d24);
    let mut button_82 = Button::new(&in_d39, &out_d26);
    let mut button_83 = Button::new(&in_d41, &out_d22);
    let mut button_84 = Button::new(&in_d41, &out_d24);
    let mut button_85 = Button::new(&in_d41, &out_d26);
    let mut button_86 = Button::new(&in_d43, &out_d26);
    let mut button_87 = Button::new(&in_d45, &out_d22);
    let mut button_88 = Button::new(&in_d45, &out_d24);
    let mut button_89 = Button::new(&in_d39, &out_d28);
    let mut button_90 = Button::new(&in_d43, &out_d28);

    // quickanimations
    let mut button_91 = Button::new(&in_d47, &out_d26);
    let mut button_92 = Button::new(&in_d47, &out_d28);
    let mut button_93 = Button::new(&in_d49, &out_d26);
    let mut button_94 = Button::new(&in_d49, &out_d28);
    let mut button_95 = Button::new(&in_d51, &out_d26);
    let mut button_96 = Button::new(&in_d51, &out_d28);
    let mut button_97 = Button::new(&in_d53, &out_d26);
    let mut button_98 = Button::new(&in_d53, &out_d28);
    let mut button_99 = Button::new(&in_d16, &out_d26);
    let mut button_100 = Button::new(&in_d16, &out_d28);
    let mut button_101 = Button::new(&in_d17, &out_d26);
    let mut button_102 = Button::new(&in_d17, &out_d28);
    let mut button_103 = Button::new(&in_d47, &out_d22);
    let mut button_104 = Button::new(&in_d47, &out_d24);
    let mut button_105 = Button::new(&in_d49, &out_d22);
    let mut button_106 = Button::new(&in_d49, &out_d24);
    let mut button_107 = Button::new(&in_d51, &out_d22);
    let mut button_108 = Button::new(&in_d51, &out_d24);
    let mut button_109 = Button::new(&in_d53, &out_d22);
    let mut button_110 = Button::new(&in_d53, &out_d24);
    let mut button_111 = Button::new(&in_d16, &out_d22);
    let mut button_112 = Button::new(&in_d16, &out_d24);
    let mut button_113 = Button::new(&in_d17, &out_d22);
    let mut button_114 = Button::new(&in_d17, &out_d24);

    // other
    let mut button_115 = Button::new(&in_d35, &out_d26);
    let mut button_116 = Button::new(&in_d35, &out_d28);

    #[rustfmt::skip]
    let mut buttons: [&mut dyn ButtonTick; 116] = [ &mut button_1, &mut button_2, &mut button_3, &mut button_4, &mut button_5, &mut button_6, &mut button_7, &mut button_8, &mut button_9, &mut button_10, &mut button_11, &mut button_12, &mut button_13, &mut button_14, &mut button_15, &mut button_16, &mut button_17, &mut button_18, &mut button_19, &mut button_20, &mut button_21, &mut button_22, &mut button_23, &mut button_24, &mut button_25, &mut button_26, &mut button_27, &mut button_28, &mut button_29, &mut button_30, &mut button_31, &mut button_32, &mut button_33, &mut button_34, &mut button_35, &mut button_36, &mut button_37, &mut button_38, &mut button_39, &mut button_40, &mut button_41, &mut button_42, &mut button_43, &mut button_44, &mut button_45, &mut button_46, &mut button_47, &mut button_48, &mut button_49, &mut button_50, &mut button_51, &mut button_52, &mut button_53, &mut button_54, &mut button_55, &mut button_56, &mut button_57, &mut button_58, &mut button_59, &mut button_60, &mut button_61, &mut button_62, &mut button_63, &mut button_64, &mut button_65, &mut button_66, &mut button_67, &mut button_68, &mut button_69, &mut button_70, &mut button_71, &mut button_72, &mut button_73, &mut button_74, &mut button_75, &mut button_76, &mut button_77, &mut button_78, &mut button_79, &mut button_80, &mut button_81, &mut button_82, &mut button_83, &mut button_84, &mut button_85, &mut button_86, &mut button_87, &mut button_88, &mut button_89, &mut button_90, &mut button_91, &mut button_92, &mut button_93, &mut button_94, &mut button_95, &mut button_96, &mut button_97, &mut button_98, &mut button_99, &mut button_100, &mut button_101, &mut button_102, &mut button_103, &mut button_104, &mut button_105, &mut button_106, &mut button_107, &mut button_108, &mut button_109, &mut button_110, &mut button_111, &mut button_112, &mut button_113, &mut button_114, &mut button_115, &mut button_116 ];

    // FADER
    let mut adc = arduino_hal::Adc::new(dp.ADC, AdcSettings::default());
    let fader_1_in = pins.a2.into_analog_input(&mut adc);
    let mut old_position_1: u16 = fader_1_in.analog_read(&mut adc);
    // pinMode(9, OUTPUT);
    // pinMode(8, OUTPUT);
    let fader_2_in = pins.a3.into_analog_input(&mut adc);
    let mut old_position_2: u16 = fader_2_in.analog_read(&mut adc);
    // pinMode(7, OUTPUT);
    // pinMode(6, OUTPUT);
    let fader_3_in = pins.a0.into_analog_input(&mut adc);
    let mut old_position_3: u16 = fader_3_in.analog_read(&mut adc);
    // pinMode(13, OUTPUT);
    // pinMode(12, OUTPUT);
    let fader_4_in = pins.a1.into_analog_input(&mut adc);
    let mut old_position_4: u16 = fader_4_in.analog_read(&mut adc);
    // pinMode(11, OUTPUT);
    // pinMode(10, OUTPUT);
    let fader_5_in = pins.a4.into_analog_input(&mut adc);
    let mut old_position_5: u16 = fader_5_in.analog_read(&mut adc);
    // pinMode(11, OUTPUT);
    // pinMode(10, OUTPUT);
    let fader_6_in = pins.a5.into_analog_input(&mut adc);
    let mut old_position_6: u16 = fader_6_in.analog_read(&mut adc);
    // pinMode(11, OUTPUT);
    // pinMode(10, OUTPUT);

    let mut sendable_bytes_queue = SendableBytesQueueHead::default();

    unsafe { avr_device::interrupt::enable() }

    loop {
        serial.listen(Event::RxComplete);
        watchdog.feed();
        if !interrupt::free(|cs| IDENTIFIED.borrow(cs).get()) {
            continue;
        }
        for (id, button) in buttons.iter_mut().enumerate() {
            let mut id: u16 = id.clamp_cast();
            id = id.saturating_add(1);
            if let Some(package) = button.tick(id) {
                sendable_bytes_queue.append_package(package);
            }
        }

        let new_position = fader_1_in.analog_read(&mut adc);
        if old_position_1.abs_diff(new_position) > 5 {
            old_position_1 = new_position;
            if sendable_bytes_queue.package_count(117) < 3 {
                sendable_bytes_queue.append_package(RawInput {
                    id: 117,
                    value: (new_position / 4).clamp_cast(),
                });
            }
        }
        #[cfg(not(debug_assertions))]
        {
            let new_position = fader_2_in.analog_read(&mut adc);
            if old_position_2.abs_diff(new_position) > 5 {
                old_position_2 = new_position;
                if sendable_bytes_queue.package_count(118) < 3 {
                    sendable_bytes_queue.append_package(RawInput {
                        id: 118,
                        value: (new_position / 4).clamp_cast(),
                    });
                }
            }
        }
        let new_position = fader_3_in.analog_read(&mut adc);
        if old_position_3.abs_diff(new_position) > 5 {
            old_position_3 = new_position;
            if sendable_bytes_queue.package_count(119) < 3 {
                sendable_bytes_queue.append_package(RawInput {
                    id: 119,
                    value: (new_position / 4).clamp_cast(),
                });
            }
        }
        let new_position = fader_4_in.analog_read(&mut adc);
        if old_position_4.abs_diff(new_position) > 5 {
            old_position_4 = new_position;
            if sendable_bytes_queue.package_count(120) < 3 {
                sendable_bytes_queue.append_package(RawInput {
                    id: 120,
                    value: (new_position / 4).clamp_cast(),
                });
            };
        }
        #[cfg(not(debug_assertions))]
        {
            let new_position = fader_5_in.analog_read(&mut adc);
            if old_position_5.abs_diff(new_position) > 5 {
                old_position_5 = new_position;
                if sendable_bytes_queue.package_count(121) < 3 {
                    sendable_bytes_queue.append_package(RawInput {
                        id: 121,
                        value: (new_position / 4).clamp_cast(),
                    });
                }
            }
            let new_position = fader_6_in.analog_read(&mut adc);
            if old_position_6.abs_diff(new_position) > 5 {
                old_position_6 = new_position;
                if sendable_bytes_queue.package_count(122) < 3 {
                    sendable_bytes_queue.append_package(RawInput {
                        id: 122,
                        value: (new_position / 4).clamp_cast(),
                    });
                };
            }
        }

        if millis().wrapping_sub(ack_timestamp) > PAUSE_BETWEEN_SENDS_MS {
            if interrupt::free(|cs| ACKNOWLEDGED.borrow(cs).get()) {
                #[cfg(debug_assertions)]
                {
                    debug_serial.write_byte(110);
                    debug_serial.write_byte(10);
                    debug_serial.write_byte(13);
                }
                if sendable_bytes_queue.waiting_for_ack() {
                    sendable_bytes_queue.discard_first();
                    sendable_bytes_queue.set_waiting_for_ack(false);
                }
                if sendable_bytes_queue.is_empty() {
                    continue;
                }
            }
            if let Some(package) = sendable_bytes_queue.first() {
                let package_as_array: [u8; 3] = package.into();
                let package = [
                    HEAD_1,
                    HEAD_2,
                    HEAD_3,
                    RwModuleUsbPackage::Input(RawInput::default()).into(),
                    package_as_array[0],
                    package_as_array[1],
                    package_as_array[2],
                ];
                for byte in package {
                    serial.write_byte(byte);
                }
                sendable_bytes_queue.set_waiting_for_ack(true);
            }
            ack_timestamp = millis();
        }
    }
}
