// This file was generated by [ts-rs](https://github.com/Aleph-Alpha/ts-rs). Do not edit this file manually.
import type { ColorPropertyCoordinate } from "./ColorPropertyCoordinate";
import type { UnimplementedChannelPropertyCoordinate } from "./UnimplementedChannelPropertyCoordinate";

export type TimedPropertyFileDescriptor = { "UnimplementedChannel": [string, Array<UnimplementedChannelPropertyCoordinate>, number, Array<number>] } | { "ColorPropertyCoordinates": [Array<ColorPropertyCoordinate>, number, Array<number>] } | { "CallSnippet": [number, number, Array<number>] };