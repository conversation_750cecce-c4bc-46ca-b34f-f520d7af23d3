export interface DndData {
    id: number
}
export function draggable(node: HTMLElement, data: number | string) {
    let state = data;

    node.draggable = true;
    node.style.cursor = 'grab';

    function handle_dragstart(e: DragEvent) {
        if (!e.dataTransfer) return;
        e.dataTransfer.setData('text/plain', data.toString());
    }

    node.addEventListener('dragstart', handle_dragstart);

    return {
        update(id: number) {
            state = id;
        },

        destroy() {
            node.removeEventListener('dragstart', handle_dragstart);
        }
    };
}

interface DropzoneOptions {

    dropEffect: 'move' | 'none' | 'copy' | 'link';
    dragover_class: string;
    on_dropzone: (options: string, e: DragEvent) => void;
}
export function dropzone(node: HTMLElement, options: DropzoneOptions) {
    let state:
        DropzoneOptions
        = {
        ...options,
        dropEffect: 'move',
        dragover_class: 'droppable',
    };

    function handle_dragenter(e: DragEvent) {
        if (!(e.target instanceof HTMLElement)) return;
        e.target.classList.add(state.dragover_class);
    }

    function handle_dragleave(e: DragEvent) {
        if (!(e.target instanceof HTMLElement)) return;
        e.target.classList.remove(state.dragover_class);
    }

    function handle_dragover(e: DragEvent) {
        e.preventDefault();
        if (!e.dataTransfer) return;
        e.dataTransfer.dropEffect = state.dropEffect;
    }

    function handle_drop(e: DragEvent) {
        e.preventDefault();
        if (!e.dataTransfer) return;
        const data = e.dataTransfer.getData('text/plain');
        if (!(e.target instanceof HTMLElement)) return;
        e.target.classList.remove(state.dragover_class);
        state.on_dropzone(data, e);
    }

    node.addEventListener('dragenter', handle_dragenter);
    node.addEventListener('dragleave', handle_dragleave);
    node.addEventListener('dragover', handle_dragover);
    node.addEventListener('drop', handle_drop);

    return {
        update(options: DropzoneOptions) {
            state = {
                ...options,
                dropEffect: 'move',
                dragover_class: 'droppable',
            };
        },

        destroy() {
            node.removeEventListener('dragenter', handle_dragenter);
            node.removeEventListener('dragleave', handle_dragleave);
            node.removeEventListener('dragover', handle_dragover);
            node.removeEventListener('drop', handle_drop);
        }
    };
}

