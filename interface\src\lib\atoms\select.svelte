<script lang="ts" generics="T extends string | number | null">
    import type { Snippet } from "svelte";

    let {
        name,
        value = $bindable(),
        disabled,
        onchange,
        children,
        disableYPadding = false,
    }: {
        name?: string;
        value: T;
        disableYPadding?: boolean;
        onchange?: (newValue: T) => any;
        disabled?: boolean;
        children: Snippet;
    } = $props();
</script>

<select
    class="rounded-lg border border-input bg-input px-4 drop-shadow focus:outline-none focus:ring-2 focus:ring-accent"
    class:py-1={!disableYPadding}
    class:text-disabled={disabled}
    class:text-primary={!disabled}
    bind:value
    {name}
    onchange={() => (onchange ? onchange(value) : null)}
    {disabled}
>
    {@render children()}
</select>
