CREATE TABLE fixturegroups (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name <PERSON><PERSON><PERSON><PERSON>(255),
    show_id INT,
    FOREI<PERSON><PERSON> KEY (show_id) REFERENCES shows(id) ON DELETE CASCADE
);

CREATE TABLE fixturegroup_fixtures (
    id INT PRIMARY KEY AUTO_INCREMENT,
    fixturegroup_id INT,
    fixture_id INT,
    FOREIG<PERSON> KEY (fixturegroup_id) REFERENCES fixturegroups(id) ON DELETE CASCADE,
    FOREIGN KEY (fixture_id) REFERENCES fixtures(id) ON DELETE CASCADE
);
