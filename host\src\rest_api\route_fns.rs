use crate::database_handler::audiofiles::sync_audiofiles_with_filesystem;
use crate::database_handler::fixturegroups::FixtureGroup;
use crate::database_handler::show::Show;
use crate::dmx_renderer::dynamics::timecode::TimecodeFileDescriptor;
use axum::extract::rejection::JsonRejection;
use axum::extract::{multipart::Multipart, State};
use axum::extract::{Path, Query};
use axum::http::{header, Method, StatusCode};
use axum::response::IntoResponse;
use axum::Json;
use gdtf_parser::Gdtf;
use serde::Deserialize;
use std::process::Command;
use ts_rs::TS;

use crate::database_handler::directory_tree::Directory;
use crate::database_handler::pan_tilt_positions::ComposedPanTiltPosition;
use crate::database_handler::snippet::{Snippet, SnippetCategory};
use crate::dmx_renderer::dynamics::blueprint::BlueprintFileDescriptor;
use crate::input_parser::structs::{Instruction, RawInput};
use crate::logging;

use super::{deserialize_json_body, lock_db_handler_error, RouteState};

fn sanitize_filename(filename: &str) -> String {
    let sanitized = filename
        .chars()
        .map(|c| match c {
            c if c.is_alphanumeric() || c == '.' || c == '_' || c == ' ' => c,
            _ => '_',
        })
        .collect::<String>();

    let cleaned = sanitized
        .split(&['_', ' '][..])
        .filter(|s| !s.is_empty())
        .collect::<Vec<&str>>()
        .join("_");

    let result = cleaned.chars().take(255).collect::<String>();
    result
}

#[allow(clippy::unused_async)]
pub async fn ping_pong(
    State(_): State<RouteState>,
) -> (StatusCode, Json<String>) {
    (StatusCode::OK, Json(env!("CARGO_PKG_VERSION").to_owned()))
}
#[derive(Clone, serde::Serialize, TS)]
#[ts(export)]
pub struct BackendInfo {
    name: String,
    version: String,
    artnet_sending: bool,
    uptime_seconds: u64,
}

#[allow(clippy::unused_async)]
pub async fn here_i_am(
    State(state): State<RouteState>,
) -> (StatusCode, Json<BackendInfo>) {
    let uptime_seconds = state.start_time.elapsed().as_secs();

    let artnet_sending = state
        .dmx_renderer
        .lock()
        .is_ok_and(|dmx_renderer| dmx_renderer.is_artnet_active());

    (
        StatusCode::OK,
        Json(BackendInfo {
            name: "ruhige_waldgeraeusche".to_owned(),
            version: env!("CARGO_PKG_VERSION").to_owned(),
            artnet_sending,
            uptime_seconds,
        }),
    )
}
#[allow(clippy::unused_async)]
pub async fn shutdown(State(_): State<RouteState>) -> StatusCode {
    if Command::new("sudo")
        .arg("shutdown")
        .arg("now")
        .status()
        .is_ok()
    {
        StatusCode::OK
    } else {
        StatusCode::INTERNAL_SERVER_ERROR
    }
}
#[allow(clippy::unused_async)]
pub async fn most_recent_key_id(
    State(state): State<RouteState>,
) -> (StatusCode, Json<u16>) {
    state.input_parser.lock().map_or(
        (StatusCode::INTERNAL_SERVER_ERROR, Json(0)),
        |input_parser| {
            input_parser.most_recent_input_activated.map_or(
                (StatusCode::NO_CONTENT, Json(0)),
                |most_recent_input_activated| {
                    (StatusCode::OK, Json(most_recent_input_activated))
                },
            )
        },
    )
}

#[allow(clippy::unused_async)]
pub async fn upload_audiofile(
    State(state): State<RouteState>,
    mut multipart: Multipart,
) -> StatusCode {
    while let Ok(next_field) = multipart.next_field().await {
        if let Some(field_content) = next_field {
            if let Some(field_name) = field_content.name() {
                if field_name != "audiofile" {
                    continue;
                }

                let file_name = if let Some(file_name) =
                    field_content.file_name()
                {
                    let sanitized_name = sanitize_filename(file_name);
                    if sanitized_name.is_empty() || sanitized_name == "_" {
                        logging::log(
                                format!("Filename '{file_name}' could not be sanitized to a valid name"),
                                logging::LogLevel::Warning,
                                false,
                            );
                        return StatusCode::UNPROCESSABLE_ENTITY;
                    }
                    sanitized_name
                } else {
                    return StatusCode::UNPROCESSABLE_ENTITY;
                };
                if let Ok(data) = field_content.bytes().await {
                    if let Ok(mut db_handler) = state.db_handler.lock() {
                        db_handler.save_audiofile(&file_name.clone(), &data);
                        return StatusCode::CREATED;
                    }
                    return StatusCode::INTERNAL_SERVER_ERROR;
                }
                return StatusCode::UNPROCESSABLE_ENTITY;
            }
        }
    }
    StatusCode::NOT_ACCEPTABLE
}
#[allow(clippy::unused_async)]
pub async fn download_audiofile(
    State(state): State<RouteState>,
    Path(id): Path<usize>,
) -> impl IntoResponse {
    state.db_handler.lock().map_or(
        StatusCode::INTERNAL_SERVER_ERROR.into_response(),
        |mut db_handler| {
            let audiofile = db_handler.get_audiofile(id);
            let headers = [
                (header::CONTENT_TYPE, ".mp3"),
                (
                    header::CONTENT_DISPOSITION,
                    &format!("attachment; filename=\"{}\"", audiofile.0),
                ),
            ];
            (headers, audiofile.1).into_response()
        },
    )
}
#[allow(clippy::unused_async)]
pub async fn get_available_audiofile_names(
    State(state): State<RouteState>,
) -> (StatusCode, Json<Vec<(usize, String)>>) {
    state.db_handler.lock().map_or(
        (StatusCode::INTERNAL_SERVER_ERROR, Json(vec![])),
        |mut db_handler| {
            (StatusCode::OK, Json(db_handler.available_audiofile_names()))
        },
    )
}

#[allow(clippy::unused_async)]
pub async fn delete_audiofile(
    State(state): State<RouteState>,
    Path(id): Path<usize>,
) -> StatusCode {
    state.db_handler.lock().map_or(
        StatusCode::INTERNAL_SERVER_ERROR,
        |mut db_handler| {
            db_handler.remove_audiofile(id);
            StatusCode::OK
        },
    )
}

#[allow(clippy::unused_async)]
pub async fn sync_audiofiles(State(state): State<RouteState>) -> StatusCode {
    state.db_handler.lock().map_or(
        StatusCode::INTERNAL_SERVER_ERROR,
        |mut db_handler| {
            let available_audiofile_names =
                db_handler.available_audiofile_names();
            sync_audiofiles_with_filesystem(
                &mut db_handler.db_connection(),
                &available_audiofile_names,
            );
            StatusCode::OK
        },
    )
}

#[allow(clippy::unused_async)]
pub async fn get_dashboard(
    State(state): State<RouteState>,
) -> (StatusCode, Json<String>) {
    state.dashboard.lock().map_or(
        (StatusCode::INTERNAL_SERVER_ERROR, Json(String::new())),
        |dashboard| {
            serde_json::to_string(&(*dashboard)).map_or(
                (StatusCode::INTERNAL_SERVER_ERROR, Json(String::new())),
                |serialized_dashboard| {
                    (StatusCode::OK, Json(serialized_dashboard))
                },
            )
        },
    )
}

pub async fn get_dmx_universe(
    State(state): State<RouteState>,
    Path(id): Path<u16>,
) -> (StatusCode, Json<Vec<u8>>) {
    let db_handler_result = state.db_handler.lock();
    let dmx_renderer_result = state.dmx_renderer.lock();
    let input_parser_result = state.input_parser.lock();

    if let (Ok(mut db_handler), Ok(mut dmx_renderer), Ok(mut input_parser)) =
        (db_handler_result, dmx_renderer_result, input_parser_result)
    {
        let dmx_channels = dmx_renderer.build_dmx_universe(
            id,
            &mut db_handler,
            &mut input_parser,
            0.,
        );
        (StatusCode::OK, Json(dmx_channels))
    } else {
        logging::log(
            "Could not lock required components for dmx output endpoint"
                .to_owned(),
            logging::LogLevel::Warning,
            true,
        );
        (StatusCode::INTERNAL_SERVER_ERROR, Json(vec![]))
    }
}

#[allow(clippy::unused_async)]
pub async fn handle_keyevent(
    State(state): State<RouteState>,
    Query(keys): Query<Vec<RawInput>>,
) -> StatusCode {
    state.input_parser.lock().map_or(
        StatusCode::INTERNAL_SERVER_ERROR,
        |mut input_parser| {
            for input in keys {
                input_parser.push_to_inputs(input);
            }
            StatusCode::OK
        },
    )
}

#[derive(Clone, Deserialize, TS)]
#[ts(export)]
pub struct InstructionsWithValue {
    instructions: Vec<Instruction>,
    value: u8,
}
#[allow(clippy::unused_async)]
pub async fn process_one_time_instructions(
    State(state): State<RouteState>,
    result: Result<Json<InstructionsWithValue>, JsonRejection>,
) -> StatusCode {
    state.input_parser.lock().map_or(
        StatusCode::INTERNAL_SERVER_ERROR,
        |mut input_parser| match deserialize_json_body(
            result,
            &("/process_one_time_instructions", Method::PUT),
        ) {
            Ok(e) => {
                input_parser
                    .push_one_time_instructions(&e.instructions, e.value);
                StatusCode::OK
            }
            Err(_) => StatusCode::BAD_REQUEST,
        },
    )
}

#[allow(clippy::unused_async)]
pub async fn get_fixtures_library(
    State(state): State<RouteState>,
) -> (StatusCode, Json<Vec<DmxFixtureLibraryDescriptor>>) {
    state.db_handler.lock().map_or_else(
        |e| {
            lock_db_handler_error::<Json<Vec<DmxFixtureLibraryDescriptor>>>(
                Some(&e),
                "get_fixtures_library",
            )
        },
        |mut db_handler| {
            let fixtures = db_handler.library_fixtures();
            (StatusCode::OK, Json(fixtures))
        },
    )
}
#[allow(clippy::unused_async)]
pub async fn get_positions(
    State(state): State<RouteState>,
) -> (StatusCode, Json<Vec<ComposedPanTiltPosition>>) {
    state.db_handler.lock().map_or_else(
        |e| {
            lock_db_handler_error::<Json<Vec<ComposedPanTiltPosition>>>(
                Some(&e),
                "get_positions",
            )
        },
        |db_handler| {
            (
                StatusCode::OK,
                Json(db_handler.active_show.positions.clone()),
            )
        },
    )
}
#[derive(Clone, Deserialize, TS)]
#[ts(export)]
pub struct PanTiltPositionRenamePayload {
    id: usize,
    name: String,
}
#[allow(clippy::unused_async)]
pub async fn rename_position(
    State(state): State<RouteState>,
    result: Result<Json<PanTiltPositionRenamePayload>, JsonRejection>,
) -> StatusCode {
    state.db_handler.lock().map_or(
        StatusCode::INTERNAL_SERVER_ERROR,
        |mut db_handler| match deserialize_json_body(
            result,
            &("/rename_pan_tilt_position", Method::PATCH),
        ) {
            Ok(e) => {
                db_handler.rename_pan_tilt_position(e.id, &e.name);
                StatusCode::OK
            }
            Err(_) => StatusCode::BAD_REQUEST,
        },
    )
}
#[derive(Clone, Deserialize, TS)]
#[ts(export)]
pub struct PanTiltPositionDeletePayload {
    id: usize,
}
#[allow(clippy::unused_async, clippy::option_if_let_else)]
pub async fn delete_position(
    State(state): State<RouteState>,
    result: Result<Json<PanTiltPositionDeletePayload>, JsonRejection>,
) -> StatusCode {
    state.db_handler.lock().map_or(
        StatusCode::INTERNAL_SERVER_ERROR,
        |mut db_handler| match deserialize_json_body(
            result,
            &("/delete_pan_tilt_position", Method::DELETE),
        ) {
            Ok(e) => {
                db_handler.delete_pan_tilt_position(e.id);
                StatusCode::OK
            }
            Err(_) => StatusCode::BAD_REQUEST,
        },
    )
}
#[derive(Clone, Deserialize, TS)]
#[ts(export)]
pub struct PanTiltPositionPayload {
    position_name: String,
}
#[allow(clippy::unused_async)]
pub async fn trigger_pan_tilt_position_creation(
    State(state): State<RouteState>,
    result: Result<Json<PanTiltPositionPayload>, JsonRejection>,
) -> (StatusCode, String) {
    match deserialize_json_body(
        result,
        &("/createpantiltposition", Method::POST),
    ) {
        Ok(e) => {
            if let Ok(mut db_handler) = state.db_handler.lock() {
                db_handler.trigger_pan_tilt_position_creation(
                    &state.dmx_renderer,
                    e.position_name.as_str(),
                );
                return (StatusCode::OK, "Ok".to_string());
            }
            logging::log(
                "Could not lock DbHandler for pan_tilt position creation triggering"
                    .to_string(),
                logging::LogLevel::Warning,
                true,
            );
            (StatusCode::OK, "Ok".to_string())
        }
        Err(err) => err,
    }
}
#[allow(clippy::unused_async)]
pub async fn trigger_pan_tilt_position_update(
    State(state): State<RouteState>,
    Path(id): Path<usize>,
) -> (StatusCode, String) {
    if let Ok(mut db_handler) = state.db_handler.lock() {
        db_handler.trigger_pan_tilt_position_update(&state.dmx_renderer, id);
        return (StatusCode::OK, "Ok".to_string());
    }
    logging::log(
        "Could not lock DbHandler for pan_tilt position update triggering"
            .to_string(),
        logging::LogLevel::Warning,
        true,
    );
    (StatusCode::OK, "Ok".to_string())
}

#[allow(clippy::unused_async)]
pub async fn patch_fixture(
    State(state): State<RouteState>,
    result: Result<Json<DmxFixtureFileDescriptor>, JsonRejection>,
) -> (StatusCode, String) {
    match deserialize_json_body(result, &("/fixture", Method::PATCH)) {
        Ok(payload) => state.db_handler.lock().map_or_else(
            |e| lock_db_handler_error::<String>(Some(&e), "patch_fixture"),
            |mut db_handler| {
                db_handler.update_fixture(&payload);
                (StatusCode::OK, String::new())
            },
        ),
        Err(err) => err,
    }
}
#[allow(clippy::unused_async)]
pub async fn delete_fixture(
    State(state): State<RouteState>,
    Path(id): Path<usize>,
) -> (StatusCode, String) {
    let Ok(mut db_handler) = state.db_handler.lock() else {
        return lock_db_handler_error::<String>(None, "delete_fixture");
    };
    db_handler.delete_fixture(id);
    (StatusCode::OK, String::new())
}

#[allow(clippy::unused_async)]
pub async fn get_snippets_dir(
    State(state): State<RouteState>,
) -> (StatusCode, Json<Directory<Snippet>>) {
    state.db_handler.lock().map_or_else(
        |e| {
            lock_db_handler_error::<Json<Directory<Snippet>>>(
                Some(&e),
                "get_snippets_dir",
            )
        },
        |db_handler| {
            db_handler.active_show.snippets_dir.clone().map_or_else(
                || {
                    logging::log(
                        "No snippets are found in show file".to_string(),
                        logging::LogLevel::Warning,
                        true,
                    );
                    (StatusCode::NO_CONTENT, Json(Directory::default()))
                },
                |snippets| (StatusCode::OK, Json(snippets)),
            )
        },
    )
}
#[allow(clippy::unused_async)]
pub async fn put_new_default_snippets_dir(
    State(state): State<RouteState>,
    Path(id): Path<usize>,
) -> (StatusCode, Json<String>) {
    state.db_handler.lock().map_or_else(
        |e| {
            lock_db_handler_error::<Json<String>>(
                Some(&e),
                "put_new_default_snippets_dir",
            )
        },
        |mut db_handler| {
            db_handler.create_default_snippet_directory(id);
            (StatusCode::OK, Json(String::new()))
        },
    )
}
#[allow(clippy::unused_async)]
pub async fn delete_snippets_dir(
    State(state): State<RouteState>,
    Path(id): Path<usize>,
) -> (StatusCode, Json<String>) {
    state.db_handler.lock().map_or_else(
        |e| {
            lock_db_handler_error::<Json<String>>(
                Some(&e),
                "delete_snippets_dir",
            )
        },
        |mut db_handler| {
            db_handler.delete_snippets_dir(id);
            (StatusCode::OK, Json(String::new()))
        },
    )
}
#[allow(clippy::unused_async)]
pub async fn delete_snippet(
    State(state): State<RouteState>,
    Path(id): Path<usize>,
) -> (StatusCode, Json<String>) {
    state.db_handler.lock().map_or_else(
        |e| lock_db_handler_error::<Json<String>>(Some(&e), "delete_snippet"),
        |mut db_handler| {
            db_handler.delete_snippet(id);
            (StatusCode::OK, Json(String::new()))
        },
    )
}
#[allow(clippy::unused_async)]
pub async fn put_new_default_snippet(
    State(state): State<RouteState>,
    Path(parent_id): Path<usize>,
) -> (StatusCode, Json<String>) {
    state.db_handler.lock().map_or_else(
        |e| {
            lock_db_handler_error::<Json<String>>(
                Some(&e),
                "put_new_default_snippet",
            )
        },
        |mut db_handler| {
            db_handler.create_default_snippet(parent_id);
            (StatusCode::OK, Json(String::new()))
        },
    )
}
#[allow(clippy::unused_async)]
pub async fn patch_snippets_dir_name(
    State(state): State<RouteState>,
    Path(id): Path<usize>,
    result: Result<Json<String>, JsonRejection>,
) -> (StatusCode, String) {
    match deserialize_json_body(
        result,
        &("/snippets_dir/:id/name", Method::PATCH),
    ) {
        Ok(payload) => state.db_handler.lock().map_or_else(
            |e| {
                lock_db_handler_error::<String>(
                    Some(&e),
                    "patch_snippets_dir_name",
                )
            },
            |mut db_handler| {
                db_handler.set_snippets_dir_name(id, &payload);
                (StatusCode::OK, String::new())
            },
        ),
        Err(err) => err,
    }
}
#[allow(clippy::unused_async)]
pub async fn patch_snippet_name(
    State(state): State<RouteState>,
    Path(id): Path<usize>,
    result: Result<Json<String>, JsonRejection>,
) -> (StatusCode, String) {
    match deserialize_json_body(result, &("/snippets/:id/name", Method::PATCH))
    {
        Ok(payload) => state.db_handler.lock().map_or_else(
            |e| lock_db_handler_error::<String>(Some(&e), "patch_snippet_name"),
            |mut db_handler| {
                db_handler.set_snippet_name(id, &payload);
                (StatusCode::OK, String::new())
            },
        ),
        Err(err) => err,
    }
}
#[allow(clippy::unused_async)]
pub async fn patch_snippet_instructions(
    State(state): State<RouteState>,
    Path(id): Path<usize>,
    result: Result<Json<Vec<Instruction>>, JsonRejection>,
) -> (StatusCode, String) {
    match deserialize_json_body(result, &("/snippets/:id/name", Method::PATCH))
    {
        Ok(payload) => state.db_handler.lock().map_or_else(
            |e| {
                lock_db_handler_error::<String>(
                    Some(&e),
                    "patch_snippet_instructions",
                )
            },
            |mut db_handler| {
                db_handler.set_snippet_instructions(id, &payload);
                (StatusCode::OK, String::new())
            },
        ),
        Err(err) => err,
    }
}
#[allow(clippy::unused_async)]
pub async fn patch_snippet_category(
    State(state): State<RouteState>,
    Path(id): Path<usize>,
    result: Result<Json<SnippetCategory>, JsonRejection>,
) -> (StatusCode, String) {
    match deserialize_json_body(
        result,
        &("/snippets/:id/category", Method::PATCH),
    ) {
        Ok(payload) => state.db_handler.lock().map_or_else(
            |e| {
                lock_db_handler_error::<String>(
                    Some(&e),
                    "patch_snippet_category",
                )
            },
            |mut db_handler| {
                db_handler.set_snippet_category(id, payload);
                (StatusCode::OK, String::new())
            },
        ),
        Err(err) => err,
    }
}
#[allow(clippy::unused_async)]
pub async fn patch_snippet_do_not_use_instructions(
    State(state): State<RouteState>,
    Path(id): Path<usize>,
    result: Result<Json<bool>, JsonRejection>,
) -> (StatusCode, String) {
    match deserialize_json_body(
        result,
        &("/snippets/:id/do_not_use_instructions", Method::PATCH),
    ) {
        Ok(payload) => state.db_handler.lock().map_or_else(
            |e| {
                lock_db_handler_error::<String>(
                    Some(&e),
                    "patch_snippet_do_not_use_instructions",
                )
            },
            |mut db_handler| {
                db_handler.set_snippet_do_not_use_instructions(id, payload);
                (StatusCode::OK, String::new())
            },
        ),
        Err(err) => err,
    }
}
#[allow(clippy::unused_async)]
pub async fn patch_snippet_serial_module_key(
    State(state): State<RouteState>,
    Path(id): Path<usize>,
    result: Result<Json<Option<u16>>, JsonRejection>,
) -> (StatusCode, String) {
    match deserialize_json_body(
        result,
        &("/snippets/:id/serial_module_key", Method::PATCH),
    ) {
        Ok(payload) => state.db_handler.lock().map_or_else(
            |e| {
                lock_db_handler_error::<String>(
                    Some(&e),
                    "patch_snippet_serial_module_key",
                )
            },
            |mut db_handler| {
                db_handler.set_snippet_serial_module_key(id, payload);
                (StatusCode::OK, String::new())
            },
        ),
        Err(err) => err,
    }
}
#[allow(clippy::unused_async)]
pub async fn patch_snippet_requires_user_action_reason(
    State(state): State<RouteState>,
    Path(id): Path<usize>,
    result: Result<Json<Option<String>>, JsonRejection>,
) -> (StatusCode, String) {
    match deserialize_json_body(
        result,
        &("/snippets/:id/requires_user_action_reason", Method::PATCH),
    ) {
        Ok(payload) => state.db_handler.lock().map_or_else(
            |e| {
                lock_db_handler_error::<String>(
                    Some(&e),
                    "patch_snippet_requires_user_action_reason",
                )
            },
            |mut db_handler| {
                db_handler
                    .set_snippet_requires_user_action_reason(id, &payload);
                (StatusCode::OK, String::new())
            },
        ),
        Err(err) => err,
    }
}

#[allow(clippy::unused_async)]
pub async fn patch_snippet_parent_directory(
    State(state): State<RouteState>,
    Path(id): Path<usize>,
    result: Result<Json<usize>, JsonRejection>,
) -> (StatusCode, String) {
    match deserialize_json_body(result, &("/snippets/:id/move", Method::PATCH))
    {
        Ok(payload) => state.db_handler.lock().map_or_else(
            |e| {
                lock_db_handler_error::<String>(
                    Some(&e),
                    "patch_snippet_parent_directory",
                )
            },
            |mut db_handler| {
                db_handler.move_snippet_to_directory(id, payload);
                (StatusCode::OK, String::new())
            },
        ),
        Err(err) => err,
    }
}

#[allow(clippy::unused_async)]
pub async fn patch_snippet_directory_parent_directory(
    State(state): State<RouteState>,
    Path(id): Path<usize>,
    result: Result<Json<usize>, JsonRejection>,
) -> (StatusCode, String) {
    match deserialize_json_body(
        result,
        &("/snippets_dir/:id/move", Method::PATCH),
    ) {
        Ok(payload) => state.db_handler.lock().map_or_else(
            |e| {
                lock_db_handler_error::<String>(
                    Some(&e),
                    "patch_snippet_directory_parent_directory",
                )
            },
            |mut db_handler| {
                db_handler.move_snippet_directory_to_directory(id, payload);
                (StatusCode::OK, String::new())
            },
        ),
        Err(err) => err,
    }
}

#[allow(clippy::unused_async)]
pub async fn request_snippet_state_reset(
    State(state): State<RouteState>,
) -> StatusCode {
    state.db_handler.lock().map_or(
        StatusCode::INTERNAL_SERVER_ERROR,
        |mut db_handler| {
            db_handler.request_snippet_state_reset();
            StatusCode::OK
        },
    )
}

#[allow(clippy::unused_async)]
pub async fn gdtfs_from_filesystem(
    State(_state): State<RouteState>,
) -> (StatusCode, Json<Vec<Gdtf>>) {
    // if let Ok(db_handler) = state.db_handler.lock() {
    //     let gdtfs = db_handler.gdtfs_from_filesystem();
    //     (StatusCode::OK, Json(gdtfs))
    // } else {
    //     lock_db_handler_error::<Json<Vec<Gdtf>>>()
    // }
    (StatusCode::NOT_IMPLEMENTED, Json(vec![]))
}
#[allow(clippy::unused_async)]
pub async fn add_gdtf_to_library(_: Multipart) -> (StatusCode, String) {
    // while let Ok(Some(field)) = multipart.next_field().await {
    //     let file_name = if let Some(file_name) = field.file_name() {
    //         file_name.to_owned()
    //     } else {
    //         continue;
    //     };
    //     if gdtf_stream_to_file(&file_name, field).await.is_err() {
    //         logging::log(
    //             "Unable to save gdtf file to the filesystem".to_string(),
    //             logging::LogLevel::Warning,
    //             true,
    //         );
    //         return (
    //             StatusCode::INTERNAL_SERVER_ERROR,
    //             "Unable to store file on server".to_owned(),
    //         );
    //     };
    // }
    (StatusCode::NOT_IMPLEMENTED, String::new())
}
#[allow(clippy::unused_async)]
pub async fn get_blueprints(
    State(state): State<RouteState>,
) -> (StatusCode, Json<Vec<BlueprintFileDescriptor>>) {
    state.db_handler.lock().map_or_else(
        |e| {
            lock_db_handler_error::<Json<Vec<BlueprintFileDescriptor>>>(
                Some(&e),
                "get_blueprints",
            )
        },
        |db_handler| {
            (
                StatusCode::OK,
                Json(db_handler.active_show.blueprints.clone()),
            )
        },
    )
}
#[allow(clippy::unused_async)]
pub async fn post_blueprint(
    State(state): State<RouteState>,
) -> (StatusCode, String) {
    state.db_handler.lock().map_or_else(
        |e| lock_db_handler_error::<String>(Some(&e), "post_blueprint"),
        |mut db_handler| {
            db_handler.create_default_blueprint_in_active_show();
            (StatusCode::OK, String::new())
        },
    )
}
#[allow(clippy::unused_async)]
pub async fn delete_blueprint(
    State(state): State<RouteState>,
    Path(id): Path<usize>,
) -> StatusCode {
    state.db_handler.lock().map_or(
        StatusCode::INTERNAL_SERVER_ERROR,
        |mut db_handler| {
            db_handler.delete_blueprint(id);
            StatusCode::OK
        },
    )
}
#[allow(clippy::unused_async)]
pub async fn patch_blueprint(
    State(state): State<RouteState>,
    result: Result<Json<BlueprintFileDescriptor>, JsonRejection>,
) -> (StatusCode, String) {
    match deserialize_json_body(result, &("/blueprint", Method::POST)) {
        Ok(payload) => state.db_handler.lock().map_or_else(
            |e| lock_db_handler_error::<String>(Some(&e), "patch_blueprint"),
            |mut db_handler| {
                db_handler.update_blueprint(&payload);
                (StatusCode::OK, String::new())
            },
        ),
        Err(err) => err,
    }
}
#[allow(clippy::unused_async)]
pub async fn get_timecodes(
    State(state): State<RouteState>,
) -> (StatusCode, Json<Vec<TimecodeFileDescriptor>>) {
    state.db_handler.lock().map_or_else(
        |e| {
            lock_db_handler_error::<Json<Vec<TimecodeFileDescriptor>>>(
                Some(&e),
                "get_timecodes",
            )
        },
        |db_handler| {
            (
                StatusCode::OK,
                Json(db_handler.active_show.timecodes.clone()),
            )
        },
    )
}
#[allow(clippy::unused_async)]
pub async fn post_timecode(
    State(state): State<RouteState>,
) -> (StatusCode, String) {
    state.db_handler.lock().map_or_else(
        |e| lock_db_handler_error::<String>(Some(&e), "post_timecode"),
        |mut db_handler| {
            db_handler.create_default_timecode_in_active_show();
            (StatusCode::OK, String::new())
        },
    )
}
#[allow(clippy::unused_async)]
pub async fn delete_timecode(
    State(state): State<RouteState>,
    Path(id): Path<usize>,
) -> StatusCode {
    state.db_handler.lock().map_or(
        StatusCode::INTERNAL_SERVER_ERROR,
        |mut db_handler| {
            db_handler.delete_timecode(id);
            StatusCode::OK
        },
    )
}
#[allow(clippy::unused_async)]
pub async fn patch_timecode(
    State(state): State<RouteState>,
    result: Result<Json<TimecodeFileDescriptor>, JsonRejection>,
) -> (StatusCode, String) {
    match deserialize_json_body(result, &("/timecode", Method::POST)) {
        Ok(payload) => state.db_handler.lock().map_or_else(
            |e| lock_db_handler_error::<String>(Some(&e), "patch_timecode"),
            |mut db_handler| {
                db_handler.update_timecode(&payload);
                (StatusCode::OK, String::new())
            },
        ),
        Err(err) => err,
    }
}
#[allow(clippy::unused_async)]
pub async fn get_available_show_names_with_active_hint(
    State(state): State<RouteState>,
) -> (StatusCode, Json<Vec<(usize, String, bool)>>) {
    state.db_handler.lock().map_or_else(
        |e| {
            lock_db_handler_error(
                Some(&e),
                "get_available_show_names_with_active_hint",
            )
        },
        |mut db_handler| {
            (
                StatusCode::OK,
                Json(db_handler.get_available_show_names_with_active_hint()),
            )
        },
    )
}
#[allow(clippy::unused_async)]
pub async fn set_active_show(
    State(state): State<RouteState>,
    Path(id): Path<usize>,
) -> (StatusCode, String) {
    state.db_handler.lock().map_or_else(
        |e| lock_db_handler_error(Some(&e), "set_active_show"),
        |mut db_handler| {
            if let Ok(mut dmx_renderer) = state.dmx_renderer.lock() {
                dmx_renderer.reset_to_defaults();
            }
            if let Ok(mut input_parser) = state.input_parser.lock() {
                input_parser.reset_to_defaults();
            }
            db_handler.switch_show_to(id);
            (StatusCode::OK, String::new())
        },
    )
}
#[derive(Debug, Deserialize)]
pub struct RenameShowQuery {
    new_name: String,
}
#[allow(clippy::unused_async)]
pub async fn rename_show(
    State(state): State<RouteState>,
    Path(id): Path<usize>,
    Query(rename_show_query): Query<RenameShowQuery>,
) -> StatusCode {
    if let Ok(mut db_handler) = state.db_handler.lock() {
        db_handler.rename_show(id, &rename_show_query.new_name);
        StatusCode::OK
    } else {
        StatusCode::INTERNAL_SERVER_ERROR
    }
}
#[allow(clippy::unused_async)]
pub async fn get_show(
    State(state): State<RouteState>,
    Path(id): Path<usize>,
) -> (StatusCode, Json<String>) {
    if let Ok(mut db_handler) = state.db_handler.lock() {
        let show = db_handler.get_show(id);
        if let Ok(serialized_show) = serde_json::to_string(show) {
            return (StatusCode::OK, Json(serialized_show));
        }
        return (StatusCode::INTERNAL_SERVER_ERROR, Json(String::new()));
    }
    lock_db_handler_error(None, "get_show")
}
#[allow(clippy::unused_async)]
pub async fn post_show(
    State(state): State<RouteState>,
    result: Result<Json<String>, JsonRejection>,
) -> (StatusCode, String) {
    match deserialize_json_body(result, &("/show", Method::POST)) {
        Ok(payload) => state.db_handler.lock().map_or_else(
            |e| lock_db_handler_error::<String>(Some(&e), "post_show"),
            |mut db_handler| {
                if let Ok(mut dmx_renderer) = state.dmx_renderer.lock() {
                    dmx_renderer.reset_to_defaults();
                }
                if let Ok(mut input_parser) = state.input_parser.lock() {
                    input_parser.reset_to_defaults();
                }
                db_handler.create_new_show(&payload);
                (StatusCode::OK, String::new())
            },
        ),
        Err(err) => err,
    }
}
#[allow(clippy::unused_async)]
pub async fn upload_show(
    State(state): State<RouteState>,
    result: Result<Json<Show>, JsonRejection>,
) -> (StatusCode, String) {
    match deserialize_json_body(result, &("/show", Method::POST)) {
        Ok(payload) => state.db_handler.lock().map_or_else(
            |e| lock_db_handler_error::<String>(Some(&e), "upload_show"),
            |mut db_handler| {
                if let Ok(mut dmx_renderer) = state.dmx_renderer.lock() {
                    dmx_renderer.reset_to_defaults();
                }
                if let Ok(mut input_parser) = state.input_parser.lock() {
                    input_parser.reset_to_defaults();
                }
                db_handler.upload_show(&payload);
                (StatusCode::OK, String::new())
            },
        ),
        Err(err) => err,
    }
}

#[allow(clippy::unused_async)]
pub async fn upload_snippet(
    State(state): State<RouteState>,
    Path(id): Path<usize>,
    mut multipart: Multipart,
) -> (StatusCode, Json<String>) {
    while let Ok(next_field) = multipart.next_field().await {
        if let Some(field_content) = next_field {
            if let Some(field_name) = field_content.name() {
                if field_name != "snippet" {
                    continue;
                }

                if let Ok(data) = field_content.bytes().await {
                    let Ok(json_str) = String::from_utf8(data.to_vec()) else {
                        return (
                            StatusCode::UNPROCESSABLE_ENTITY,
                            Json("Invalid UTF-8 content".to_string()),
                        );
                    };

                    let snippet: Snippet = match serde_json::from_str(&json_str)
                    {
                        Ok(s) => s,
                        Err(err) => {
                            logging::log(
                                format!(
                                    "Failed to parse snippet JSON: {err:?}"
                                ),
                                logging::LogLevel::Warning,
                                false,
                            );
                            return (
                                StatusCode::UNPROCESSABLE_ENTITY,
                                Json(format!("Invalid snippet JSON: {err}")),
                            );
                        }
                    };

                    if let Ok(mut db_handler) = state.db_handler.lock() {
                        let snippet_name = snippet.name.clone();
                        match db_handler.create_snippet_with_data(snippet, id) {
                            Some(_) => {
                                return (
                                    StatusCode::CREATED,
                                    Json(format!(
                                        "Snippet '{snippet_name}' uploaded successfully"
                                    )),
                                );
                            }
                            None => {
                                return (
                                    StatusCode::INTERNAL_SERVER_ERROR,
                                    Json(
                                        "Failed to create snippet".to_string(),
                                    ),
                                );
                            }
                        }
                    }
                    return (
                        StatusCode::INTERNAL_SERVER_ERROR,
                        Json("Database connection error".to_string()),
                    );
                }
                return (
                    StatusCode::UNPROCESSABLE_ENTITY,
                    Json("Invalid file content".to_string()),
                );
            }
        }
    }
    (
        StatusCode::NOT_ACCEPTABLE,
        Json("No valid snippet file found".to_string()),
    )
}

#[allow(clippy::unused_async)]
pub async fn delete_show(
    State(state): State<RouteState>,
    Path(id): Path<usize>,
) -> StatusCode {
    state.db_handler.lock().map_or(
        StatusCode::INTERNAL_SERVER_ERROR,
        |mut db_handler| {
            db_handler.delete_show(id);
            StatusCode::OK
        },
    )
}

#[allow(clippy::unused_async)]
pub async fn get_variable(
    State(state): State<RouteState>,
    Path(name): Path<String>,
) -> (StatusCode, Json<Option<u8>>) {
    state.input_parser.lock().map_or_else(
        |e| {
            logging::log(
                format!("Could not lock input_parser for get_variable: {e:?}"),
                logging::LogLevel::Warning,
                true,
            );
            (StatusCode::INTERNAL_SERVER_ERROR, Json(None))
        },
        |input_parser| {
            input_parser
                .get_variable(&name)
                .map_or((StatusCode::NOT_FOUND, Json(None)), |variable| {
                    (StatusCode::OK, Json(Some(variable.value)))
                })
        },
    )
}

#[allow(clippy::unused_async)]
pub async fn patch_variable(
    State(state): State<RouteState>,
    Path(name): Path<String>,
    result: Result<Json<u8>, JsonRejection>,
) -> (StatusCode, String) {
    match deserialize_json_body(result, &("/variables/:name", Method::PATCH)) {
        Ok(payload) => state.input_parser.lock().map_or_else(
            |e| {
                logging::log(
                    format!(
                        "Could not lock input_parser for patch_variable: {e:?}"
                    ),
                    logging::LogLevel::Warning,
                    true,
                );
                (
                    StatusCode::INTERNAL_SERVER_ERROR,
                    "Internal server error".to_string(),
                )
            },
            |mut input_parser| match input_parser.get_variable_mut(&name) {
                Some(variable) => {
                    variable.value = payload;
                    (StatusCode::OK, String::new())
                }
                None => {
                    (StatusCode::NOT_FOUND, "Variable not found".to_string())
                }
            },
        ),
        Err(err) => err,
    }
}

#[allow(clippy::unused_async)]
pub async fn get_fixturegroups(
    State(state): State<RouteState>,
) -> (StatusCode, Json<Vec<FixtureGroup>>) {
    state.db_handler.lock().map_or_else(
        |e| {
            lock_db_handler_error::<Json<Vec<FixtureGroup>>>(
                Some(&e),
                "get_fixturegroups",
            )
        },
        |db_handler| {
            (
                StatusCode::OK,
                Json(db_handler.active_show.enriched_groups()),
            )
        },
    )
}

#[allow(clippy::unused_async)]
pub async fn post_fixturegroup(
    State(state): State<RouteState>,
    result: Result<Json<FixtureGroup>, JsonRejection>,
) -> (StatusCode, String) {
    match deserialize_json_body(result, &("/fixturegroup", Method::POST)) {
        Ok(payload) => state.db_handler.lock().map_or_else(
            |e| lock_db_handler_error::<String>(Some(&e), "post_fixturegroup"),
            |mut db_handler| {
                db_handler.create_fixturegroup(&payload);
                (StatusCode::OK, String::new())
            },
        ),
        Err(err) => err,
    }
}

#[allow(clippy::unused_async)]
pub async fn patch_fixturegroup(
    State(state): State<RouteState>,
    result: Result<Json<FixtureGroup>, JsonRejection>,
) -> (StatusCode, String) {
    match deserialize_json_body(result, &("/fixturegroup", Method::PATCH)) {
        Ok(payload) => state.db_handler.lock().map_or_else(
            |e| lock_db_handler_error::<String>(Some(&e), "patch_fixturegroup"),
            |mut db_handler| {
                db_handler.update_fixturegroup(&payload);
                (StatusCode::OK, String::new())
            },
        ),
        Err(err) => err,
    }
}

#[allow(clippy::unused_async)]
pub async fn delete_fixturegroup(
    State(state): State<RouteState>,
    Path(id): Path<usize>,
) -> (StatusCode, String) {
    let Ok(mut db_handler) = state.db_handler.lock() else {
        return lock_db_handler_error::<String>(None, "delete_fixturegroup");
    };
    db_handler.delete_fixturegroup(id);
    (StatusCode::OK, String::new())
}

#[allow(clippy::unused_async)]
pub async fn get_fixture_types(
    State(state): State<RouteState>,
) -> (
    StatusCode,
    Json<Vec<crate::database_handler::fixture_types::FixtureType>>,
) {
    state.db_handler.lock().map_or_else(
        |e| {
            lock_db_handler_error::<
                Json<Vec<crate::database_handler::fixture_types::FixtureType>>,
            >(Some(&e), "get_fixture_types")
        },
        |mut db_handler| {
            let fixture_types = db_handler.fixture_types();
            (StatusCode::OK, Json(fixture_types))
        },
    )
}

pub async fn post_fixture_type(
    State(state): State<RouteState>,
    result: Result<
        Json<crate::database_handler::fixture_types::FixtureType>,
        JsonRejection,
    >,
) -> (StatusCode, String) {
    match deserialize_json_body(result, &("/fixture-type", Method::POST)) {
        Ok(payload) => state.db_handler.lock().map_or_else(
            |e| lock_db_handler_error::<String>(Some(&e), "post_fixture_type"),
            |mut db_handler| {
                db_handler.create_fixture_type(&payload);
                (StatusCode::OK, String::new())
            },
        ),
        Err(status_code) => status_code,
    }
}
pub async fn instanciate_fixture_type(
    State(state): State<RouteState>,
    result: Result<
        Json<crate::database_handler::fixture_types::FixtureType>,
        JsonRejection,
    >,
) -> (StatusCode, String) {
    match deserialize_json_body(result, &("/fixture-type", Method::POST)) {
        Ok(payload) => state.db_handler.lock().map_or_else(
            |e| lock_db_handler_error::<String>(Some(&e), "post_fixture_type"),
            |mut db_handler| {
                db_handler.create_fixture_type(&payload);
                (StatusCode::OK, String::new())
            },
        ),
        Err(status_code) => status_code,
    }
}

pub async fn patch_fixture_type(
    State(state): State<RouteState>,
    result: Result<
        Json<crate::database_handler::fixture_types::FixtureType>,
        JsonRejection,
    >,
) -> (StatusCode, String) {
    match deserialize_json_body(result, &("/fixture-type", Method::PATCH)) {
        Ok(payload) => state.db_handler.lock().map_or_else(
            |e| lock_db_handler_error::<String>(Some(&e), "patch_fixture_type"),
            |mut db_handler| {
                db_handler.update_fixture_type(&payload);
                (StatusCode::OK, String::new())
            },
        ),
        Err(status_code) => status_code,
    }
}

pub async fn delete_fixture_type(
    State(state): State<RouteState>,
    Path(id): Path<usize>,
) -> (StatusCode, String) {
    let Ok(mut db_handler) = state.db_handler.lock() else {
        return lock_db_handler_error::<String>(None, "delete_fixture_type");
    };
    db_handler.delete_fixture_type(id);
    (StatusCode::OK, String::new())
}
