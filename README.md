# Ruhige-Waldgeräusche is a lightcontroller with the following features

- Completely abstracting dmx, so that the user can concentrate on setting `pan`, not `channel 17 on universe 3`.
- Exposing a webinterface to configure the stagesetup, fixtures and keymappings. (Timecodeshows comming soon)
- Creating the possibility of custom keyboard layouts called `modules` with keys, faders and led indicating backlight.
- Detecting the `beat` on the network which is send out from `pioneer cdjs` to sync the show to the song.
- Sending dmx via `ArtNet` to address up to `65.535 dmx universes`.

## Using

Downloading assets via the terminal on linux: `rm host && curl -vLJO -H 'Authorization: token <TOKEN>' 'https://github.com/JonasFocke01/ruhige_waldgeraeusche/releases/download/<VERSION>/rw_host_<VERSION>_aarch64' && mv rw_host_<VERSION>_aarch64 host && chmod +x host`


When you successfully buid the project, the backend is allready sending artnet. It can be seen on a connected `ArtNet` node in the same network, or in a visualizer like `Unreal Engine` on **another** computer. The documentation and tutorials to use this are currently in development.

## Building

This project contains multiple sub-projects.
The backbone is the backend written in rust, so you can use `cargo` to run it.
The interface you need to interact with is written in js and can be run via `node`. Go to the webinterface subdirectory, build it with `npm run build` and run it with `node build`. The interface can now be accessed via `your.local.ip.address:3000`.

If you want to program in the webinterface, you need to generate the ts types from the backend.
You can simply do that by running `webinterface/src/lib/types/generate_new_types_from_host.sh`. (In the same directory)

## Structure

The code for the backend, that handles `ArtNet` output, `rwm`(rw-modules) input, webinterface api, ... lives in `host/`.
The frontend, with which you can create fixtures, edit configurations and timecode shows, lives in the `webinterface/` directory. This is a sveltekit-js project.
The controller keyboard modules live in `modules/`. They are written in `arduino/c++` and `rust`.

## Contributing

Any contributing is highly appreciated!
Feel free to raise pull requests or simply write ideas as an issue.

## Note to myself

To create a release, just create a release branch `number.number.number`, and trigger the release pipeline manually on that branch
