<script lang="ts">
    import { toasts, type ToastType } from "$lib/stores/toast";
    import { fade, fly } from "svelte/transition";

    function getToastClasses(type: ToastType): string {
        const baseClasses =
            "rounded-lg border p-4 shadow-lg flex items-center justify-between min-w-80 max-w-sm";

        switch (type) {
            case "success":
                return `${baseClasses} text-primary border-input bg-success`;
            case "error":
                return `${baseClasses} text-primary border-input bg-error`;
            case "warning":
                return `${baseClasses} text-primary border-input bg-warning`;
            case "info":
                return `${baseClasses} text-primary border-input bg-info`;
            default:
                return `${baseClasses} text-primary border-input bg-primary`;
        }
    }

    function toastIcon(toastType: ToastType): string {
        switch (toastType) {
            case "success":
                return "✓";
            case "error":
                return "✕";
            case "warning":
                return "⚠";
            case "info":
                return "ℹ";
            default:
                return "ℹ";
        }
    }
</script>

<div class="fixed top-4 right-4 z-50 space-y-2">
    {#each $toasts as toast (toast.id)}
        <div
            class={getToastClasses(toast.type)}
            in:fly={{ x: 300, duration: 300 }}
            out:fade={{ duration: 200 }}
        >
            <div class="flex items-center space-x-3">
                <span class="text-lg font-bold" aria-hidden="true">
                    {toastIcon(toast.type)}
                </span>
                <span class="text-sm font-medium">
                    {toast.message}
                </span>
            </div>
            <button
                class="ml-4 text-lg opacity-70 hover:opacity-100 focus:outline-none"
                onclick={() => toasts.remove(toast.id)}
                aria-label="Close notification"
            >
                ×
            </button>
        </div>
    {/each}
</div>

