#![no_std]

pub const HEAD_1: u8 = 122;
pub const HEAD_2: u8 = 127;
pub const HEAD_3: u8 = 115;

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Debug, PartialEq, Eq)]
pub struct RawInput {
    pub id: u16,
    pub value: u8,
}

impl From<[u8; 3]> for RawInput {
    #[allow(clippy::arithmetic_side_effects)]
    fn from(value: [u8; 3]) -> Self {
        let mut id: u16 = 0;
        id += u16::from(value[1]);
        id <<= 8;
        id += u16::from(value[0]);
        RawInput {
            id,
            value: value[2],
        }
    }
}

impl From<RawInput> for [u8; 3] {
    fn from(value: RawInput) -> Self {
        [
            value.id.to_le_bytes()[0],
            value.id.to_le_bytes()[1],
            value.value,
        ]
    }
}

#[allow(dead_code)]
#[derive(<PERSON><PERSON><PERSON>, Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>q, Eq)]
pub struct SetLed {
    id: u16,
    red: u8,
    green: u8,
    blue: u8,
}

#[derive(Debu<PERSON>, <PERSON><PERSON>, PartialEq, Eq)]
pub enum RwModule {
    Color,
    // Tomahawk1,
    Tomahawk1,
    // Tomahawk2,
    Tomahawk2,
    Unknown,
}
impl From<u8> for RwModule {
    fn from(value: u8) -> Self {
        match value {
            0 => Self::Color,
            1 => Self::Tomahawk1,
            2 => Self::Tomahawk2,
            _ => Self::Unknown,
        }
    }
}

#[allow(dead_code)]
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum RwModuleUsbPackage {
    /// module -> host
    ModuleIdentifier(RwModule),
    /// host -> module
    IdentifyRequest,
    /// module -> host: Button / Fader was set to x
    Input(RawInput),
    /// host -> module: Instruction from host. holds RGB
    SetLed(SetLed),
    /// host -> module: Instruction from host
    SetFader(RawInput),
    /// host -> module: If modules do not receive this after sending a package to the host, they
    /// resend it after a short delay
    Acknowledgment,
    /// module -> host: The info that this module has paniced
    Panic(RwModule),
    /// host -> module
    AreYouAlive,
    /// module -> host
    ImAlive,
    None,
}

impl From<RwModuleUsbPackage> for u8 {
    fn from(value: RwModuleUsbPackage) -> Self {
        match value {
            RwModuleUsbPackage::ModuleIdentifier(_) => 0,
            RwModuleUsbPackage::IdentifyRequest => 1,
            RwModuleUsbPackage::Input(_) => 2,
            RwModuleUsbPackage::SetLed(_) => 3,
            RwModuleUsbPackage::SetFader(_) => 4,
            RwModuleUsbPackage::Acknowledgment => 5,
            RwModuleUsbPackage::AreYouAlive => 6,
            RwModuleUsbPackage::ImAlive => 7,
            RwModuleUsbPackage::Panic(_) => 255,
            RwModuleUsbPackage::None => 254,
        }
    }
}

#[allow(clippy::too_many_lines)]
impl From<(usize, &[u8])> for RwModuleUsbPackage {
    fn from(value: (usize, &[u8])) -> Self {
        let length = value.0;
        let value = value.1;
        if value.len() < 4 {
            return Self::None;
        }
        let Some(package_start) = value.iter().position(|x| *x == HEAD_1) else {
            return Self::None;
        };
        let length_read = length.saturating_sub(package_start);
        let Some(sliced_buffer) = value.get(package_start..) else {
            return Self::None;
        };
        let mut sliced_buffer = sliced_buffer.iter();
        if let Some(n) = sliced_buffer.next() {
            if *n != HEAD_1 {
                return Self::None;
            }
        }
        if let Some(n) = sliced_buffer.next() {
            if *n != HEAD_2 {
                return Self::None;
            }
        }
        if let Some(n) = sliced_buffer.next() {
            if *n != HEAD_3 {
                return Self::None;
            }
        }

        let package_type = sliced_buffer.next().copied();
        if package_type == Some(RwModuleUsbPackage::ModuleIdentifier(255.into()).into()) {
            if length_read < 5 {
                return Self::None;
            }
            if let Some(module_type) = sliced_buffer.next() {
                Self::ModuleIdentifier((*module_type).into())
            } else {
                Self::None
            }
        } else if package_type == Some(RwModuleUsbPackage::IdentifyRequest.into()) {
            Self::IdentifyRequest
        } else if package_type == Some(RwModuleUsbPackage::Input(RawInput::default()).into()) {
            if length_read < 7 {
                return Self::None;
            }
            let Some(lo_addr) = sliced_buffer.next() else {
                return Self::None;
            };
            let Some(hi_addr) = sliced_buffer.next() else {
                return Self::None;
            };
            let Some(input_value) = sliced_buffer.next() else {
                return Self::None;
            };
            let mut result_addr: u16 = 0;
            result_addr = u16::from(*hi_addr).saturating_add(result_addr);
            result_addr <<= 8;
            result_addr = u16::from(*lo_addr).saturating_add(result_addr);
            Self::Input(RawInput {
                id: result_addr,
                value: *input_value,
            })
        } else if package_type == Some(Self::SetLed(SetLed::default()).into()) {
            if length_read < 9 {
                return Self::None;
            }
            let Some(lo_addr) = sliced_buffer.next() else {
                return Self::None;
            };
            let Some(hi_addr) = sliced_buffer.next() else {
                return Self::None;
            };
            let mut result_addr: u16 = 0;
            result_addr = u16::from(*hi_addr).saturating_add(result_addr);
            result_addr <<= 8;
            result_addr = u16::from(*lo_addr).saturating_add(result_addr);
            let Some(red) = sliced_buffer.next() else {
                return Self::None;
            };
            let Some(green) = sliced_buffer.next() else {
                return Self::None;
            };
            let Some(blue) = sliced_buffer.next() else {
                return Self::None;
            };
            Self::SetLed(SetLed {
                id: result_addr,
                red: *red,
                green: *green,
                blue: *blue,
            })
        } else if package_type == Some(Self::SetFader(RawInput::default()).into()) {
            if length_read < 7 {
                return Self::None;
            }
            let Some(lo_addr) = sliced_buffer.next() else {
                return Self::None;
            };
            let Some(hi_addr) = sliced_buffer.next() else {
                return Self::None;
            };
            let mut result_addr: u16 = 0;
            result_addr = u16::from(*hi_addr).saturating_add(result_addr);
            result_addr <<= 8;
            result_addr = u16::from(*lo_addr).saturating_add(result_addr);
            let Some(set_value) = sliced_buffer.next() else {
                return Self::None;
            };
            Self::SetFader(RawInput {
                id: result_addr,
                value: *set_value,
            })
        } else if package_type == Some(Self::Acknowledgment.into()) {
            Self::Acknowledgment
        } else if package_type == Some(Self::AreYouAlive.into()) {
            Self::AreYouAlive
        } else if package_type == Some(Self::ImAlive.into()) {
            Self::ImAlive
        } else if package_type == Some(Self::Panic(255.into()).into()) {
            if length_read < 5 {
                return Self::Panic(RwModule::Unknown);
            }
            if let Some(module_type) = sliced_buffer.next() {
                Self::Panic((*module_type).into())
            } else {
                Self::Panic(RwModule::Unknown)
            }
        } else {
            Self::None
        }
    }
}
