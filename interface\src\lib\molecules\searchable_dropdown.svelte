<script lang="ts" module>
    export interface DropdownItem {
        name: string;
        id: number | string;
        requires_user_action_reason?: string | null;
        readonly?: boolean;
    }
</script>

<script lang="ts">
    import Textinput from "$lib/atoms/textinput.svelte";
    import Icon from "$lib/atoms/icon.svelte";
    import Button from "$lib/atoms/button.svelte";
    import ClickOutside from "svelte-click-outside";
    import RequiresUseractionHint from "$lib/atoms/RequiresUseractionHint.svelte";

    let isOpen = $state(false);

    let {
        id,
        value = $bindable(),
        items,
        disabled = false,
    }: {
        id: string;
        value: string | number | undefined | null;
        items: DropdownItem[];
        disabled?: boolean;
    } = $props();

    let filteredItems = $derived(
        items.filter((item) =>
            item.name.toLowerCase().includes(textSearchValue.toLowerCase()),
        ),
    );

    let maxItemLength = $derived.by(() => {
        if (items.length > 0) {
            return items
                .map((item) => item.name)
                .sort((a, b) => a.length - b.length)
                .slice(-1)[0].length;
        } else {
            return 1;
        }
    });

    let placeholderString = $derived("-".repeat(maxItemLength));

    let textSearchValue = $state("");
</script>

<!-- <ClickOutside onClickOutside={() => (isOpen = false)}> -->
<div class="flex items-center justify-center">
    <div
        class="group relative w-full"
        style="min-width: {maxItemLength * 13}px;"
    >
        <Button {id} onclick={() => (!disabled ? (isOpen = !isOpen) : null)}>
            <div class="flex" class:text-disabled={disabled}>
                <span class="mr-2"
                    >{items.find((item) => item.id === value)?.name ??
                        placeholderString}</span
                >
                <div class="ml-auto mt-1">
                    <div
                        class="transition"
                        class:rotate-90={!isOpen}
                        class:-rotate-90={isOpen}
                    >
                        <Icon icon="weui:arrow-filled"></Icon>
                    </div>
                </div>
            </div>
        </Button>
        {#if isOpen}
            <div
                id="dropdown-menu"
                class="absolute z-10 mt-2 space-y-1 rounded-md bg-input p-2 shadow-lg ring-1 ring-black ring-opacity-5"
            >
                <Textinput bind:value={textSearchValue} placeholder="search"
                ></Textinput>
                <div class="max-h-32 overflow-y-scroll p-1">
                    {#each filteredItems as item}
                        <RequiresUseractionHint
                            reason={item.requires_user_action_reason}
                        >
                            <button
                                class="w-full rounded-md p-1 transition-colors hover:bg-accent flex items-center justify-between"
                                class:opacity-60={item.readonly}
                                onclick={() => {
                                    value = item.id;
                                    isOpen = false;
                                }}
                            >
                                <span>{item.name}</span>
                                {#if item.readonly}
                                    <span class="text-xs text-gray-400 ml-2"
                                        >(read-only)</span
                                    >
                                {/if}
                            </button>
                        </RequiresUseractionHint>
                    {/each}
                </div>
            </div>
        {/if}
    </div>
</div>
<!-- </ClickOutside> -->
