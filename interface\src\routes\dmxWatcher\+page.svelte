<script lang="ts">
    import { fetchDmxOutput } from "$lib/utils";
    import Loadingspinner from "$lib/atoms/Loadingspinner.svelte";
    import Card from "../dashboard/partials/card.svelte";
    import TimeAgo from "javascript-time-ago";
    import en from "javascript-time-ago/locale/en";
    import LabeledNumberinput from "$lib/molecules/labeled_numberinput.svelte";

    let dmxChannels: number[] = $state([]);
    let isLoading = $state(true);
    let updateInterval: ReturnType<typeof setInterval> | undefined = $state();

    let universeIndex = $state(1);
    let loadingIndicatorTimeout: NodeJS.Timeout | undefined = $state();
    let lastResponseTimestamp: Date | undefined = $state();

    async function fetchDmxData() {
        isLoading = true;
        dmxChannels = await fetchDmxOutput();
        lastResponseTimestamp = new Date();
        if (loadingIndicatorTimeout) {
            clearTimeout(loadingIndicatorTimeout);
        }
        loadingIndicatorTimeout = setTimeout(() => (isLoading = false), 1000);
    }

    TimeAgo.addDefaultLocale(en);
    let timeAgo: TimeAgo | undefined = $state();

    $effect(() => {
        timeAgo = new TimeAgo("en-US");

        fetchDmxData();

        updateInterval = setInterval(fetchDmxData, 250);

        return () => {
            if (updateInterval !== undefined) {
                clearInterval(updateInterval);
            }
        };
    });
</script>

<div class="max-w-4xl mx-auto">
    <Card>
        <div class="p-2">
            <h1 class="text-3xl font-bold mb-6 text-primary">DMX Watcher</h1>

            {#if isLoading}
                <div class="flex">
                    <div class="text-center pr-2">
                        <Loadingspinner />
                    </div>
                    {#if lastResponseTimestamp && timeAgo}
                        Last frame-response {timeAgo.format(
                            lastResponseTimestamp,
                        )}
                    {/if}
                </div>
            {/if}

            <LabeledNumberinput
                label="Universe"
                bind:value={universeIndex}
                min={1}
                max={65535}
            ></LabeledNumberinput>
        </div>
    </Card>
    {#if dmxChannels.length > 0}
        <div class="grid grid-cols-5 gap-4 mt-2">
            {#each dmxChannels as channelValue, index}
                {@const channelNumber = index + 1}
                {@const percentage = Math.round((channelValue / 255) * 100)}
                <div class="bg-surface p-4 rounded-lg border">
                    <div class="text-center mb-2">
                        <div class="text-sm font-semibold text-primary/70">
                            CH {channelNumber}
                        </div>
                        <div class="text-2xl font-bold text-primary">
                            {channelValue}
                        </div>
                    </div>
                    <div
                        class="w-full bg-primary/20 rounded-full h-2 overflow-hidden"
                    >
                        <div
                            class="bg-accent h-full transition-all duration-75 ease-linear"
                            style="width: {percentage}%"
                        ></div>
                    </div>
                </div>
            {/each}
        </div>
    {/if}
</div>
